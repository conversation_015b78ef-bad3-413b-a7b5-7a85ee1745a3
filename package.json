{"name": "external-system-demo", "version": "1.0.0", "description": "A Vue.js project", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "devDependencies": {"@ant-design/icons-vue": "^7.0.1", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "ant-design-vue": "4.0.5", "axios": "^0.21.0", "babel-eslint": "^10.1.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "less": "^3.13.1", "less-loader": "^11.1.3", "postcss": "^8.4.32", "sass": "^1.57.1", "view-ui-plus": "^1.3.14", "vue": "3.3.4", "vue-ls": "^3.2.1", "vue-router": "^4.2.4", "vue-select": "^4.0.0-beta.6", "vuex": "^4.1.0", "vxe-table": "4.5.13", "yao-import": "3.2.6", "ych-components": "^333.0.20"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "dependencies": {"@surely-vue/table": "^4.3.17", "postcss-import": "^15.1.0", "postcss-url": "^10.1.3", "vue-draggable-next": "^2.2.1", "vue-i18n": "^11.1.1", "vue-multiselect": "^3.2.0", "vue-select": "^4.0.0-beta.6"}}