
import { createApp } from 'vue'

function userSetPlugins(){
  return store => {
    try {
      let tableColumn = window.sessionStorage.getItem('SET_COLUMNTABLE')
      let queryValue = window.sessionStorage.getItem('SET_QUERYVALUE')
      let formColumn = window.sessionStorage.getItem('SET_COLUMNFORM')
      let gwSetting = window.sessionStorage.getItem('SET_GWSETTING')
      if (tableColumn) {
        store.commit('SET_COLUMNTABLE', JSON.parse(tableColumn))
      }
      if (queryValue) {
        store.commit('SET_QUERYVALUE', JSON.parse(queryValue))
      }
      if (formColumn) {
        store.commit('SET_COLUMNFORM', JSON.parse(formColumn))
      }
      if (gwSetting) {
        store.commit('SET_GWSETTING', JSON.parse(gwSetting))
      }
    } catch (e) {
      console.error(e)
    }

    store.subscribe((mutation) => {
      try {
        if (mutation.type === 'SET_COLUMNTABLE') {
          window.sessionStorage.setItem('SET_COLUMNTABLE', JSON.stringify(mutation.payload))
        }
        if (mutation.type === 'SET_QUERYVALUE') {
          window.sessionStorage.setItem('SET_QUERYVALUE', JSON.stringify(mutation.payload))
        }
        if (mutation.type === 'SET_COLUMNFORM') {
          window.sessionStorage.setItem('SET_COLUMNFORM', JSON.stringify(mutation.payload))
        }
        if (mutation.type === 'SET_GWSETTING') {
          window.sessionStorage.setItem('SET_GWSETTING', JSON.stringify(mutation.payload))
        }
      } catch (e) {
        console.error(e)
      }
    })
  }
}
const app = createApp()
const setPlugins = userSetPlugins(app);
export default setPlugins;
