import {getCustomVaueByTypeAndDataId} from "@/api/bi/bi_client_info";


const custom = {
  state: {
    customTableColumns: {},
  },
  mutations:{
    /**
     * @description 存储指定页面表格中 显示列及排序配置
     * @param payload
     * {
     *   key:表格key
     *   val:表格中显示列及排序配置规则
     * }
     */
    SAVE_CUSTOM_TABLE_COLUMNS: (state, payload) => {
      if (payload.key) {
        //Vue.set(state.customTableColumns, payload.key, payload.val);
        state.customTableColumns[payload.key] = payload.val
      }
    },

    /**
     * 只保存vuex,不保存数据库
     * @param state
     * @param payload
     * @constructor
     */
    SAVE_CUSTOM_TABLE_COLUMNS_NODB: (state, payload) => {
      if (payload.key) {
        //Vue.set(state.customTableColumns, payload.key, payload.val);
        state.customTableColumns[payload.key] = payload.val
      }
    },

  },
  actions:{
    /* 通过表格key加载指定页面表格的个性化配置 */
    loadCustomTableColumns({ commit, state }, tableKey) {
      if (!state.customTableColumns[tableKey]) {
        let params = {
          type: 'customColumns',
          dataId: tableKey
        };
        getCustomVaueByTypeAndDataId(params).then(res => {
          if (res.success && res.result.length > 0) {
            //从数据库加载的个性化设置只存vuex，不存数据库
            commit('SAVE_CUSTOM_TABLE_COLUMNS_NODB', { key: tableKey, val: JSON.parse(res.result) });
          } else {
            //没有数据存空对象到vuex，下次不再从数据库加载
            commit('SAVE_CUSTOM_TABLE_COLUMNS_NODB', { key: tableKey, val: [] });
          }
        });
      }
    },
  }
}

export default custom
