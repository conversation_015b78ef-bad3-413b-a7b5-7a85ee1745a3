

import { createApp } from 'vue'
import {saveCustomWithDataId} from "@/api/bi/bi_client_info";

/**
 * @description 提交数据到数据库(观察者模式)
 * @param {vuex.$store对象} store
 */
function createCustomPlugin() {
  return store => {
    store.subscribe((mutation, state) => {
      //保存表格列个性化设置到数据库
      if (mutation.type === 'SAVE_CUSTOM_TABLE_COLUMNS' && mutation.payload.key  && mutation.payload.val) {
        let params = {
          type:'customColumns',
          dataId: mutation.payload.key,
          name:'content',
          value: mutation.payload.val.length<1?"":JSON.stringify(mutation.payload.val)
        };
        saveCustomWithDataId(params);
      }
    });
  };
}
const app = createApp()
const customPlugin = createCustomPlugin(app);
export default customPlugin;
