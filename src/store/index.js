
import * as Vuex from 'vuex'
import getters from './getters'
import custom from './modules/custom'
import setPlugins from "@/store/modules/userSetPlugins";
import customPlugin from "@/store/modules/customPlugin";

export default Vuex.createStore({
  modules: {
    custom,
  },
  state() {
    return {

    }
  },
  mutations: {

  },
  actions: {},
  getters,
  plugins: [customPlugin, setPlugins],
})
