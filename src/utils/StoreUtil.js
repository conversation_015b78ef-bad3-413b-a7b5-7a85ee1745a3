
class StoreUtil {
  constructor () {

  }

  getItem (key) {
    let value = localStorage.getItem(key)
    try {
      return JSON.parse(value)
    } catch (e) {
      console.warn(e)
      return value
    }
  }

  setItem (key, value) {
    localStorage.removeItem(key)
    try {
      if (Object.entries(value).length === 0 && value.constructor === Object) {
        return
      }
      localStorage.setItem(key, JSON.stringify(value))
    } catch (e) {
      console.warn(e)
    }
  }

  removeItem (key) {
    localStorage.removeItem(key)
  }
}

export default new StoreUtil()
