// 防抖
let timer = null
const debounced = (func, delay = 1000) => {
  return (...args) => {
    clearTimeout(timer)
    timer = setTimeout(() => {
      func(...args)
    }, delay)
  }
}

const map = new WeakMap()
const observer = new ResizeObserver((entries) => {
  entries.forEach(entry => {
    const func = map.get(entry.target)
    if (func) {
      const config = {
        width: entry.borderBoxSize[0].inlineSize,
        height: entry.borderBoxSize[0].blockSize,
      }
      debounced(func, 100)(config)
    }
  })
})

export const observe = (el, func) => {
  map.set(el, func)
  observer.observe(el)
}

export const unobserve = (el) => {
  map.delete(el)
  observer.unobserve(el)
}
