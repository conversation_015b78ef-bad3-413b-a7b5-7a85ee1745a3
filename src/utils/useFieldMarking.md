# 表单字段标记功能使用说明

## 功能描述

`useFieldMarking` 是一个Vue 3组合式函数，用于实现表单label点击切换背景色的功能。支持三种状态循环切换：
- 第一次点击：绿色背景
- 第二次点击：红色背景  
- 第三次点击：恢复原色

## 使用方法

### 1. 导入组合式函数

```javascript
import { useFieldMarking } from '@/utils/useFieldMarking'
```

### 2. 在组件中使用

```javascript
// 在 setup() 函数或 <script setup> 中
const {
  fieldMarkings,        // 字段标记状态（响应式对象）
  handleLabelClick,     // 处理label点击事件
  getLabelClass,        // 获取label的CSS类名
  setFieldMarkings,     // 设置字段标记状态
  getFieldMarkings,     // 获取当前字段标记状态
  clearFieldMarkings,   // 清空所有字段标记
  getMarkedFieldsCount  // 获取标记字段的数量
} = useFieldMarking()
```

### 3. 在模板中使用

将原来的表单项：
```vue
<a-form-item name="fieldName" :label="'字段名称'" class="grid-item" :colon="false">
  <!-- 表单控件 -->
</a-form-item>
```

修改为：
```vue
<a-form-item name="fieldName" class="grid-item" :colon="false">
  <template #label>
    <span 
      class="form-label" 
      :class="getLabelClass('fieldName')"
      @click="handleLabelClick('fieldName')"
    >
      字段名称
    </span>
  </template>
  <!-- 表单控件 -->
</a-form-item>
```

### 4. 添加CSS样式

在组件的 `<style>` 部分添加以下样式：

```css
<style scoped>
.form-label {
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
}

.form-label:hover {
  opacity: 0.8;
}

.label-green {
  background-color: #52c41a;
  color: white;
}

.label-red {
  background-color: #ff4d4f;
  color: white;
}
</style>
```

或者使用提供的样式生成函数：

```javascript
import { getFieldMarkingStyles } from '@/utils/useFieldMarking'

// 在需要的地方使用
const styles = getFieldMarkingStyles()
```

## API 参考

### 返回值

| 属性名 | 类型 | 描述 |
|--------|------|------|
| fieldMarkings | Ref<Object> | 字段标记状态的响应式对象 |
| handleLabelClick | Function | 处理label点击事件的方法 |
| getLabelClass | Function | 获取label CSS类名的方法 |
| setFieldMarkings | Function | 设置字段标记状态 |
| getFieldMarkings | Function | 获取当前字段标记状态 |
| clearFieldMarkings | Function | 清空所有字段标记 |
| getMarkedFieldsCount | Function | 获取标记字段的数量 |

### 方法详情

#### handleLabelClick(fieldName)
处理label点击事件
- **参数**: `fieldName` (string) - 字段名称
- **返回值**: 无

#### getLabelClass(fieldName)
获取label的CSS类名
- **参数**: `fieldName` (string) - 字段名称
- **返回值**: string - CSS类名（'label-green'、'label-red' 或 ''）

#### setFieldMarkings(markings)
设置字段标记状态
- **参数**: `markings` (Object) - 字段标记状态对象
- **返回值**: 无

#### getFieldMarkings()
获取当前字段标记状态
- **参数**: 无
- **返回值**: Object - 字段标记状态对象

#### clearFieldMarkings()
清空所有字段标记
- **参数**: 无
- **返回值**: 无

#### getMarkedFieldsCount()
获取标记字段的数量
- **参数**: 无
- **返回值**: number - 已标记字段的数量

## 数据持久化

如果需要将标记状态保存到数据库或本地存储，可以使用以下方法：

```javascript
// 保存标记状态
const saveMarkings = async () => {
  const markings = getFieldMarkings()
  // 调用API保存到数据库
  await saveFieldMarkingsToDatabase(markings)
}

// 加载标记状态
const loadMarkings = async () => {
  const markings = await loadFieldMarkingsFromDatabase()
  setFieldMarkings(markings)
}
```

## 完整示例

```vue
<template>
  <a-form ref="formRef" :model="formData" :rules="rules">
    <a-form-item name="customerName" class="grid-item" :colon="false">
      <template #label>
        <span 
          class="form-label" 
          :class="getLabelClass('customerName')"
          @click="handleLabelClick('customerName')"
        >
          客户名称
        </span>
      </template>
      <a-input v-model:value="formData.customerName" />
    </a-form-item>
    
    <a-form-item name="contractNo" class="grid-item" :colon="false">
      <template #label>
        <span 
          class="form-label" 
          :class="getLabelClass('contractNo')"
          @click="handleLabelClick('contractNo')"
        >
          合同编号
        </span>
      </template>
      <a-input v-model:value="formData.contractNo" />
    </a-form-item>
  </a-form>
</template>

<script setup>
import { ref } from 'vue'
import { useFieldMarking } from '@/utils/useFieldMarking'

// 表单数据
const formData = ref({
  customerName: '',
  contractNo: ''
})

// 使用字段标记功能
const {
  handleLabelClick,
  getLabelClass,
  getFieldMarkings
} = useFieldMarking()

// 其他业务逻辑...
</script>

<style scoped>
.form-label {
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
}

.form-label:hover {
  opacity: 0.8;
}

.label-green {
  background-color: #52c41a;
  color: white;
}

.label-red {
  background-color: #ff4d4f;
  color: white;
}
</style>
```

## 注意事项

1. 确保字段名称（fieldName）在同一个表单中是唯一的
2. CSS样式需要在使用的组件中引入
3. 如果需要数据持久化，请根据项目需求实现相应的保存和加载逻辑
4. 建议在表单提交前清空标记状态，避免影响业务数据