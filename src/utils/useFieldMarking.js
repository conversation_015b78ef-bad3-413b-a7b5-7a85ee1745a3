import { ref } from 'vue'
import ycCsApi from "@/api/ycCsApi";

/**
 * 表单字段标记功能的组合式函数
 * 用于实现表单label点击切换背景色功能
 * @returns {Object} 包含字段标记相关的方法和数据
 */
export function useFieldMarking() {
  // 字段标记状态管理
  const fieldMarkings = ref({})

  /**
   * 处理label点击事件
   * @param {string} fieldName - 字段名称
   */
  const handleLabelClick = (fieldName) => {
    if (!fieldMarkings.value[fieldName]) {
      // 第一次点击：设置为绿色
      fieldMarkings.value[fieldName] = 'green'
    } else if (fieldMarkings.value[fieldName] === 'green') {
      // 第二次点击：设置为红色
      fieldMarkings.value[fieldName] = 'red'
    } else {
      // 第三次点击：恢复原色
      delete fieldMarkings.value[fieldName]
    }

    // 记录字段名和颜色，用于后续存储
    console.log('字段标记状态:', fieldMarkings.value)
  }

  /**
   * 获取label的CSS类名
   * @param {string} fieldName - 字段名称
   * @returns {string} CSS类名
   */
  const getLabelClass = (fieldName) => {
    const marking = fieldMarkings.value[fieldName]
    if (marking === 'green') {
      return 'label-green'
    } else if (marking === 'red') {
      return 'label-red'
    }
    return ''
  }

  /**
   * 设置字段标记状态
   * @param {Object} markings - 字段标记状态对象
   */
  const setFieldMarkings = (markings) => {
    fieldMarkings.value = { ...markings }
  }

  /**
   * 获取当前字段标记状态
   * @returns {Object} 字段标记状态对象
   */
  const getFieldMarkings = () => {
    return fieldMarkings.value
  }

  /**
   * 清空所有字段标记
   */
  const clearFieldMarkings = () => {
    fieldMarkings.value = {}
  }

  /**
   * 获取标记字段的数量
   * @returns {number} 已标记字段的数量
   */
  const getMarkedFieldsCount = () => {
    return Object.keys(fieldMarkings.value).length
  }

  /**
   * 保存审核记录
   * @param sid
   * @param formType
   * @param extraData
   */
  const saveCurrentMarkings = (sid, formType = 'default', extraData = {}) => {
    if (!sid) {
      console.error('保存审核记录失败: 缺少表单记录ID')
      return Promise.reject('缺少表单记录ID')
    }

    const data = {
      sid,
      formType,
      fieldMarkings: JSON.stringify(extraData),
    }

    window.majesty.httpUtil.postAction(ycCsApi.aeoManage.fieldMarking.saveOrUpdate, data)
      .then(() => {
    })
  }

  /**
   * 根据sid和formType查询字段标记记录
   * @param {string} sid - 业务ID
   * @param {string} formType - 表单类型
   * @returns {Promise} - 查询结果Promise
   */
  const getBySidAndFormType = (sid, formType = 'default') => {
    if (!sid) {
      console.error('查询字段标记失败: 缺少表单记录ID')
      return Promise.reject('缺少表单记录ID')
    }

    const params = {
      sid,
      formType
    }

    return window.majesty.httpUtil.getAction(ycCsApi.aeoManage.fieldMarking.getBySidAndFormType, params)
      .then((response) => {
        if (response && response.data) {
          // 解析字段标记数据并赋值到fieldMarkings.value
          try {
            const markingData = response.data.fieldMarkings ? JSON.parse(response.data.fieldMarkings) : {}
            fieldMarkings.value = markingData
            console.log('字段标记数据加载成功:', markingData)
            return markingData
          } catch (error) {
            console.error('解析字段标记数据失败:', error)
            fieldMarkings.value = {}
            return {}
          }
        } else {
          fieldMarkings.value = {}
          return {}
        }
      })
      .catch((error) => {
        console.error('查询字段标记失败:', error)
        fieldMarkings.value = {}
        return Promise.reject(error)
      })
  }

  return {
    fieldMarkings,
    handleLabelClick,
    getLabelClass,
    setFieldMarkings,
    getFieldMarkings,
    clearFieldMarkings,
    getMarkedFieldsCount,
    saveCurrentMarkings,
    getBySidAndFormType
  }
}

/**
 * 生成表单项label的模板代码
 * @param {string} fieldName - 字段名称
 * @param {string} labelText - 标签文本
 * @returns {string} Vue模板代码
 */
export function generateLabelTemplate(fieldName, labelText) {
  return `<template #label>
  <span
    class="form-label"
    :class="getLabelClass('${fieldName}')"
    @click="handleLabelClick('${fieldName}')"
  >
    ${labelText}
  </span>
</template>`
}

/**
 * 获取字段标记功能所需的CSS样式
 * @returns {string} CSS样式代码
 */
export function getFieldMarkingStyles() {
  return `
.form-label {
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
}

.form-label:hover {
  opacity: 0.8;
}

.label-green {
  background-color: #52c41a;
  color: white;
}

.label-red {
  background-color: #ff4d4f;
  color: white;
}
`
}
