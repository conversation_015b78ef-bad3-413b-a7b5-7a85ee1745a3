import xdoCache from './XdoCache'

class Pcode {
  codeEnum = Object.freeze({
    /** 入离境口岸 */
    ciqEntyPort: 'CIQ_ENTY_PORT',
    /** 进口口岸 */
    port_lin: 'PORT_LIN',
    /** 检验检疫申报要素 */
    requestCert: 'REQUEST_CERT',
    /** 原产地区 */
    originPlace: 'ORIGIN_PLACE',
    /** 包装方式（组合） */
    wrapCombination: 'WRAP_COMBINATION',
    /** 企业性质 */
    co_type: 'CO_TYPE',
    /** 运输方式 */
    transfOutdated: 'TRANSF_OUTDATED',
    /** 征免性质 */
    levytype: 'LEVYTYPE',
    /** 地区性质 */
    dist_type: 'DIST_TYPE',
    /** 关区 */
    customs_rel: 'CUSTOMS_REL',
    /** 国别--组合包含关检融合后 */
    countryCombination: 'COUNTRY_COMBINATION',
    /** 国别(旧) */
    country_outdated: 'COUNTRY_OUTDATED',
    /** 境内目的地-行政区划代码 */
    post_area: 'POST_AREA',
    /** 币制 (旧)，原502,142.。。*/
    curr_outdated: 'CURR_OUTDATED',
    /** 许可证类别 */
    licType: 'LIC_TYPE',
    /** 计量单位 */
    unit: 'UNIT',
    /** 地区-境内货源地 */
    area: 'AREA',
    /** 商品编码-检验检疫证件代码 */
    complexInspection: 'COMPLEX_INSPECTION',
    /** 企业 */
    company: 'COMPANY',
    /** 社会信用代码 */
    creditcode: 'CREDITCODE',
    /** 商品编码 */
    complex: 'COMPLEX',
    /** 成交方式 */
    transac: 'TRANSAC',
    /** 用途(检验检疫) */
    useToCiq: 'USE_TO_CIQ',
    /** 包装种类 */
    wrap: 'WRAP',
    /** 结汇方式 */
    lc_type: 'LC_TYPE',
    /** 监管证件 */
    licensedocu: 'LICENSEDOCU',
    /** 监管方式 */
    trade: 'TRADE',
    /** 报关单随附单据代码 */
    attType: 'ATT_TYPE',
    /** 加工种类 */
    product_type_mnl: 'PRODUCT_TYPE_MNL',
    /** 用途 */
    use_to: 'USE_TO',
    /** 运费标记 */
    feeMark: 'FEE_MARK',
    /** 贸易条款 */
    tradeTerms: 'TRADE_TERMS',
    /** 报关单类型 */
    entryType: 'ENTRY_TYPE',
    /** 企业资质 */
    entqualif: 'ENTQUALIF',
    /** 特殊物品附件类型 */
    edocCode: 'EDOC_CODE',
    /** 关联理由 */
    reasonflag: 'REASONFLAG',
    /** 币制，新USD,CHK... */
    curr: 'CURR',
    /** 国别 */
    country: 'COUNTRY',
    /** 商品编码-CIQ编码 */
    complexCiqcode: 'COMPLEX_CIQCODE',
    /** 运输方式 */
    transf: 'TRANSF',
    /** 征减免税方式 */
    levymode: 'LEVYMODE',
    /** 申报要素 */
    element: 'ELEMENT',
    /** 集装箱规格 */
    container_model: 'CONTAINER_MODEL',
    /** 币制组合 */
    curr_combination: 'CURR_COMBINATION',
  })

  _keys = new Map()
  _lcache = new WeakMap()
  _api = null

  setupApi (api) {
    this._api = api
  }

  initDict () {
    const params = [this.codeEnum.ciqEntyPort, this.codeEnum.port_lin, this.codeEnum.requestCert,
      this.codeEnum.originPlace, this.codeEnum.wrapCombination, this.codeEnum.co_type,
      this.codeEnum.transfOutdated,
      this.codeEnum.levytype, this.codeEnum.dist_type, this.codeEnum.customs_rel, this.codeEnum.countryCombination,
      this.codeEnum.country_outdated,this.codeEnum.post_area,
      this.codeEnum.curr_outdated, this.codeEnum.licType, this.codeEnum.unit, this.codeEnum.area,
      this.codeEnum.complexInspection,
      this.codeEnum.creditcode,
      this.codeEnum.transac,
      this.codeEnum.useToCiq, this.codeEnum.wrap, this.codeEnum.lc_type, this.codeEnum.licensedocu,
      this.codeEnum.trade,
      this.codeEnum.attType, this.codeEnum.product_type_mnl, this.codeEnum.use_to,
      this.codeEnum.feeMark,
      this.codeEnum.tradeTerms, this.codeEnum.entryType, this.codeEnum.entqualif, this.codeEnum.edocCode,
      this.codeEnum.reasonflag,
      this.codeEnum.curr, this.codeEnum.country, this.codeEnum.complexCiqcode,
      this.codeEnum.transf,this.codeEnum.levymode,this.codeEnum.element,this.codeEnum.container_model,this.codeEnum.curr_combination]

    this.load(params)
  }

  remote (type, code) {
    return this.load([type], code, '')
  }

  get (type, key) {
    if (!type) {
      console.warn(`type: ${type} 为空`)
      return ''
    }

    if (!key) {
      console.warn(`key: ${key} 为空`)
      return ''
    }

    let dict = xdoCache.getSync(type)
    if (dict && dict.hasOwnProperty(key)) {
      return dict[key]
    }

    // eslint-disable-next-line
    console.warn(`${type} ${key} 找不到`)
    return ''
  }

  // eslint-disable-next-line
  async getList (codeEnum, option) {
    const self = this
    const cacheKey = codeEnum
    if (!self._keys.has(cacheKey)) {
      // 构建二级缓存key
      self._keys.set(cacheKey, { key : cacheKey })
    }

    let dict = null
    const weakKey = self._keys.get(cacheKey)

    if (self._lcache.has(weakKey)) {
      dict = self._lcache.get(weakKey)
      if (dict) {
        return dict
      }
    } else {
      dict = await xdoCache.get(codeEnum, (key) => {
        if (!key) {
          return Promise.reject({});
        }

        return this.load([key])
      })
      self._lcache.set(weakKey, dict)
    }

    if (dict) {
      let table = Object.keys(dict).map(value => {
        return {
          value,
          text: dict[value],
          title: dict[value]
        }
      })

      table = table.sort((a, b) => a.value * 1 - b.value * 1)

      self._lcache.set(weakKey, table)

      return table
    }
    return []
  }

  /***
   *
   * @param paramList
   * @param code
   * @param format map, table
   * @returns {Promise<void>}
   */
  async load(paramList, code = '', format='map') {
    const params = {
      type: paramList.filter(el => (el)).join(',')
    }

    if (format === 'map') {
      // 只缓存字典，不缓存table
      const version = xdoCache.get('etag', false)
      const etag = paramList.map(el => version[el]).filter(el => (el)).join(',')
      if (etag) {
        params.etag =  etag
      }
    }

    if (code) {
      params.code = code
    }

    if (format) {
      params.format = format
    }

    const res = await this._api.get('/api/pcode', { params })
    if (res.success) {
      const val = res.data
      if (format !== 'map') {
        // 非map 情况下，直接返回数据，不进行缓存处理
        return Promise.resolve(val)
      }

      const etags = res.etag.split(',')
      const version = xdoCache.get('etag', false)
      for (let i = 0; i < paramList.length; i++) {
        const key = paramList[i];
        if (val.hasOwnProperty(key)) {
          xdoCache.put(key, val[key])
        }
        version[key] = etags[i]
      }
      xdoCache.put('etag', version)

      if (paramList.length === 1) {
        return Promise.resolve(val[paramList[0]])
      }
    }
  }
}

export default new Pcode()
