
import store from './StoreUtil'

class XdoCache {
  _keys = new Map()
  _cacheMap = new WeakMap()

  async put (key, value) {
    this.setupCacheKey(key)
    this._cacheMap.set(this._keys.get(key), value)
    store.setItem(key, value)
  }

  async get (key, fn) {
    const weakKey = this.setupCacheKey(key)
    if (this._cacheMap.has(weakKey)) {
      return this._cacheMap.get(weakKey)
    }
    let value = store.getItem(key)

    if (value) {
      this._cacheMap.set(weakKey, value)
      return value
    } else {
      if (fn && typeof fn === 'function') {
        const fnData = await fn(key)
        this._cacheMap.set(weakKey, fnData)
        return fnData
      } else {
        return null
      }
    }
  }

  getSync (key) {
    const cacheKey = this.setupCacheKey(key)
    if (this._cacheMap.has(cacheKey)) {
      return this._cacheMap.get(cacheKey)
    }

    let value = store.getItem(key)
    if (value) {
      this._cacheMap.set(cacheKey, value)
      return value
    }

    return null;
  }


  /***
   * 设置缓存的key
   *
   * @param key
   */
  setupCacheKey(key) {
    const cacheKey = { key: key }
    if (!this._keys.has(cacheKey)) {
      this._keys.set(key, { key : cacheKey })
    }
    return cacheKey
  }

  async remove (key) {
    this._cacheMap.delete(key)
    store.removeItem(key)
  }

  async flush () {

  }

  async flushExpired () {

  }
}

export default new XdoCache()
