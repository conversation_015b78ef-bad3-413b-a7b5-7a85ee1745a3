<template>
  <a-popover
    :placement="placement"
    :trigger="trigger"
    arrow-point-at-center
    overlay-class-name="table-column-setting-popover"
    :auto-adjust-overflow="true"
  >
    <template #title>
      <div class="table-column-setting-title">
        <a-checkbox
          :indeterminate="indeterminate"
          :checked="checkAll"
          @change="allColumnChange"
        >
          {{ $t("m.components.label.columnShow") }}
        </a-checkbox>
        <a href="#" @click="resetColumn" class="table-column-setting-reset">{{
            $t("m.common.button.reset")
          }}</a>
        <!--
        <a href="#" @click="handleOk" class="table-column-setting-reset" v-if="showColumnSettings">{{ $t('m.common.button.ok') }}</a>
        <a href="#" @click="handleDel" class="table-column-setting-reset" v-if="showColumnSettings">{{ $t('m.common.button.delete') }}</a>
        -->
      </div>
    </template>
    <template #content>
      <div class="table-column-setting-list">
        <div style="max-height: 450px; overflow: auto">
          <!-- 固定左侧的列 -->
          <!--
        <ul class="table-column-setting-ship_from-item">
          <li
            class="table-column-setting-ship_from-item-li"
            v-for="(item, index) in fixedLeftColumns"
            :key="index"
            :title="item.title"
          >
            <a-checkbox
              :value="item.dataIndex"
              :checked="item.visible"
              @change="columnChange(item)"
            >
              {{ item.title }}
            </a-checkbox>
          </li>
        </ul>
        -->
          <ul class="table-column-setting-list-item">
            <VueDraggableNext
              :list="settingColumns"
              v-bind="dragOptions"
              handle=".li-drag"
              @start="drag = true"
              @end="dragEnd"
              tag="div"
            >
              <transition-group>
                <template
                  v-for="(item, index) in settingColumns"
                  :key="item.dataIndex"
                >
                  <li
                    :title="item.title"
                    class="table-column-setting-list-item-li"
                  >
                    <div
                      onmouseover="this.style.background='#F7F9FA';"
                      onmouseout="this.style.background='white';"
                    >
                      <a-row>
                        <a-col :span="14" >
                          <a-checkbox  :value="item.dataIndex" :checked="item.visible"
                                       @change="columnChange(item)">
                            <div style="cursor: move;" class="li-drag no-select">
                              {{ item.title }}
                            </div>
                          </a-checkbox>
                        </a-col>
                        <a-col v-if="showColumnSettings" :span="10">
                          <a-row>
                            <a-col :span="12" class="fixed" @click="handleFixSwitchChange(item, 'left')">
                              <PushpinFilled :title="$t('m.common.title.fixed_left')" v-show="item.leftFixed" class="fixed-checked" />
                              <PushpinOutlined :title="$t('m.common.title.fixed_left')" v-show="!item.leftFixed" />
                              <!-- <a-switch v-if="showColumnSettings" @change="(checked) => handleFixSwitchChange(checked, item)" v-model:checked="item.leftFixed" size="small" style="float: right; margin-right: 40px" checked-children="固定" un-checked-children="不固定" /> -->
                            </a-col>
                            <a-col :span="12" class="fixed" @click="handleFixSwitchChange(item, 'right')">
                              <PushpinFilled :title="$t('m.common.title.fixed_right')" v-show="item.rightFixed" class="rotate-270 fixed-checked" />
                              <PushpinOutlined :title="$t('m.common.title.fixed_right')" v-show="!item.rightFixed" class="rotate-270" />
                            </a-col>
                          </a-row>
                        </a-col>
                      </a-row>

                      <!--
                  <a-switch v-if="showColumnSettings" @change="(checked) => handleFixSwitchChange(checked, item)" v-model:checked="item.leftFixed" size="small" style="float: right; margin-right: 40px" checked-children="固定" un-checked-children="不固定" />
                  <ych-icon type="drag" class="li-drag" style="
                      position: absolute;
                      right: 5px;
                      font-size: 1rem;
                      color: #888;
                      line-height: 26px;
                      cursor: move;"></ych-icon>
                    --></div>
                  </li>
                </template>
              </transition-group>
            </VueDraggableNext>
          </ul>
          <!-- 固定右侧的列 -->
          <!-- <ul class="table-column-setting-ship_from-item">
            <li
              class="table-column-setting-ship_from-item-li"
              v-for="(item, index) in fixedRightColumns"
              :key="index"
              :title="item.title"
            >
              <a-checkbox
                :value="item.dataIndex"
                :checked="item.visible"
                @change="columnChange(item)"
              >
                {{ item.title }}
              </a-checkbox>
            </li>
          </ul> -->
        </div>
        <div v-if="showColumnSettings">
          <a-row :gutter="8">
            <a-col span="18">
              <a-form ref="formRef" :model="formState">
                <a-form-item
                  name="name"
                  :rules="[
                    {
                      required: true,
                      message: this.$t(
                        'm.system.login.verify.specific_template_name'
                      ),
                    },
                  ]"
                >
                  <a-input
                    v-model:value="formState.name"
                    :placeholder="$t('m.eform.apptable.placeholder.template_name')"
                  />
                </a-form-item>
              </a-form>
            </a-col>
            <a-col span="6">
              <a
                href="#"
                @click="handleOk"
                class="table-column-setting-reset"
                v-if="showColumnSettings"
              >{{ $t("m.common.button.save_template") }}</a
              >
            </a-col>
          </a-row>
          <a-row :gutter="8">
            <a-col :span="18">
              <a-select
                style="width: 99%"
                :placeholder="$t('m.eform.apptable.placeholder.template_select')"
                v-model:value="selectionName"
                :options="templateOptions"
                @select="onTemplateSelect"
              >
              </a-select>
            </a-col>
            <a-col span="6">
              <a
                href="#"
                @click="handleDel"
                class="table-column-setting-reset"
                v-if="showColumnSettings"
              >{{ $t("m.common.button.del_template") }}</a
              >
            </a-col>
          </a-row>
        </div>
      </div>
    </template>
    <GlobalIcon
      type="setting"
      :style="{ fontSize: '14px', marginLeft: '15px' }"
      :title="$t('m.components.title.columnSet')"
    />
  </a-popover>
</template>

<script>
import { VueDraggableNext } from "vue-draggable-next";
import { mapGetters, mapActions } from "vuex";
import { ref, reactive } from "vue";
import { putAction, getAction } from "@/api/manage";
import CsConstant from "@/api/CsConstant";
import { PushpinOutlined,PushpinFilled } from '@ant-design/icons-vue';
export default {
  name: "CsTableColSettings",
  components: {
    VueDraggableNext,
    PushpinOutlined,
    PushpinFilled
  },
  emits: ["customColumnChange"],
  props: {
    showColumnSettings: false,
    resId: "",
    tableKey: {
      type: String,
    },
    // 弹出框位置
    placement: {
      type: String,
      default: "bottomRight",
    },
    // 触发方式
    trigger: {
      type: String,
      default: "click",
    },
    // 记录的初始列设置columns
    initSettingColumns: {
      type: Array,
      default: () => [],
    },
  },
  setup() {
    const formRef = ref();
    const formState = reactive({});
    return { formRef, formState };
  },
  data() {
    return {
      url: {
        viewColumn: CsConstant.PREFIX_SYSTEM + "/config/company/view_column",
        all: CsConstant.PREFIX_SYSTEM + "/config/all",
      },
      selectionName: "",
      templateOptions: [],
      templates: {},
      settingColumns: [], // 内部的列设置columns
      indeterminate: false, // 列显隐全选按钮的半选状态的样式控制
      checkAll: true, // 列显隐全选按钮的全选与全不选标志
      dragOptions: {
        // 列表拖拽配置参数
        animation: 300,
        group: "columnsSettingDragGroup",
        disabled: false,
        ghostClass: "table-column-setting-ghost",
        forceFallback: true,
        fallbackClass: "table-column-setting-drag-fallback",
      },
      drag: false, // 列表拖拽状态
    };
  },
  computed: {
    /**
     * 引入个性化设置vuex的getter
     */
    ...mapGetters(["customTableColumns"]),
    // 可响应个性化配置数据变化的模块列配置
    customCols() {
      return this.customTableColumns(this.tableKey);
    },
    fixedLeftColumns() {
      let leftCols = [];
      this.settingColumns.map((item) => {
        if (item.fixed === "left") {
          let fixLeftCol = Object.assign({}, item);
          leftCols.push(fixLeftCol);
        }
      });
      return leftCols;
    },
    fixedRightColumns() {
      let rightCols = [];
      this.settingColumns.map((item) => {
        if (item.fixed === "right") {
          let fixRightCol = Object.assign({}, item);
          rightCols.push(fixRightCol);
        }
      });
      return rightCols;
    },
  },
  methods: {
    handleFixSwitchChange(item,position) {
      if(position === 'left'){
        item.rightFixed = false
        if(item.fixed === 'left'){
          item.fixed = ""
          item.leftFixed = false
        }else{
          item.fixed = "left"
          item.leftFixed = true
        }
      }else if(position === 'right'){
        item.leftFixed = false
        if(item.fixed === 'right'){
          item.fixed = ""
          item.rightFixed = false
        }else{
          item.fixed = "right"
          item.rightFixed = true
        }
      }
      // if (checked) {
      //   item.fixed = "left";
      // } else {
      //   item.fixed = "";
      // }
      this.$nextTick(() => {
        console.log('固定列变化')
        this.columnShowChange();
      });
    },
    onTemplateSelect(value, o) {
      this.formState.name = value;
      this.settingColumns = JSON.parse(
        JSON.stringify(this.templates[this.resId][value])
      );
      this.$nextTick(() => {
        console.log('选择模板')
        this.columnShowChange();
      });
    },
    loadTemplate() {
      window.majesty.httpUtil.getAction(this.url.all).then((res) => {
        let tmp = [];
        let columnSetting = res.data
        if (columnSetting &&  window.$vueApp) {
          this.$store.commit('SET_COLUMNFORM', columnSetting.form)
          this.$store.commit('SET_COLUMNTABLE', columnSetting.tableColumn)
          this.$store.commit('SET_GWSETTING', columnSetting.gwSetting)
        }
        if (res&& res.data && res.data.viewColumn && res.data.viewColumn[this.resId]) {
          Object.keys(res.data.viewColumn[this.resId]).forEach((o) => {
            tmp.push({ value: o, label: o });
          });
        }
        if(res && res.data && res.data.viewColumn){
          this.templates = res.data.viewColumn;

        }
        this.templateOptions = [...new Set(tmp)];
      });
    },
    handleDel() {
      this.formRef
        .validate()
        .then(() => {
          let data = {};
          data.key = this.resId;
          let templateToDelete = this.templates[this.resId];
          delete templateToDelete[this.formState.name];
          let tmp = [];
          Object.keys(this.templates[this.resId]).forEach((o) => {
            tmp.push({ value: o, label: o });
          });
          this.selectionName = "";
          this.templateOptions = [...new Set(tmp)];

          data.value = this.templates;
          window.majesty.httpUtil.putAction(this.url.viewColumn, data).then((res) => {
            this.$message.success(
              this.$t("m.eform.appform.business.delete_success")
            );
            this.formState.name = "";
          });
        })
        .catch((error) => {
          console.log(error);
        });
    },
    handleOk() {
      this.formRef
        .validate()
        .then(() => {
          let data = {};
          data.key = this.resId;
          if (!this.templates) {
            this.templates = {}
          }
          if (!this.templates[this.resId]) {
            this.templates[this.resId] = {};
          }
          this.templates[this.resId][this.formState.name] = JSON.parse(JSON.stringify(this.settingColumns));
          let tmp = [];
          Object.keys(this.templates[this.resId]).forEach((o) => {
            tmp.push({ value: o, label: o });
          });
          this.templateOptions = [...new Set(tmp)];

          data.value = this.templates;
          window.majesty.httpUtil.putAction(this.url.viewColumn, data).then((res) => {
            this.$message.success(
              this.$t("m.eform.formDesign.label.bao-cun-cheng-gong")
            );
            this.formState.name = "";
            this.selectionName = "";
          });
        })
        .catch((error) => {
          console.log(error);
        });
    },
    ...mapActions(["loadCustomTableColumns"]),
    /**
     * 改变所有字段的显隐
     */
    allColumnChange(e) {
      let columns = [...this.settingColumns];
      if (e.target.checked) {
        // 全选
        columns.map((item) => {
          item.visible = true;
        });
        this.indeterminate = false;
        this.checkAll = true;
      } else {
        // 全不选
        columns.map((item) => {
          item.visible = false;
        });
        this.indeterminate = false;
        this.checkAll = false;
      }
      this.settingColumns = columns;
      this.$nextTick(() => {
        console.log('改变所有字段的显隐')
        this.columnShowChange();
      });
    },
    /**
     * 重置
     */
    resetColumn() {
      // 深拷贝，以免改变原数组
      let dealColumns = [];
      this.initSettingColumns.map((item) => {
        let newObj = Object.assign({}, item);
        dealColumns.push(newObj);
      });
      this.settingColumns = dealColumns;
      this.formState.name = "";
      this.selectionName = "";
      this.$nextTick(() => {
        console.log('重置')
        this.columnShowChange(true);
      });
    },
    /**
     * 改变单个字段的显隐
     */
    columnChange(column) {
      let columns = [...this.settingColumns];
      columns.map((item) => {
        if (item.dataIndex == column.dataIndex) {
          item.visible = !item.visible;
        }
      });
      this.settingColumns = columns;
      this.$nextTick(() => {
        console.log('改变单个字段的显隐')
        this.columnShowChange();
      });
    },
    allCheckStyle(fullColumn) {
      let showNum = 0;
      fullColumn.map((item) => {
        if (item.visible) {
          showNum++;
        }
      });
      this.checkAll = showNum > 0 && showNum == fullColumn.length;
      this.indeterminate = showNum > 0 && showNum < fullColumn.length;
    },
    /**
     * 列表拖拽end事件
     */
    dragEnd() {
      this.drag = false;
      this.$nextTick(() => {
        console.log('列表拖拽end事件')
        this.columnShowChange();
      });
    },
    /**
     * 列显隐变化传回父组件事件
     */
    columnShowChange(isReset) {
      this.allCheckStyle(this.settingColumns);
      this.$emit("customColumnChange", this.settingColumns);
      let storeCustomColumns = [];
      //不是重置保存个性化配置，重置传空
      if (!isReset) {
        storeCustomColumns = this.settingColumns.map((item) => {
          let newItem = {
            dataIndex: item.dataIndex,
            visible: item.visible,
            leftFixed: item.leftFixed,
            rightFixed: item.rightFixed
          };
          return newItem;
        });
      }
      //保存个性化列设置
      this.$store.commit("SAVE_CUSTOM_TABLE_COLUMNS", {
        key: this.tableKey,
        val: storeCustomColumns,
      });
    },
    dealCustomColumns() {
      let customColumns = this.customCols;
      if (customColumns) {
        if (customColumns.length < 1) {
          //深拷贝
          let newInitSettingColumns = [];
          for (let i = 0; i < this.initSettingColumns.length > 0; i++) {
            let newObj = Object.assign({}, this.initSettingColumns[i]);
            newInitSettingColumns.push(newObj);
          }
          this.settingColumns = newInitSettingColumns;
          return;
        }
        const columns = [...this.initSettingColumns]; // 表格所有字段
        let realColumns = []; // 排序后的真实columns
        for (let i = 0; i < customColumns.length; i++) {
          for (let j = 0; j < columns.length; j++) {
            // 个性化表格设置不为空,则获取个性化表格设置与表格所有字段的公共部分
            if (
              columns[j].dataIndex &&
              customColumns[i].dataIndex &&
              columns[j].dataIndex === customColumns[i].dataIndex
            ) {
              let newObj = Object.assign({}, columns[j]);
              newObj.visible = customColumns[i].visible;
              newObj.fixed = customColumns[i].fixed;
              newObj.leftFixed = customColumns[i].leftFixed;
              newObj.rightFixed = customColumns[i].rightFixed;
              realColumns.push(newObj);
            }
          }
        }
        // 追加授权变化等新增的列
        for (let i = 0; i < columns.length; i++) {
          let isSet = false;
          realColumns.map((item) => {
            if (item.dataIndex === columns[i].dataIndex) {
              isSet = true;
            }
          });
          if (!isSet) {
            columns[i].visible = true;
            realColumns.push(columns[i]);
          }
        }
        // 按固定列左右排列
        let sortedColumns = [];
        realColumns.map((item) => {
          if (item.fixed === "left" || item.leftFixed) {
            item.fixed = "left";
            item.leftFixed = true;
            sortedColumns.push(item);
          }
        });

        realColumns.map((item) => {
          if (!item.fixed && !item.rightFixed) {
            sortedColumns.push(item);
          }
        });
        realColumns.map((item) => {
          if (item.fixed === "right" || item.rightFixed) {
            item.fixed = "right";
            item.rightFixed = true
            sortedColumns.push(item);
          }
        });
        //sortedColumns.forEach(item => {
        //  if (item.leftFixed) {
        //    item.fixed = 'left'
        //  }
        //});

        this.settingColumns = sortedColumns;
        this.allCheckStyle(this.settingColumns);
        this.$emit("customColumnChange", this.settingColumns);
      } else {
        this.loadCustomTableColumns(this.tableKey);
      }
    },
  },
  watch: {
    initSettingColumns: {
      immediate: true,
      handler(val) {
        if (val) {
          if (this.showColumnSettings) {
            this.loadTemplate();
          }
          console.log('initSettingColumns',val)
          this.dealCustomColumns();
        }
      },
    },
    customCols: {
      immediate: true,
      handler(val) {
        if (val) {
          this.dealCustomColumns();
        }
      },
    },
  },
};
</script>

<style lang="less">
/* Start 列显隐样式 */
.table-column-setting-popover .ant-popover-inner-content {
  max-height: 520px;
  overflow: hidden;
}
/* 列显隐列表的滚动条样式 */
.table-column-setting-popover
.ant-popover-inner-content::-webkit-scrollbar-thumb {
  border-width: 0 0 0 9px;
}
.table-column-setting-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 32px;
  .ant-checkbox-wrapper {
    font-size: 12px;
  }
}
.table-column-setting-list {
  display: flex;
  flex-direction: column;
  width: 258px;
  .table-column-setting-list-item {
    display: block;
    width: 100%;
    list-style: none;
    .table-column-setting-list-item-li {
      position: relative;
      width: 100%;
      height: 30px;
      padding-right: 0.8rem;
      .ant-checkbox-wrapper {
        // margin-bottom: 8px;
        margin-left: 0;
        width: 100%;
        font-size: 12px;
        height: 22px;
        line-height: 22px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .fixed{
        cursor: pointer;
        &:hover{
          color: var(--primary-color)
        }
        .rotate-270{
          transform: rotate(270deg);
        }
        .fixed-checked{
          color: var(--primary-color)
        }
      }
    }
  }
}
/* End 列显隐样式 */
/* Start 拖拽相关 */

.table-column-setting-ghost {
  opacity: 0.8;
}

.table-column-setting-drag-fallback {
  opacity: 0 !important;
}
.no-select {
  user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;
}

/* End 拖拽相关 */
</style>
