<template>
    <ant-select popupClassName="cs-select-dropdown"  id="cs-select" size="small" v-bind="$attrs" v-on="$attrs" @search="handleKeyDown" style="width: 100%" :default-active-first-option="activeFirstChecked">
      <!-- 使用 $slots.default 插槽，以保留原有 Select 的子组件 -->
      <slot></slot>
    </ant-select>
</template>

  <script>
  import {ref, reactive, toRefs, onMounted, onUnmounted, nextTick, watch, computed, h,} from 'vue';
  import { Select, Option } from 'ant-design-vue';

  export default {
    name: 'CsSelect',
    // 在组件中注册 Ant Design Vue 的 Select 组件
    components: {
      'ant-select': Select
    },
    setup() {
      const activeFirstChecked = ref(false)
      function handleKeyDown(val) {
        if (val) {
          activeFirstChecked.value = true
        } else {
          activeFirstChecked.value = false
        }
      }

      /**设置禁用的下拉框样式，允许其内容可选择复制 */
      function setDropDownBoxStyleByDisabled() {
        nextTick(() => {
          document.querySelectorAll('input[class="ant-select-selection-search-input"][disabled]').forEach(function(el) {
          // 获取当前元素的父元素
          var parent = el.parentNode;
          // 获取父元素的父元素
          var grandparent = parent.parentNode;

          if (grandparent.className === 'ant-select-selector' && el.disabled) {
            grandparent.style.pointerEvents ='none'
          }
        });
        })
      }

      onMounted(() => {
        setDropDownBoxStyleByDisabled()
      })

      return {
        activeFirstChecked,
        handleKeyDown
      }
    }
  }
  </script>


