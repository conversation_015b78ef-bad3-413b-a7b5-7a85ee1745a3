import * as Icons from '@ant-design/icons-vue'
import { createVNode } from 'vue'
const GlobalIcon = function (props) {
  const { type,iconStyle } = props
  let style = iconStyle ? iconStyle : 'outlined'
  let types = type.split("-")
  let newType = ''
  types.forEach(t=>{
    newType += titleCase(t)
  })
  return createVNode(Icons[newType + titleCase(style)])
};

function titleCase(str) {
  return str.toLowerCase().replace(/( |^)[a-z]/g, (L) => L.toUpperCase());
}

export { GlobalIcon }
