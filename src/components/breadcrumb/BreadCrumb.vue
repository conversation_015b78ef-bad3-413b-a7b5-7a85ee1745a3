<template>
  <div class="breadcrumb">
    <a-breadcrumb>
      <a-breadcrumb-item ><GlobalIcon type="home" /></a-breadcrumb-item>
      <a-breadcrumb-item v-for="(item, index) in breadCrumbList" :key="index" :to="item.titleObj.path">
        <span>{{getMetaTitleLocale(item.titleObj)}}</span>
      </a-breadcrumb-item>
    </a-breadcrumb>
    <div class="custom-search-buttons">
      <slot></slot>
    </div>
  </div>

</template>

<script setup>
import {computed} from 'vue';
import { useStore } from 'vuex';
import {isNullOrEmpty} from "@/view/utils/common";

const store = useStore();


const breadCrumbList = computed(() => {
  let bcList = [];
  // 如果getters.permissionList为空，则直接返回空数组
  console.log('2112',store)
  if (isNullOrEmpty(store)  || isNullOrEmpty(window.$vueApp)) {
    return bcList;
  }
  const permissionList = store.getters.permissionList;
  console.log('permissionList', permissionList);
  const routes = window.majesty.router.currentRoute.value.matched;
  console.log('routes', routes);

  if (routes?.length) {
    let selectedKeys = routes[routes.length - 1].path;
    console.log('selectedKeys', selectedKeys);
    let routeArr = [];
    const result = handleRoutes(permissionList, selectedKeys, routeArr);

    if (result) {
      console.log('routeArr', result);
      routeArr.reverse().forEach(item => {
        bcList.push({ path: '', titleObj: item });
      });
    }
  }
  return bcList;
});

const handleRoutes = (permissionList, currentPath, pathArr) => {
  for (const permission of permissionList) {
    if (permission.path === currentPath) {
      pathArr.push({
        title: permission.meta.title,
        zhCn: permission.meta.zhCn,
        enUs: permission.meta.enUs,
        thTh: permission.meta.thTh
      });
      return true;
    }
    if (permission.children?.length) {
      const found = handleRoutes(permission.children, currentPath, pathArr);
      if (found) {
        pathArr.push({
          title: permission.meta.title,
          zhCn: permission.meta.zhCn,
          enUs: permission.meta.enUs,
          thTh: permission.meta.thTh
        });
        return true;
      }
    }
  }
  return false;
};


const getMetaTitleLocale = (item) => {
  let secLang = window.$vueApp.ls.get("SEC_LANGUAGE_CODE")
  if (secLang) {
    if (secLang === 'zh_CN') {
      return item.title + '|' + item.zhCn
    } else if (secLang === 'en_US') {
      return item.title + '|' + item.enUs
    } else if (secLang ===  'th_TH') {
      return item.title + '|' + item.thTh
    }
  } else {
    return item.title
  }
}

</script>
<style lang="less" scoped>
:deep(.ant-breadcrumb-link){
  font-size: 12px;
}
.breadcrumb{
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-left: 10px;
}
</style>
