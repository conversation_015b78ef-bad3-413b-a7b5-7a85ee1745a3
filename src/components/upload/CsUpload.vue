<template>
  <div class="file-upload-container">
    <div class="file-content">
    <!-- 上传区域 -->
    <a-upload
      v-model:file-list="fileList"
      :before-upload="handleBeforeUpload"
      :custom-request="customUploadRequest"
      :show-upload-list="false"
      :multiple="true"
      :disabled="uploading"
      @change="handleChange"
    >
      <div class="file-list-left" v-if="isShow">
        <a-button type="link">
          <upload-outlined class="upload-icon" ></upload-outlined>
          上传附件
        </a-button>
      </div>
    </a-upload>

    <!-- 文件列表 -->
    <div class="file-list-right">
      <div v-for="file in uploadedFiles" :key="file.uid" class="file-item">
        <div class="file-info">
          <span class="file-name" @click="handlerOpenPre(file)">{{ file.name }}</span>
          <a-progress
            v-if="file.status === 'uploading'"
            :percent="file.percent"
            size="small"
            status="active"
          />
          <span v-else-if="file.status === 'error'" class="error-text">上传失败</span>
        </div>

        <div class="file-actions">
          <!-- 下载 -->
          <download-outlined
            v-show="isShow"
            class="download-icon"
            @click="handleDownload(file)"
          />
          <!-- 删除 -->
          <close-outlined
            v-show="isShow"
            class="delete-icon cs-margin-left"
            @click="handleRemove(file)"
          />
        </div>
      </div>
    </div>
    </div>
  </div>

  <div v-if="fileList.length > 0">
          <file-preview-modal   v-model:modelValue="showPreview" :file-data="preViewFileList" />
  </div>
</template>

<script setup>
import {ref, computed, onMounted} from 'vue'
import { message } from 'ant-design-vue'
import { UploadOutlined, FileOutlined, CloseOutlined,DownloadOutlined } from '@ant-design/icons-vue'
import {deleteAttachFile, getAttachFileList, getAttachFileListByType, uploadAttachFile} from "@/api/cs_api_constant";
import {blobSaveFile, isNullOrEmpty} from "@/view/utils/common";
import ycCsApi from "@/api/ycCsApi";
import FilePreviewModal from "@/components/upload/CsFilePreviewModal.vue";

defineOptions({
  name: 'CsUpload'
})

// 组件属性
const props = defineProps({
  // 业务属性
  bType: {
    type: String,
    default: 'default'
  },
  headId:{
    type: String,
    required: true,
    default: ''
  },
  isShow:{
    type: Boolean,
    default: false
  }
})



const fileList = ref([])
const uploading = ref(false)


/* 显示预览 */
const showPreview = ref(false)
/* 预览文件列表 */
const preViewFileList = ref([])

/* 筛选出上传成功的文件 */
const uploadedFiles = computed(() => {
  console.log('fileList.value', fileList.value)
  return fileList.value.filter(file => file.status === 'done')
})

/* 预览文件 */
const handlerOpenPre = (file) => {
  // console.log('file', file)
  showPreview.value = true
  preViewFileList.value = [file]
}





/* 获取下载文件的列表数据 */
const getFileList = ()=>{
  getAttachFileListByType(props.bType,props.headId).then(res => {
    console.log('res', res.data)
    fileList.value = []
    // 将数值赋值给updatedFiles
    res.data.forEach(item=>{
      fileList.value.push({
        sid: item.sid,
        name: item.originFileName,
        status: 'done',
        percent: 100,
        url: item.fileName
      })
    })
  })
}

onMounted(()=>{
  getFileList()
})




/* 上传文件校验 */
const handleBeforeUpload = (file) => {
  const isLt50M = file.size / 1024 / 1024 < 50
  if (!isLt50M) {
    message.error('文件大小不能超过50MB!')
    return false
  }
  return true
}



/* 处理上传请求 */
const customUploadRequest = async (options) => {
  const { file, onProgress, onSuccess, onError } = options
  const uid = file.uid

  try {
    uploading.value = true

    // 创建新文件对象并加入列表
    const newFile = {
      uid: uid,
      name: file.name,
      status: 'uploading',
      percent: 0,
      rawFile: file,
      url: '' // 初始化url字段
    }
    fileList.value = [...fileList.value, newFile]

    // 创建表单数据
    const formData = new FormData()
    formData.append('file', file)
    formData.append('businessType', props.bType)
    formData.append('businessSid', props.headId)
    formData.append('acmpNo', '')
    formData.append('acmpType', '')


    // 执行上传请求
    const res = await uploadAttachFile(formData)
    console.log('上传接口返回数据:', res)
    if (res.code !== 200) {
      throw new Error(res.message)
    }

    // 正确更新文件状态（保持对象引用）
    fileList.value = fileList.value.map(item => {
      if (item.uid === uid) {
        return {
          ...item,
          status: 'done',
          url: res, // 适配不同返回格式
          percent: 100
        }
      }
      return item
    })

    console.log('ada,fileList', fileList.value)

    message.success(`${file.name} 上传成功`)
    onSuccess()
    // 重新获取已上传文件列表
    getFileList()

  } catch (error) {
    fileList.value = fileList.value.map(item => {
      if (item.uid === uid) {
        return { ...item, status: 'error' }
      }
      return item
    })
    message.error(`${file.name} 上传失败`)
    onError(error)
  } finally {
    uploading.value = false
  }
}



/* 删除文件 */
const handleRemove = (file) => {
  console.log('删除文件', file)
  // 执行删除操作
  // ...
  let param = [file.sid]
  deleteAttachFile(param).then(res=>{
    // console.log('res', res)
    if (res.code === 200){
      message.success('删除成功!')
    }else {
      message.error("删除文件失败!")
    }
    getFileList()
  })
}



/* 下载文件 */
const downloadFileLoading = ref(false)
const handleDownload = (record) => {
  if (!isNullOrEmpty(record.sid)) {
    downloadFileLoading.value = true
    // // if (ieMark.value === 'E') {
    // //   postUrl = entryApi.entryEOtherUrl.get_entry_file
    // // }
    // window.majesty.httpUtil.downloadFile(`${postUrl}/${record.sid}`
    //   , record.fileName, '', 'get', '', '')
    //   .then((res) => {
    //     if (!isNullOrEmpty(res.message)) {
    //      message.error(res.message)
    //     }
    //   })
    // 类似getPreviewAttach的实现，调用下载API
    let
      fileType = '',
      fileName =  record.name
    if (!isNullOrEmpty(record.sid) && !isNullOrEmpty(fileName)) {
      if (fileName.endsWith('pdf')) {
        fileType = 'application/pdf'
      } else if (fileName.endsWith('xlsx') || fileName.endsWith('xls')) {
        fileType = 'application/vnd.ms-excel'
      }else {
        fileType = 'application/octet-stream'
      }
      window.majesty.httpUtil.getAction(ycCsApi.baseAttach.getAttachFile + `/${record.sid}`).then(res => {
        // console.log('1111',res)
        const blob = getBlob(res, fileType)
        blobSaveFile(blob, fileName)
      }, () => {
      }).finally(()=>{
        downloadFileLoading.value = false
      })
    }else {
      downloadFileLoading.value = false
    }
  } else {
    message.info('当前没有可预览的文件！')
  }
}

const getBlob = (base64, contentType) => {
  const byteCharacters = atob(base64)
  const byteArrays = []

  for (let offset = 0; offset < byteCharacters.length; offset += 512) {
    const slice = byteCharacters.slice(offset, offset + 512)
    const byteNumbers = new Array(slice.length)
    for (let i = 0; i < slice.length; i++) {
      byteNumbers[i] = slice.charCodeAt(i)
    }
    byteArrays.push(new Uint8Array(byteNumbers))
  }

  return new Blob(byteArrays, { type: contentType })
}


// 移除原来的handleChange处理
const handleChange = () => {} // 不再需要
</script>

<style lang="less" scoped>
/* 保持样式不变 */
//.file-upload-container {
//  //width: 100%;
//  //max-width: 600px;
//}

.upload-icon {
  font-size: 12px;
  color: #1890ff;
  margin-bottom: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  //border: 1px solid #d9d9d9;
  border-radius: 4px;
  margin-bottom: 8px;
  height: 12px;
  min-height: 12px;
}

.file-content{
  display: flex;
  justify-content: left;
  align-items: center;
  .file-list-left{
    button{
      color: #1677ff;
      font-weight: 600;
    }
  }
  .file-list-right {
    margin-top: 5px;
    //display: flex;
    //justify-content: center;
    //align-items: center;
    font-size: 14px;
    flex-direction: column;
    text-align: left;
    width: 400px;

    .file-item{
      color: #1677ff;
    }
  }
}


.file-info {
  flex: 1;
  display: flex;
  align-items: center;
  min-width: 0;
}

.file-icon {
  margin-right: 8px;
  color: rgba(0, 0, 0, 0.45);
}

.file-name {
  cursor: pointer;
  color: #1890ff;
  margin-right: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-name:hover {
  color: #40a9ff;
}

.delete-icon {
  color: rgba(0, 0, 0, 0.45);
  cursor: pointer;
  padding: 4px;
}

.delete-icon:hover {
  color: #ff4d4f;
}

.error-text {
  color: #ff4d4f;
}
</style>
