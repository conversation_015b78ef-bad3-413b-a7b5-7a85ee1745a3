<template>
  <a-modal
    v-model:visible="props.modelValue"
    title="文件预览"
    :footer="null"
    :closable="true"
    :maskClosable="false"
    wrapClassName="file-preview-modal"
    width="100%"
    style="top: 0; padding: 0"
  >
    <template #closeIcon>
      <close-circle-outlined style="font-size: 16px" @click="handleClose" class="modal-close-icon" />
    </template>

    <div class="preview-container">
      <a-card class="preview-content" :style="{ width: leftWidth + 'px' }">
        <embed
          v-if="isPdf"
          :src="fileUrl"
          type="application/pdf"
          class="preview-embed"
          :style="{ height: autoHeight + 'px' }"
        />
        <!-- 可根据需要添加Excel预览功能 -->
      </a-card>

      <a-card class="file-list" :style="{ width: rightWidth + 'px' }">
        <div class="file-list-header">文件列表</div>
        <ul class="file-list-content">
          <li v-for="item in fileData" :key="item.sid" class="file-item">
            <span
              :class="[
                'file-name',
                { 'non-pdf': !item.name.endsWith('pdf') }
              ]"
              @click="getPreviewAttach(item)"
            >
              {{ item.name }}
            </span>
            <download-outlined class="download-icon" @click="downLoad(item)" />
          </li>
        </ul>
      </a-card>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, computed, watch, onUnmounted } from 'vue'
import { DownloadOutlined, CloseCircleOutlined } from '@ant-design/icons-vue'
// import XLSX from 'xlsx'

defineOptions({
  name: 'FilePreviewModal'
})

import { message } from 'ant-design-vue'
import {blobSaveFile, isNullOrEmpty} from "@/view/utils/common";
import ycCsApi from "@/api/ycCsApi";

const props = defineProps({
  modelValue: Boolean,
  fileData: {
    type: Array,
    default: () => []
  },
  customizeUrl: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue'])

// 响应式状态
const fileUrl = ref('')
const isPdf = ref(false)
const leftWidth = ref(800)
const rightWidth = ref(300)
const autoHeight = ref(800)

// 工具函数
const getFileType = (fileName) => {
  if (fileName.endsWith('pdf')) return 'application/pdf'
  if (fileName.endsWith('xlsx') || fileName.endsWith('xls')) {
    return 'application/vnd.ms-excel'
  }
  return ''
}

// 计算属性
// const getPdfUrl = computed(() => {
//   return props.customizeUrl || `${ycCsApi.baseAttach.getAttachFile}` // 替换为实际API地址
// })

// 方法
const getBlob = (base64, contentType) => {
  const byteCharacters = atob(base64)
  const byteArrays = []

  for (let offset = 0; offset < byteCharacters.length; offset += 512) {
    const slice = byteCharacters.slice(offset, offset + 512)
    const byteNumbers = new Array(slice.length)
    for (let i = 0; i < slice.length; i++) {
      byteNumbers[i] = slice.charCodeAt(i)
    }
    byteArrays.push(new Uint8Array(byteNumbers))
  }

  return new Blob(byteArrays, { type: contentType })
}

const getPreviewAttach = async (file) => {
  // console.log('dsaaaaaaaaaaaaa', file)
  if (!file?.sid || !file?.name) return

  isPdf.value = false
  const fileName = file.name
  const fileType = getFileType(fileName)

  try {
    window.majesty.httpUtil.getAction( ycCsApi.baseAttach.getAttachFile+ `/${file.sid}`).then(res => {

      // console.log('获取文件信息', res)
      // if (!response.ok) throw new Error('文件获取失败')
      // if (res.code !== 200) {
      //   throw new Error('文件获取失败')
      // }

      const data = JSON.parse(JSON.stringify(res))
      const blob = getBlob(data, fileType)
      isPdf.value = fileName.endsWith('pdf')
      fileUrl.value = URL.createObjectURL(blob)
      // 更新布局尺寸
      updateLayoutDimensions()
    })


  } catch (error) {
    message.error('文件预览加载失败')
  }
}

const downLoad = async (file) => {
  // 类似getPreviewAttach的实现，调用下载API
  let
    fileType = '',
    fileName = file['name']
  if (!isNullOrEmpty(file.sid) && !isNullOrEmpty(fileName)) {
    if (fileName.endsWith('pdf')) {
      fileType = 'application/pdf'
    } else if (fileName.endsWith('xlsx') || fileName.endsWith('xls')) {
      fileType = 'application/vnd.ms-excel'
    }else {
      fileType = 'application/octet-stream'
    }
    window.majesty.httpUtil.getAction(ycCsApi.baseAttach.getAttachFile + `/${file.sid}`).then(res => {
      // console.log('1111',res)
        const blob = getBlob(res, fileType)
        blobSaveFile(blob, fileName)
    }, () => {
    })
  }
}

const handleClose = () => {
  emit('update:modelValue', false)
}

const updateLayoutDimensions = () => {
  leftWidth.value = Math.floor(window.innerWidth * 0.7)
  rightWidth.value = Math.floor(window.innerWidth * 0.25)
  autoHeight.value = Math.floor(window.innerHeight * 0.8)
}

// 生命周期
watch(() => props.modelValue, (show) => {
  if (show) {
    updateLayoutDimensions()
    window.addEventListener('resize', updateLayoutDimensions)
    if (props.fileData.length > 0) {
      // 找到第一个pdf文件并预览
      const pdfFile = props.fileData.find(file => file.name.endsWith('pdf'))
      if (pdfFile) {
        getPreviewAttach(pdfFile)
      } else {
        // message.warning('未找到PDF文件')
        console.log('未找到PDF文件')
        fileUrl.value = ''
      }
      // getPreviewAttach(props.fileData[0])
    }
  } else {
    window.removeEventListener('resize', updateLayoutDimensions)
  }
},{immediate:true})

onUnmounted(() => {
  if (fileUrl.value) {
    URL.revokeObjectURL(fileUrl.value)
  }
})


</script>

<style lang="less" scoped>
.ant-modal .ant-modal-content {
  position: relative;
  background-color: #ffffff;
  background-clip: padding-box;
  border: 0;
  border-radius: 8px;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  pointer-events: auto;
  height: 100vh;
  padding: 20px 24px;
}
.file-preview-modal .ant-modal-content {
  height: 100vh;
  padding: 24px;
}

.preview-container {
  display: flex;
  gap: 16px;
  height: calc(100vh - 108px);
}

.preview-content {
  flex: 1;
  height: 100%;
  overflow: hidden;
}

.preview-embed {
  width: 100%;
  height: 100%;
  border: none;
}

.file-list {
  height: 100%;
  overflow-y: auto;
}

.file-list-header {
  font-weight: 500;
  margin-bottom: 12px;
  color: rgba(0, 0, 0, 0.85);
}

.file-list-content {
  padding: 0;
  margin: 0;
  list-style: none;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  margin: 4px 0;
  border-radius: 4px;
  transition: background 0.3s;
}

.file-item:hover {
  background: #f5f5f5;
}

.file-name {
  cursor: pointer;
  color: #1890ff;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-name.non-pdf {
  color: rgba(0, 0, 0, 0.45);
  cursor: not-allowed;
}

.download-icon {
  color: rgba(0, 0, 0, 0.45);
  cursor: pointer;
  margin-left: 8px;
}

.download-icon:hover {
  color: #1890ff;
}

.modal-close-icon {
  font-size: 22px;
  color: rgba(0, 0, 0, 0.45);
  transition: color 0.3s;
}

.modal-close-icon:hover {
  color: rgba(0, 0, 0, 0.85);
}
</style>
