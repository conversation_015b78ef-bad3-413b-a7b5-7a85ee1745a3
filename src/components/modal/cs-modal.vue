<template>
  <a-modal
    style="padding: 24px 10px"
    bodyStyle="height: 60%"
    :visible="visible"
    :title="title"
    :width="width"
    :destroyOnClose="true"
    @cancel="handleCancel"
    :footer="footer ? undefined : null"
  >
    <!-- 自定义内容插槽 -->
    <slot v-if="$slots.customContent" name="customContent"></slot>

    <a-form
      v-else
      ref="formRef"
      :model="formState"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
      :rules="mergedRules"
    >
      <template v-for="field in mergedFields" :key="field.name">
        <!-- 动态表单项 -->
        <a-form-item
          :label="field.label"
          :name="field.name"
          v-bind="field.itemProps"
        >
          <component
            :is="getComponentType(field)"
            v-model:value="formState[field.name]"
            v-bind="getComponentProps(field)"
            :disabled="readonly || field.disabled"
          />
        </a-form-item>
      </template>
    </a-form>

    <!-- 自定义底部插槽 -->
    <template v-if="$slots.footer" #footer>
      <slot name="footer"></slot>
    </template>
    <template v-if="footer === null">
      <div style="display: flex;justify-content: right;align-items: center">
        <a-button @click="handleCancel" size="small">取消</a-button>
        <a-button
          style="margin-left: 8px"
          size="small"
          type="primary"
          @click="handleSubmit"
          :loading="confirmLoading"
        >确定</a-button>
      </div>

    </template>
  </a-modal>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue';
import { message } from 'ant-design-vue';

defineOptions({
  name: 'CsModal'
})

const props = defineProps({
  visible: {
    type: Boolean,
    required: true
  },
  title: {
    type: String,
    default: ''
  },
  width: {
    type: Number,
    default: 600
  },
  initialValues: {
    type: Object,
    default: () => ({})
  },
  readonly: {
    type: Boolean,
    default: false
  },
  footer: {
    type: Boolean,
    default: true
  },
  fields: {
    type: Array,
    default: () => []
  },
  rules: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['submit', 'cancel']);

// 默认字段配置
const defaultFields = [

];

// 合并默认字段和自定义字段
const mergedFields = computed(() =>
  props.fields.length > 0 ? props.fields : defaultFields
);

// 合并验证规则
const mergedRules = computed(() => {
  const defaultRules = {};
  defaultFields.forEach(field => {
    if(field.rules) defaultRules[field.name] = field.rules;
  });
  return { ...defaultRules, ...props.rules };
});

// 表单引用
const formRef = ref(null);
// 表单状态
const formState = reactive({ ...props.initialValues });
// 确认按钮加载状态
const confirmLoading = ref(false);

// 布局配置
const labelCol = { span: 6 };
const wrapperCol = { span: 16 };

// 获取组件类型
const getComponentType = (field) => {
  if (typeof field.component === 'string') return field.component;
  return 'a-input'; // 默认使用输入框
};

// 获取组件props
const getComponentProps = (field) => {
  return field.componentProps || {};
};

// 提交处理
const handleSubmit = async () => {
  try {
    await formRef.value.validate();
    confirmLoading.value = true;
    emit('submit', { ...formState });
  } catch (error) {
    console.error('表单验证失败:', error);
  } finally {
    confirmLoading.value = false;
  }
};

// 取消处理
const handleCancel = () => {

  // formRef.value.resetFields();

  emit('cancel');
};

// 监听初始值变化
watch(() => props.initialValues, (newVal) => {
  Object.assign(formState, newVal);
}, { deep: true });
</script>

<style lang="less" scoped>
/* 自定义样式 */
.ant-form-item {
  margin-bottom: 16px;
}

</style>
