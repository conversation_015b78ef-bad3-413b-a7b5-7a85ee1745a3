<template>
  <section>
    <a-card :bordered="false">
      <s-table
        ref="aeoTable"
        class="cs-action-item remove-table-border-add-bg"
        size="small"
        bordered
        :columns="aeoColumns"
        :data-source="dataAeoSource"
        row-key="apprDate"
        :pagination="false"
      />
    </a-card>
  </section>
</template>

<script setup>
import { ref, watch, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import ycCsApi from '@/api/ycCsApi'

// props
const props = defineProps({
  sid: {
    type: String,
    required: true
  },
})

// 审批数据
const dataAeoSource = ref([])

// 列定义
const aeoColumns = computed(() => [
  {
    width: 160,
    ellipsis: true,
    align: 'center',
    dataIndex: 'statusName',
    title: '节点'
  },
  {
    width: 160,
    ellipsis: true,
    align: 'center',
    dataIndex: 'userName',
    title: '操作人'
  },
  {
    width: 160,
    ellipsis: true,
    align: 'center',
    dataIndex: 'apprDate',
    title: '时间'
  },
  {
    ellipsis: true,
    align: 'center',
    dataIndex: 'apprNote',
    title: '内审意见'
  }
])

// 获取数据
const getAeoData = async (sid) => {
  if (!sid) {
    dataAeoSource.value = []
    return
  }
  let url = ycCsApi.aeoManage.aeoReview.selectListBySid

  try {
    const res = await window.majesty.httpUtil.postAction(url, { businessSid: sid })
    dataAeoSource.value = res

  } catch (e) {
    dataAeoSource.value = []
    message.error('获取内审信息失败')
  }
}

// 监听sid变化
watch(() => props.sid, (val) => {
  getAeoData(val)
})

// 首次加载
onMounted(() => {
  if (props.sid) {
    getAeoData(props.sid)
  }
})


</script>

<style lang="less" scoped>
.s-table .ant-table-placeholder {
  min-height: 100px;
}
</style>
