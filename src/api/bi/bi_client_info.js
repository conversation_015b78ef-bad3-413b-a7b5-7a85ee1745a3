import {deleteAction, getAction, postAction, putAction} from "@/api/manage";
import CsConstant from "@/api/CsConstant";
import ycCsApi from "@/api/ycCsApi";


// 海关自定义信息列表
export const insertBaseInfoCustomerParams = (params) =>window.majesty.httpUtil.postAction(ycCsApi.baseInfoCustomerParams.insert, params)
export const updateBaseInfoCustomerParams = (sid,params) =>window.majesty.httpUtil.putAction(`${ycCsApi.baseInfoCustomerParams.update}/${sid}`, params)
export const deleteBaseInfoCustomerParams = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.baseInfoCustomerParams.update}/${sids}`)


// 客户基础信息列表
export const insertClient = (params) =>window.majesty.httpUtil.postAction(ycCsApi.biClientInfo.insert, params)
export const updateClient = (sid,params) =>window.majesty.httpUtil.putAction(`${ycCsApi.biClientInfo.update}/${sid}`, params)
export const deleteClient = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.biClientInfo.update}/${sids}`)

// 客商信息列表
export const MerchantInsertClient = (params) =>window.majesty.httpUtil.postAction(ycCsApi.bizMerchant.insert, params)
export const MerchantUpdateClient = (sid,params) =>window.majesty.httpUtil.putAction(`${ycCsApi.bizMerchant.update}/${sid}`, params)
export const MerchantDeleteClient = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.bizMerchant.update}/${sids}`)
export const MerchantGetMerchantCodeClient = (params) =>window.majesty.httpUtil.postAction(ycCsApi.bizMerchant.getMerchantCode, params)
// 物料信息列表
export const MaterialInformationInsertClient = (params) =>window.majesty.httpUtil.postAction(ycCsApi.bizMaterialInformation.insert, params)
export const MaterialInformationUpdateClient = (sid,params) =>window.majesty.httpUtil.putAction(`${ycCsApi.bizMaterialInformation.update}/${sid}`, params)
export const MaterialInformationDeleteClient = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.bizMaterialInformation.update}/${sids}`)
export const MaterialInformationCancelClient = (sids) => window.majesty.httpUtil.postAction(`${ycCsApi.bizMaterialInformation.cancel}/${sids}`)
export const getMerchantCodeValueClient = () => window.majesty.httpUtil.postAction(ycCsApi.bizMaterialInformation.getMerchantCodeValue)

/* shipfrom 方法 */

export const insertShipfrom = (params) => window.majesty.httpUtil.postAction(ycCsApi.biShipfrom.insert, params)
export const updateShipfrom = (sid,params) => window.majesty.httpUtil.putAction(`${ycCsApi.biShipfrom.update}/${sid}`, params)
export const deleteShipfrom = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.biShipfrom.update}/${sids}`)

/* 获取自定义配置信息 */
export const saveCustomWithDataId = (params)=>window.majesty.httpUtil.postAction(CsConstant.PREFIX_SYSTEM + '/sysCustom/saveCustomWithDataId', params)
export const getCustomVaueByTypeAndDataId = (params) =>window.majesty.httpUtil.getAction(CsConstant.PREFIX_SYSTEM + '/sysCustom/getCustomVaueByTypeAndDataId', params)
