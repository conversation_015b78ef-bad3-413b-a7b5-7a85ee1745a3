import { axios } from '@/api/request'
import CsConstant from "@/api/CsConstant";


//post
export function postAction(url, parameter) {
  return axios({
    url: url,
    method: 'post',
    data: parameter
  })
}

//post method= {post | put}
export function httpAction(url, parameter, method) {
  return axios({
    url: url,
    method: method,
    data: parameter
  })
}

//put
export function putAction(url, parameter) {
  return axios({
    url: url,
    method: 'put',
    data: parameter
  })
}

//get
export function getAction(url, parameter) {
  return axios({
    url: url,
    method: 'get',
    params: parameter
  })
}

//deleteAction
export function deleteAction(url, parameter) {
  return axios({
    url: url,
    method: 'delete',
    params: parameter
  })
}

/**
 * 下载文件 用于excel导出
 * @param url
 * @param parameter
 * @returns {*}
 */
export function downFile(url, parameter, method){
  return axios({
    url: url,
    params: (!method || method.toLowerCase() == "get") ? parameter : {},
    data: (method && method.toLowerCase() == "post") ? parameter : {},
    method: method || 'get',
    responseType: 'blob'
  })
}

/**
 * 下载文件 用于excel导出
 * @param url
 * @param parameter
 * @returns {*}
 */
function downFileIncludeHeader(url,parameter, method){
  return axios({
    url: url,
    params: (!method || method.toLowerCase() == "get") ? parameter : {},
    data: (method && method.toLowerCase() == "post") ? parameter : {},
    method: method || 'get',
    responseType: 'blob',
    includeHeader: true
  })
}

function getHttpHeaderFileName(headers){
  let filename = ''
  if (!headers) {
    return filename
  }
  const disposition = headers['content-disposition']
  if (disposition && disposition.indexOf('attachment') !== -1) {
    let filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
    let matches = filenameRegex.exec(disposition);
    if (matches && matches[1]) {
      filename = matches[1].replace(/['"]/g, '');
      return decodeURIComponent(filename)
    }
  }
  return filename
}

/**
 * 下载文件
 * @param url 文件路径
 * @param fileName 文件名
 * @param parameter
 * @returns {*}
 */
export function downloadFile(url, fileName, parameter, method, callback) {
  return downFileIncludeHeader(url, parameter, method).then((res) => {
    if (!res || !res.data || res.data.size === 0) {
      // MessageUtil.warning(LocaleUtil.getLocale('m.eform.formDesign.label.wen-jian-xia-zai-shi-bai'))
      if (callback) {
        callback()
      }
      return
    }
    if(!fileName){
      fileName = getHttpHeaderFileName(res.headers)
    }
    if (typeof window.navigator.msSaveBlob !== 'undefined') {
      window.navigator.msSaveBlob(new Blob([res.data]), fileName)
    } else {
      let url = window.URL.createObjectURL(new Blob([res.data]))
      let link = document.createElement('a')
      link.style.display = 'none'
      link.href = url
      link.setAttribute('download', fileName)
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link) //下载完成移除元素
      window.URL.revokeObjectURL(url) //释放掉blob对象
    }
    if (callback) {
      callback()
    }
  })
}



/**
 * 文件上传 用于富文本上传图片
 * @param url
 * @param parameter
 * @returns {*}
 */
export function uploadAction(url,parameter){
  return axios({
    url: url,
    data: parameter,
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data',  // 文件上传
    },
  })
}
