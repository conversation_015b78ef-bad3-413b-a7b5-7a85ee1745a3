import {deleteAction, getAction, postAction, putAction} from "@/api/manage";
import CsConstant from "@/api/CsConstant";
import ycCsApi from "@/api/ycCsApi";
import params from "@/view/params";


// 外商合同信息列表
export const insertContract = (params) =>window.majesty.httpUtil.postAction(ycCsApi.auxiliaryMaterials.bizIAuxMatForContractHead.insert, params)
export const updateContract = (sid,params) =>window.majesty.httpUtil.putAction(`${ycCsApi.auxiliaryMaterials.bizIAuxMatForContractHead.update}/${sid}`, params)
export const deleteContract = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.auxiliaryMaterials.bizIAuxMatForContractHead.delete}/${sids}`)

export const addContractList = (params) =>window.majesty.httpUtil.postAction(ycCsApi.auxiliaryMaterials.bizIAuxMatForContractList.batchInsert, params)
export const updateContractList = (sid,params) =>window.majesty.httpUtil.putAction(`${ycCsApi.auxiliaryMaterials.bizIAuxMatForContractList.update}/${sid}`, params)
export const deleteContractList = (sids) => window.majesty.httpUtil.deleteAction(`${ycCsApi.auxiliaryMaterials.bizIAuxMatForContractList.delete}/${sids}`)
export const confirmContract = (params) => window.majesty.httpUtil.postAction(ycCsApi.auxiliaryMaterials.bizIAuxMatForContractHead.confirm, params)
export const copyContract = (params) => window.majesty.httpUtil.postAction(ycCsApi.auxiliaryMaterials.bizIAuxMatForContractHead.copy, params)
export const sendAudit = (params) => window.majesty.httpUtil.postAction(ycCsApi.auxiliaryMaterials.bizIAuxMatForContractHead.sendAudit, params)
export const invalidContract = (params) => window.majesty.httpUtil.postAction(ycCsApi.auxiliaryMaterials.bizIAuxMatForContractHead.cancel, params)
export const checkContract = (params) => window.majesty.httpUtil.postAction(ycCsApi.auxiliaryMaterials.bizIAuxMatForContractHead.checkStatus, params)
export const getContractTotal = (params) => window.majesty.httpUtil.postAction(ycCsApi.auxiliaryMaterials.bizIAuxMatForContractList.getContractTotal, params)


/* 获取自定义配置信息 */
export const saveCustomWithDataId = (params)=>window.majesty.httpUtil.postAction(CsConstant.PREFIX_SYSTEM + '/sysCustom/saveCustomWithDataId', params)
export const getCustomVaueByTypeAndDataId = (params) =>window.majesty.httpUtil.getAction(CsConstant.PREFIX_SYSTEM + '/sysCustom/getCustomVaueByTypeAndDataId', params)
