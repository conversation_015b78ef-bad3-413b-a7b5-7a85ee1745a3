{"m": {"logo": {"full_name": "แพลตฟอร์มคลาวด์ลอยน้ำ", "simply_name": "เมฆลอยน้ำ"}, "common": {"button": {"add": "ใหม่", "add_yellow": "新增(黄色)", "addRedirect": "新增跳转", "update": "แก้ไข", "update_blue": "编辑(蓝色)", "updateRedirect": "แก้ไขการกระโดด", "delete": "ลบ", "delete_orange": "删除(橙色)", "batch_delete": "การลบแบทช์", "import": "นำเข้า", "import-xdo": "นำเข้า (รองรับ xdo)", "handleAsyncImport": "异步导入", "handleImportAddXls": "异步新增导入", "handleImportUpdateXls": "异步修改导入", "handleImportDeleteXls": "异步删除导入", "handleImportSaveXls": "异步新增和修改导入", "import_red": "导入(红色)", "export": "ส่งออก", "habit_export": "自定义顺序导出", "publish_version": "版本发布", "roll_back": "ย้อนกลับ", "export_green": "导出(绿色)", "editExport": "ปรับเปลี่ยนการส่งออก", "exportDetail": "导出明细", "allExport": "ส่งออกทั้งหมด", "selectedExport": "ตรวจสอบการส่งออก", "exportasync": "การส่งออกแบบอะซิงโครนัส", "query": "สอบถาม", "reset": "รีเซ็ต", "selectAll": "เลือกทั้งหมด", "byUser": "用户级", "byEnterprise": "企业级", "close": "ปิด", "ok": "ระบุ", "cancel": "การยกเลิก", "select": "เลือก", "upload": "อัพโหลด", "browse": "浏览", "selectFile": "选择文件", "selectPicture": "选择图片", "more": "มากกว่า", "save": "บันทึก", "back": "กลับ", "config": "การกำหนดค่า", "setting": "ติดตั้ง", "clear": "ชัดเจน", "enable": "เปิดใช้งาน", "disable": "ปิดการใช้งาน", "refresh": "รีเฟรช", "related": "查看关联项", "relatedTableButton": "Related table button", "relatedViews": "Related views", "relatedScripts": "Related scripts", "expand": "ขยาย", "fold": "ปิด", "open": "เปิด", "see": "ตรวจสอบ", "detail": "รายละเอียด", "detailRedirect": "详细跳转", "addFlow": "发起流程", "detailFlow": "流程详细", "openSubView": "ดูตารางย่อย", "custom": "自定义", "takeData": "提取数据", "addPreValidate": "添加前置校验", "otherUpload": "接口上传", "closeList": "关闭列表", "riskWarn": "风险预警", "otherUpload_valid": "请配置接口上传按钮动作", "design": "设计", "test": "ทดสอบ", "selectDbTable": "从数据库选择表", "selectApp": "选择应用", "selectEntityTable": "从实体模型选择表", "setCountSql": "配置countSql", "parseSql": "解析SQL", "copy": "สำเนา", "copy_purple": "复制(紫色)", "upload_pic": "อัพโหลดภาพ", "editSourceFile": "编辑源文件", "editScript": "编辑脚本", "preview": "ดูตัวอย่าง", "restore": "ฟื้นตัว", "pre_form_design": "Preview form design", "initializeInterface": "初始化接口", "selectTableGenInterface": "选择表生成接口", "addInterface": "新增接口", "document": "文档", "tuo-dong": "拖动", "close_left": "ปิดทางซ้าย", "close_right": "ปิดทางขวา", "close_other": "ปิดอื่นๆ", "column_detail": "รายละเอียดสนาม", "change": "แปลง", "openView": "เปิดหน้า", "defalut_click": "คลิกเริ่มต้น", "set_parameter": "การตั้งค่าพารามิเตอร์", "zan_cun": "การจัดเก็บชั่วคราว", "move": "เคลื่อนไหว", "initAppLocale": "init app locale", "existSimilarityLocale": "exist similarity locale", "initAppLocale_confirm": "confirm init locale", "stillSaveLocale": "still save locale", "bu-chong-kong-lan-wei": "补充空栏位", "quan-liang-fu-gai": "全量覆盖", "custom_report": "自定义报表", "expand_setting": "拓展栏位设置", "save_template": "保存模板", "del_template": "删除模板"}, "title": {"add": "ใหม่", "update": "แก้ไข", "detail": "รายละเอียด", "delete": "ยืนยันการลบ", "operate": "พร้อมท์การยืนยัน", "error": "ความผิดพลาด", "warning": "เตือน"}, "label": {"number": "หมายเลขซีเรียล", "operate": "ดำเนินงาน", "columnsDetail": "字段详情", "match": "对应", "status": "状态", "sort": "排序", "fileName": "ชื่อไฟล์", "arrangeName": "业务服务名称", "arrangeDescription": "业务服务描述", "whether_use": "是否使用", "enableCite": "允许其他app调用", "citedIds": "Exec other app business after current business", "notification": "弹窗内容赋值字段", "notificationTitle": "弹窗内容标题", "notificationLength": "限制内容长度", "notificationLimit": "内容长度限制位数:", "addBusinessService": "新增业务服务", "arrangeId": "业务Id", "businessArrange": "业务编排", "asyncImport": "异步导入", "initializeScript": "初始化脚本", "commonScript": "公共脚本", "appAql": "SQL配置", "arrangeComponent": "编排组件", "switchConfirm": "页面还未保存,是否切换编排组件?", "copy": "复制", "page_record_num": " ทั้งหมด {0} รายการ", "yes": "是", "no": "否", "parameter_key": "参数名", "parameter_value": "参数值", "prompt": "คำใบ้", "all": "全部", "part": "部分", "begin_time": "开始时间", "end_time": "结束时间", "begin_date": "开始日期", "end_date": "结束日期", "upload_time": "เวลาอัพโหลด", "uploader": "ผู้อัปโหลด", "to_date": "至", "year": "年", "month": "月", "days": "天", "day": "日", "hours": "小时", "hours_short": "时", "minutes": "分钟", "minutes_short": "分", "seconds": "秒", "millisecond": "毫秒", "custom_SQL": "自定义SQL", "count_SQL": "count语句SQL", "option_select": "请选择", "label_color_placeholder": "取值同CSS Color属性", "option_select_dialog": "请选择弹框大小", "Monday": "星期一", "Tuesday": "星期二", "Wednesday": "星期三", "Thursday": "星期四", "Friday": "星期五", "Saturday": "星期六", "Sunday": "星期天", "code": "编码", "Chinese": "中文", "English": "英文", "Thai": "泰文", "app": "应用", "team": "分类", "Maintain_data": "维护数据字典", "interfaceName": "接口名称", "page_total": "{0}-{1} 共 {2} 条", "statusUpdate": "状态更新", "syncData": "同步", "statistics": "统计", "sysDefault": "default", "sysTenant": "tenant", "sysComp": "company"}, "tip": {"delete_confirm": "แน่ใจที่จะลบประวัติ?", "delete_confirm2": "ตรวจสอบรายการ {0} ร่วมกันว่าจะลบข้อมูลที่เลือกหรือไม่?", "save_confirm": "แน่ใจที่จะบันทึก?", "locale_cover": "是否覆盖多语言数据?", "generate_confirm": "แน่ใจสร้างและเขียนทับสคริปต์?", "close_confirm": "แน่ใจ ปิด?", "enable_confirm": "แน่ใจว่าจะเปิดใช้งาน?", "disable_confirm": "ระบุปิดการใช้งาน?", "select_one_warn": "กรุณาเลือกบันทึก", "view_redirect_id_not_found": "ดู redirect id ไม่ได้ตั้งค่า", "select_one_only": "สามารถเลือกบันทึกได้เพียงรายการเดียว", "pic_warn": "กรุณาอัพโหลดรูปภาพ", "length_warn": "ความยาวไม่เกิน {0} ตัวอักษร", "execute_success": "ความสำเร็จในการดำเนินการ", "operate_success": "ความสำเร็จในการดำเนินงาน", "operate_error": "การดำเนินการล้มเหลว", "check_success": "เสร็จสิ้นการตรวจสอบความจริงสามารถเพลิดเพลินกับบริการโลมาการค้าต่างประเทศฟรีหลังจากดาวน์โหลดข้อมูล!", "check_error": "ข้อมูลการลงทะเบียนไม่ตรงกับข้อมูลบัตร กรุณาเสียบบัตรใหม่!", "return_data_null": "ข้อมูลกลับว่างเปล่า", "begin_date_tip": "โปรดเลือกวันที่เริ่มต้น", "end_date_tip": "กรุณาเลือกวัน", "file_upload_sucess": "อัปโหลดไฟล์สำเร็จ", "file_upload_error": "การอัพโหลดไฟล์ล้มเหลว", "file_uploading": "อัปโหลดไฟล์", "code_format": "การเข้ารหัสต้องไม่มีตัวอักษรจีน", "sql_warn": "โปรดป้อน SQL", "sql_ok": "ความสำเร็จในการวิเคราะห์", "verify_required": "ที่จำเป็น", "code_unique": "การเข้ารหัสไม่สามารถทำซ้ำได้", "code_required": "การเข้ารหัสต้องกรอก", "positive_digits_tip": "โปรดป้อนจำนวนเต็มบวก", "enter_script": "โปรดป้อนสคริปต์", "no_data_modification": "ไม่มีการแก้ไขข้อมูล", "cannotNull": "ไม่สามารถว่างได้", "disabled_current_action": "สภาพแวดล้อมปัจจุบันห้ามไม่ให้ดำเนินการ", "dict_code_null": "รหัสพจนานุกรมไม่สามารถว่างเปล่าได้!", "disabled_ti_yan": "ปิดการใช้งานในสภาพแวดล้อมประสบการณ์", "access_denied": "ปฏิเสธการเข้าถึง", "system_prompt": "เคล็ดลับระบบ", "unauthorized": "หมดเวลาเข้าสู่ระบบ กรุณาเข้าสู่ระบบอีกครั้ง", "multiple_login": "หมายเลขบัญชีของคุณได้เข้าสู่ระบบที่อื่นแล้วแก้ไขรหัสผ่านในเวลาที่เหมาะสมหากไม่ได้ดำเนินการโดยคุณ!", "resource_not_found": "ขออภัย ไม่พบแหล่งข้อมูล", "network_timeout": "หมดเวลาเครือข่าย", "morning1": "อรุณสวัสดิ์", "morning2": "อรุณสวัสดิ์", "afternoon1": "สวัสดีตอนเที่ยง", "afternoon2": "สวัสดีตอนบ่าย", "evening": "สวัสดีตอนเย็น", "deleteFileFailed": "การลบล้มเหลว", "checkFileSize": "{0} ใหญ่เกินไป ค่าสูงสุดที่รองรับคือ {1}M", "password_reminder": "การแจ้งเตือนล่วงหน้าของรหัสผ่าน", "to_change_password": "รหัสผ่านของคุณกำลังจะหมดอายุ คลิก OK เพื่อแก้ไขรหัสผ่านของคุณ"}, "popup": {"jiao-ben-hou": "打开弹出框：", "popup-setting": "弹出框设置", "popup-type": "弹出框类型", "popup-code": "弹出框编码", "popup-status": "弹出框状态", "popup-size": "弹出框大小", "large": "大", "middle": "中", "small": "小", "mini": "迷你", "auto": "自适应"}, "import": {"modal-title": "自定义导入配置", "enable": "是否启用", "system-code": "系统编码", "input-system-code": "请输入系统编码（systemCode）", "task-code": "任务编码", "input-task-code": "请输入任务编码（taskCode）", "param-setting": "参数设置", "title": "页面标题", "refreshParent": "关闭后是否刷新列表页面", "input-title": "请输入页面标题", "extendKey": "扩展主键", "input-extendKey": "请输入扩展主键（extendKey）（当任务编码和企业编码不足以控制任务数时，使用该字段)", "fieldsMapConfig": "开启自定义列配置", "baseUri": "api基路径", "input-baseUri": "请输入api基路径（baseUri）(需要独立部署到企业的产品,该属性必须重新配置)", "importKey": "上传控件id", "input-importKey": "请输入上传控件id（importKey）", "task-description": "任务描述", "import-start-line": "导入起始行", "multi-table-import": "启用多表导入", "extended-task": "多表导入任务", "main-child-relation": "主子表关联", "child_field": "子表字段", "please_child_field": "请选择子表字段", "main_foreign": "主表外键", "please_main_foreign": "请选择主表外键"}, "export": {"modal-title": "自定义导出配置", "enable": "是否启用", "url": "接口访问路径", "begins-with": "(以/开头)", "filename": "文件名称", "input-filename": "请输入文件名称（可为空）", "http-request-type": "Http请求类型", "param-setting": "参数设置", "carry-query-condition": "携带查询条件", "query-condition-format": "查询条件格式", "hump": "驼峰", "underline": "下划线", "scene-selection": "场景选择", "api": "API接口", "filter": "数据过滤", "filter-setting": "过滤设置", "col-name": "列名", "select-col-name": "请选择列名", "export-default": "筛选默认值", "input-export-default": "请输入筛选默认值"}, "other": {"bao-mu": "薄暮", "huo-shan": "火山", "ri-mu": "日暮", "ming-qing": "<PERSON>青", "ji-guang-lv": "极光绿", "fu-xiao-lan": "拂晓蓝", "ji-ke-lan": "极客蓝", "jiang-zi": "酱紫", "not-permission-page": "您无权访问该页面", "not-open": "ฟีเจอร์นี้ยังไม่พร้อมใช้งาน!"}, "table": {"noData": "no data"}}, "components": {"title": {"ychSelectUser": "根据部门选择用户", "ychSelectDepart": "选择部门", "columnSet": "列设置", "showSearchSettings": "开启设置查询列", "searchSettingsLevel": "设置查询列级别", "showColumnSettings": "开启设置列模板", "searchSettingId": "设置隐藏列唯一值"}, "label": {"columnShow": "列展示", "bu-men-xuan-ze-kong-jian": "部门选择控件", "qing-dian-ji-xuan-ze-bu-men": "请点击选择部门", "yong-hu-xuan-ze-kong-jian": "用户选择控件", "qing-dian-ji-xuan-ze-yong-hu": "请点击选择用户", "dian-ji-cha-kan-shi-yong-bang-zhu": "点击查看使用帮助", "zan-wu-bang-zhu-xin-xi": "暂无帮助信息", "shi-yong-bang-zhu": "使用帮助", "tree_valid_error": "组件JTreeSelect-condition传值有误，需要一个json字符串!", "wei-zhi-de-cao-zuo": "未知的操作", "yi-dao-zui-hou": "已到最后", "qing-yi-ci-dian-ji-thispointextlistjoin": "请依次点击【{0}】", "xiang-you-hua-dong-wan-cheng-yan-zheng": "ปัดไปทางขวาเพื่อทำการยืนยันให้เสร็จสิ้น", "dataSource": "数据源列", "isSelected": "已选中的列", "searchSettingId": "请输入唯一值"}, "verify": {"searchSettingId": "长度不能超过8位"}, "cron": {"title": "cron表达式", "seconds1": "每一秒钟", "seconds2": "秒执行", "seconds3": "秒开始", "seconds4": "具体秒数(可多选)", "minutes1": "每一分钟", "minutes2": "分执行", "minutes3": "分开始", "minutes4": "具体分钟数(可多选)", "hours1": "每一小时", "hours2": "小时执行", "hours3": "小时开始", "hours4": "具体小时数(可多选)", "days1": "每一天", "days2": "周执行", "days3": "开始", "days4": "天执行", "days5": "天开始", "days6": "具体星期几(可多选)", "days7": "具体天数(可多选)", "days8": "在这个月的最后一天", "days9": "在这个月的最后一个工作日", "days10": "在这个月的最后一个", "days11": "在本月底前", "days12": "最近的工作日（周一至周五）至本月", "days13": "在这个月的第", "days14": "个", "month1": "每一月", "month2": "月执行", "month3": "月开始", "month4": "具体月数(可多选)", "month5": "月之间的每个月", "year1": "每一年", "year2": "年执行", "year3": "年开始", "year4": "具体年份(可多选)", "year5": "年之间的每一年", "common1": "每隔", "common2": "周期从", "common3": "到", "common4": "从"}, "icon": {"title": "选择图标", "warnTitle": "请选择图标", "iconType1": "方向性图标", "iconType2": "指示性图标", "iconType3": "编辑类图标", "iconType4": "数据类图标", "iconType5": "网站通用图标", "iconType6": "品牌和标识", "iconType7": "IconFont(移动端)"}}, "system": {"login": {"entity": {"username": "บัญชี", "password": "รหัสผ่าน", "mobile": "หมายเลขโทรศัพท์", "captcha": "รหัสยืนยัน", "randomCode": "รหัสสุ่ม", "verify": "ตรวจสอบ", "complete": "เสร็จ", "captcha_short": "รหัสยืนยัน", "confirmpassword": "ยืนยันรหัสผ่าน", "username_phone": "บัญชี/หมายเลขโทรศัพท์มือถือ", "compCode": "กรอกรหัสศุลกากร 10 หลักของหน่วยธุรกิจ!", "compName": "กรุณากรอกชื่อบริษัท", "socialCreditCode": "โปรดป้อนรหัสเครดิตโซเชียลแบบรวม", "selectUserName": "กรุณาเลือกบัญชี"}, "verify": {"specific_template_name": "กรุณากรอกชื่อแม่แบบ", "mobile": "กรุณากรอกหมายเลขโทรศัพท์", "mobile_format": "รูปแบบหมายเลขโทรศัพท์มือถือของคุณไม่ถูกต้อง", "username": "กรุณากรอกชื่อผู้ใช้", "name": "กรุณากรอกชื่อผู้ใช้", "password": "กรุณากรอกรหัสผ่าน", "password_inconsistency": "รหัสผ่านสองตัวไม่สอดคล้องกัน", "confirmpassword": "กรุณากรอกรหัสผ่านยืนยัน", "mobile_placeholder": "หมายเลขโทรศัพท์มือถือ 11 หลัก", "placeholder_register": "ใช้เพื่อดึงรหัสผ่านของคุณ กรุณากรอกให้ถูกต้อง!", "captcha": "กรุณากรอกรหัสยืนยัน", "randomCode": "กรุณากรอกรหัสสุ่ม", "account_already_exists": "บัญชีมีอยู่แล้ว", "username_already_exists": "ชื่อผู้ใช้ได้รับการลงทะเบียนแล้ว!", "company_already_exists": "จดทะเบียนธุรกิจแล้ว!", "email_already_exists": "มีอีเมลอยู่แล้ว", "password_strength": "รหัสผ่านไม่แข็งแกร่งพอ", "register_fail": "การลงทะเบียนไม่สำเร็จ", "register_success": "บัญชีของคุณ: {0} การลงทะเบียนสำเร็จ", "register_login": "การลงทะเบียนสำเร็จ กรุณาเข้าสู่ระบบ!", "username_phone": "กรุณากรอกบัญชี/หมายเลขโทรศัพท์มือถือของคุณ", "username_notexist": "บัญชีไม่มีอยู่", "emial_notexist": "ไม่มีอีเมล", "verify_fail": "ข้อผิดพลาดในการตรวจสอบ", "change_password_fail": "การเปลี่ยนรหัสผ่านล้มเหลว", "change_password_success": "เปลี่ยนรหัสผ่านเรียบร้อยแล้ว", "compCode": "กรุณากรอกรหัสศุลกากร 10 หลักของหน่วยธุรกิจ!", "compCodeLength": "ความยาวศุลกากรของหน่วยธุรกิจคือ 10 หลัก!", "noRegister": "นายหน้าศุลกากรไม่สามารถลงทะเบียนได้!", "compName": "กรุณากรอกชื่อบริษัท", "compNameLength": "ชื่อบริษัทสามารถมีอักขระได้สูงสุด 50 ตัว", "socialCreditCode": "โปรดป้อนรหัสเครดิตโซเชียลแบบรวม", "socialCreditCodeLength": "ความยาวของรหัสเครดิตโซเชียลแบบรวมคือ 18 หลัก", "userNameLength": "ความยาวของชื่อผู้ใช้สามารถมีอักขระได้สูงสุด 20 ตัว", "realnameLength": "ความยาวของชื่อสามารถมีได้สูงสุด 10 หลัก", "mobileLength": "เบอร์โทรศัพท์มือถือยาว 11 หลัก", "emailLength": "ความยาวอีเมลสามารถมีอักขระได้สูงสุด 50 ตัว"}, "other": {"login_type1": "เข้าสู่ระบบด้วยบัญชีและรหัสผ่าน", "login_type2": "เข้าสู่ระบบรหัสยืนยันโทรศัพท์มือถือ", "captchaVerify": "ตรวจสอบรหัสยืนยัน", "getCaptcha": "รับรหัสยืนยัน", "getRandomCode": "รับรหัสสุ่ม", "alteration": "ลืมรหัสผ่าน", "register": "ลงทะเบียนบัญชี", "register_short": "ลงทะเบียน", "loginBtn": "เข้าสู่ระบบ", "deptSelect": "登录部门选择", "captchaSend": "กำลังส่งรหัสยืนยัน", "welcomeback": "ยินดีต้อนรับกลับ", "welcome": "ยินดีต้อนรับ", "loginFailed": "เข้าสู่ระบบล้มเหลว", "loginError": "请求出现错误，请稍后再试", "noDept": "您尚未归属部门,请确认账号信息", "verifybox_title": "กรุณาทำการตรวจสอบความปลอดภัยให้เสร็จสิ้น", "verify_success": "การยืนยันสำเร็จ", "verify_failed": "การยืนยันล้มเหลว", "dang-qian-xi-tong-wu-deng-lu-yong-hu": "当前系统无登陆用户!", "strength": "强度", "login_use_existing_account": "เข้าสู่ระบบโดยใช้บัญชีที่มีอยู่", "back_to_home": "กลับไปที่หน้าแรก", "button_submit": "ส่ง", "bck_to_login_tip": "将在{0}秒后返回登录页面.", "admin_account": "管理员账号", "general_account": "一般账号", "sso_null": "单点配置信息不完整"}}, "portal": {"entity": {"theme": "ธีม", "console": "คอนโซล", "fullScreen": "เต็มจอ", "nuFullScreen": "ออกจากโหมดเต็มหน้าจอ", "department": "แผนก", "notDepartment": "ไม่มีแผนก", "changePassword": "เปลี่ยนรหัสผ่าน", "addSecondLang": "Add Second Language", "changeDepartment": "เปลี่ยนบริษัท", "changeCorp": "สลับองค์กร", "logout": "ออกจากระบบ", "switchLanguage": "เปลี่ยนภาษา", "settings": "系统设置", "home": "前台首页", "homepage": "หน้าแรก", "workbench": "โต๊ะทำงาน", "largescreen": "จอใหญ่", "searchMenu": "เมนูค้นหา", "message": "แจ้ง", "layoutset": "布局设置", "leftNavigation": "侧边导航", "topNavigation": "顶部导航", "skinset": "皮肤设置", "oldPassword": "รหัสผ่านเก่า", "newPassword": "รหัสผ่านใหม่", "newPassword2": "ยืนยันรหัสผ่านใหม่", "wechatScan": "การผูกโค้ดสแกน WeChat", "messageCenter": "ศูนย์ข้อความ", "messageView": "มุมมองข้อความ", "chooseHb": "สลับบัญชี", "navigation": "การนำทาง", "closeNavigation": "ปิดการนำทาง", "expandMenus": "ขยายเมนู", "openNewTab": "เปิดในเบราว์เซอร์ใหม่", "collapsedMenus": "เมนูหด", "homeTab": "การตั้งค่าหน้าเริ่มต้น", "quickStart": "เข้าด่วน", "addMore": "เพิ่มมากขึ้น", "myApp": "แอปพลิเคชันของฉัน", "selectedMenu": "เมนูที่เลือก", "optionalMenu": "เมนูเสริม", "quickModal": "การดำเนินการทางลัดที่กำหนดเอง", "downloadTip": "แนวทางการดำเนินงาน", "return": "รู้แล้ว", "search": "ค้นหา", "menuNav": "การนำทางเมนู", "policy": "นโยบายและข้อบังคับ", "addressBook": "สมุดที่อยู่", "appMarket": "ตลาดแอปพลิเคชัน", "onlineService": "ฝ่ายบริการลูกค้าออนไลน์", "help": "ช่วย"}, "verify": {"logout": "คุณแน่ใจว่าคุณต้องการที่จะออกจากระบบ?", "noSwitch": "ไม่มีบริษัทที่สามารถสลับสับเปลี่ยนได้", "chooseCompany": "กรุณาเลือกบริษัท", "chooseTenant": "请选择租户", "chooseDivision": "กรุณาเลือกแผนกธุรกิจ"}, "other": {"deptselect_tip": "您隶属于多部门，请选择当前所在部门", "deptselect": "请选择登录部门", "deptselect_title": "การเปลี่ยนบริษัท", "current_dept": "บริษัท ปัจจุบัน", "noDept_tip": "您尚未关联部门", "divisionselect": "请选择要切换的事业部", "dept": "บริษัท", "division": "หน่วยธุรกิจ"}, "tips": {"quickSettings": "ไปข้างหน้าและตั้งค่าเมนูรายการด่วนของคุณ~", "noFuncCurrApp": "ขณะนี้ไม่มีเมนูฟังก์ชั่นในแอปพลิเคชันปัจจุบัน~", "addQuickMenus": "เพิ่มการดำเนินการด่วนจากเมนูเสริมด้านล่าง~", "noOptionalMenus": "ไม่มีเมนูเสริม~", "selectedMenu": "มากถึง 27 รายการ ลากและวางเพื่อปรับลำดับ"}}, "portlet": {"mytask": {"myTodoTask": "我的待办", "myReadTask": "我的待阅", "detailFromTask": "流程详情"}, "process": {"frequentlyProcess": "常用流程", "startProcess": "发起流程"}, "announcement": {"notification": "通知公告"}}, "position": {"entity": {"positionName": "职务名称", "positionCode": "职务编码", "positionOrder": "排序", "description": "描述", "username": "账号", "realname": "姓名", "status_dictText": "状态"}, "verify": {"positionName": "请输入职务名称", "positionCode": "请输入职务编码", "positionName_repeat": "职务名称已存在", "positionCode_repeat": "职务编码已存在"}, "other": {"href_user": "用户", "selected_position": "当前职务"}}, "role": {"entity": {"roleName": "角色名称", "roleCode": "角色编码", "roleOrder": "排序", "description": "描述"}, "verify": {"roleName": "请输入角色名称", "roleCode": "请输入角色编码", "roleCode_Chinese": "角色编码不能输入汉字"}, "other": {"add_role": "新增角色", "copy_role": "复制角色", "back_role": "后台角色", "front_role": "前台角色", "setting_menu": "设置菜单", "setting_user": "设置用户"}}, "tenant": {"entity": {"tenantCode": "租户编码", "tenantName": "租户名称", "tenant": "租户", "operationFlag": "运维权限", "loginUrl": "登录网址", "maxUsers": "最大用户数量", "enableDivision": "启用事业部", "enableProxy": "是否代理", "enableCustomBroker": "是否报关行", "enableIgnoreCase": "是否启用忽略大小写", "mainComp": "主企业", "mainCompCode": "主企业编码", "mainCompName": "主企业名称", "mainUser": "主企业用户", "mainUserCode": "主企业用户账号", "mainUserName": "主企业用户名", "proxyCompCode": "代理企业编码", "proxyCompName": "代理企业名称", "proxyUserCode": "代理企业用户账号", "proxyUserName": "代理企业用户名"}, "verify": {"tenantCode": "请输入租户编码", "tenantName": "请输入租户名称", "tenantCode_repeat": "租户编码已存在", "maxUsers": "请输入最大用户数量", "mainComp": "请选择主企业", "mainUser": "请选择主企业用户", "proxyCompCode": "请输入代理企业编码", "proxyCompCodeNotExist": "代理企业不存在", "proxyUserCode": "请输入代理企业用户账号", "proxyUserCodeNotExist": "用户账号不存在"}, "other": {"have": "有", "none": "无", "change": "请先选择租户", "nowTenant": "当前租户", "logoutTenant": "切出租户", "logoutSuccess": "切出成功"}, "button": {"switch_tenant": "切换租户", "setting_menu": "设置可用菜单"}}, "corpTag": {"entity": {"tagCode": "标识代码", "tagName": "标识名称", "bind_org": "绑定组织", "bind_user": "绑定用户", "remark": "备注", "createTime": "创建时间", "tagSetting": "标识设置", "related_org": "关联组织", "related_role": "授权角色", "related_user": "关联用户"}, "verify": {"tagCode": "请输入标识代码", "tagName": "请输入标识名称", "tagCodeExisted": "标识代码已存在", "notBoundTenant": "ผู้เช่าไม่ได้เปลี่ยน", "notOperationPermit": "没有运维权限", "notTenant": "查不到租户"}}, "job": {"entity": {"jobName": "岗位名称", "jobCode": "岗位编码", "jobOrder": "排序", "description": "描述"}, "verify": {"jobName": "请输入岗位名称", "jobCode": "请输入岗位名称", "jobName_repeat": "岗位名称已存在", "jobCode_repeat": "岗位编码已存在"}, "other": {"export_filename": "岗位"}}, "org": {"entity": {"orgName": "组织名称", "orgCode": "组织编码", "orgType": "组织类型", "orgOrder": "排序", "parentId": "上级组织", "orgNameAbbr": "组织缩写", "depName": "部门名称", "companyName": "公司名称", "compCode": "企业编码", "socialCreditCode": "统一社会信用代码", "ip_white": "启用IP白名单", "ip_address": "IP白名单地址", "enable_password_expiration": "密码强制过期", "password_expired_day": "密码过期时间(天)", "password_expired_alert_day": "密码过期提醒时间(天)", "lock_user": "启用锁定账户功能", "lock_black_password": "启用密码黑名单", "multiple_login": "账号多次登录", "enable": "启用", "disable": "不启用", "expired": "过期", "not_expired": "不过期", "default": "默认", "prohibit": "禁止", "allow": "允许", "largeScreenAddress": "大屏地址", "largeScreenEnAddress": "大屏英文地址", "largeScreenThAddress": "大屏泰文地址", "largeScreenLocaleSetting": "大屏多语言地址", "homePageAddress": "首页地址", "chargeNotice": "显示收费公告", "factoryCode": "工厂代码"}, "verify": {"orgName": "请输入组织名称", "orgType": "请选择组织类型", "orgOrder": "请输入组织排序", "compCodeLength": "企业编码长度必须10位", "socialCreditCodeLength": "统一社会信用代码长度必须18位", "compCodeExist": "当前企业编码已存在"}, "other": {"add_sub": "新增下级", "handle_update_path": "确定整理数据吗？", "orgType_org": "组织", "orgType_dept": "แผนก", "company_name": "公司", "href_division": "配置事业部"}}, "user": {"entity": {"username": "ชื่อผู้ใช้", "realname": "姓名", "password": "密码", "passwordPlaceholder": "ต้องมีตัวอักษรพิมพ์ใหญ่และพิมพ์เล็ก ตัวเลข และอักขระพิเศษรวมกันอย่างน้อยสามชุด!", "userNum": "员工编码", "userSecret": "员工密级", "userOrder": "排序", "isRealUser": "是否实体用户", "majarDepName": "主部门", "mainCompanyName": "主公司", "userQq": "QQ号", "avatar": "头像", "birthday": "生日", "sex": "性别", "email": "电子邮件", "phone": "หมายเลขโทรศัพท์", "role": "角色", "status": "状态", "workNo": "工号", "telephone": "座机号", "confirmpassword": "确认密码", "selectedRole": "角色分配", "selectedJob": "岗位分配", "compCode": "รหัสองค์กร", "compName": "ชื่อ บริษัท", "socialCreditCode": "รหัสเครดิตโซเชียลแบบรวม", "user_source": "แหล่งที่มาของผู้ใช้", "enable_vcode": "启用双因子登录认证", "force_change_pass": "是否强制修改密码", "show_water_mark": "是否显示水印", "domain_key": "域标识", "domain_name": "域账号", "user_type": "用户类型", "normal": "普通用户", "admin": "ผู้ดูแลระบบ", "proxy": "代理", "proxy_edit": "代理编辑列表", "fill_proxy_edit": "请填写代理编辑列表", "password_expiration": "密码强制过期", "multiple_login": "账号多次登录", "enable": "启用", "disable": "不启用", "expired": "过期", "not_expired": "不过期", "default": "默认", "prohibit": "禁止", "allow": "允许", "enable_phone_login": "启用手机验证码登录", "enable_email_notification": "启用邮件告知用户", "user_status": "用户状态", "remark": "备注", "show": "显示", "no-show": "不显示", "operable_org": "可操作企业", "forward_code": "货代编码", "broker_code": "报关行编码", "comp_code": "海关编码", "proxy_type": "代理类型", "proxy_code": "代理编码", "customs_code": "海关编码", "customs_code_length": "海关编码只能为十位", "require_org": "必须选择企业", "require_type": "必须选择代理类型", "require_code": "必须填写代理编码", "proxy_repeat": "同一家可操作企业存在相同的代理类型", "least_one": "至少填写货代、报关行、海关编码中的一个"}, "verify": {"username": "请输入用户名", "password": "รหัสผ่านประกอบด้วยตัวเลข 8 ถึง 20 หลัก ตัวอักษรตัวพิมพ์ใหญ่และตัวพิมพ์เล็ก และสัญลักษณ์พิเศษ", "confirmpassword": "请重新输入登陆密码", "diffpassword": "两次输入的密码不一样", "phoneexist": "手机号已存在", "phoneformat": "请输入正确格式的手机号码", "emailexist": "Email已存在", "emailformat": "请输入正确格式的Email", "userexist": "用户已存在", "workNoexist": "工号已存在", "deptexist": "该部门已添加", "realname": "请输入用户名称", "workNo": "请输入工号", "telephone": "请输入正确的座机号码", "confirmfrozen": "确认冻结该账号吗", "confirmunfrozen": "确认解冻该账号吗", "restore_user": "您确定要恢复选中的用户吗", "physical_delete": "您确定要彻底删除（不可再恢复）选中的用户吗", "isadmin_warn": "管理员账号不允许此操作", "confirmdept": "请选择一个部门", "majarDepName": "请选择主要部门", "phone": "请输入手机号", "email": "请输入电子邮件", "domain_exist": "域标识+域账号已存在"}, "other": {"select_user_title": "选择用户", "basic_info": "基础信息", "ext_info": "兼职公司", "frozen": "冻结", "unfrozen": "解冻", "recycle": "回收站", "restore_user": "恢复用户", "physical_delete": "彻底删除", "changepwd": "เปลี่ยนรหัสผ่าน", "dept_label": "แผนก", "position_label": "职务", "dept_position_label": "公司选择", "add_user": "添加用户", "sex_male": "男", "sex_female": "女", "export_filename": "用户信息", "division_info": "事业部信息", "admin_add": "管理员新增", "user_register": "用户注册", "third_party": "第三方", "max_user_exceed": "已超过最大用户数"}}, "permission": {"entity": {"name": "菜单名称", "menuType": "菜单类型", "sortNo": "排序", "groupId": "应用菜单", "component": "组件", "url": "菜单路径", "parentId": "上级菜单", "componentProps": "组件参数", "icon": "图标", "color": "菜单图标颜色", "menuHidden": "隐藏菜单", "defaultShortcutMenu": "默认快捷菜单", "isKeepalive": "缓存路由", "isDeclare": "使用云端产品", "internalOrExternal": "外部链接", "perms": "授权标识", "permsType": "授权策略", "status": "状态", "back_show": "后端展示", "zhCn": "中文", "enUs": "英文", "menuCode": "菜单code", "language1": "扩展语言1", "language2": "扩展语言2", "language3": "扩展语言3", "reminderMenu": "提醒菜单", "allowVisitorAccess": "允许游客访问", "tenantMenu": "租户管理员菜单", "tenantRange": "Tenant Range", "functionGroup": "功能组名"}, "verify": {"name": "请输入菜单标题", "menuCode": "请输入菜单code", "component": "请输入前端组件", "url": "请输入菜单路径", "permsType": "请输入授权策略", "menuType": "请检查你填的类型以及信息是否正确", "perms": "请输入授权标识", "number": "请输入正整数", "duplicate_perms": "授权标识已存在", "existsMenuCode": "菜单code已存在", "menuCodeLength": "长度不能超过100位", "functionGroup": "请输入功能组名"}, "other": {"menu_permission_label": "菜单权限", "group_permission_label": "应用权限", "unfold_fold": "展开/折叠", "all_none": "全选/全不选", "father_son_link": "父子联动", "select_role": "请选择角色", "back_menu": "后台菜单", "front_menu": "前台菜单", "add_sub": "添加下级", "data_rule": "数据规则", "premission_aq": "菜单常见问答", "add_sub_label": "添加子菜单", "permission_type0": "菜单名称", "permission_type1": "一级菜单", "permission_type2": "子菜单", "permission_type3": "页面资源", "virtualMenu": "虚拟菜单", "select_component": "选择组件", "form_component": "表单", "flow_component": "流程", "report_component": "报表", "view_component": "页面", "route_component": "聚合菜单", "iframe_component": "Iframe页签", "dataEase_component": "大屏后台开发", "perms_placeholder": "多个用逗号分隔, 如:user:list,user:create", "init_root_menu": "初始化根菜单", "menu_workbench": "工作台", "menu_process": "流程", "menu_application": "应用", "permsType_1": "可见/可访问(授权后可见/可访问)", "permsType_2": "可编辑(未授权时禁用)", "status_1": "有效", "status_0": "无效"}}, "dict": {"entity": {"dictName": "字典名称", "dictCode": "字典编号", "category": "字典分类", "description": "描述", "itemText": "字典明细", "itemValue": "字典明细值", "sortOrder": "排序值", "visibleCheck": "是否启用", "appName": "应用名称", "groupCode": "分组标识", "extraInfo": "扩展配置"}, "verify": {"dictName": "请输入字典名称", "dictCode": "请输入字典编码", "itemText": "请输入字典明细名称", "itemValue": "请输入字典明细值"}, "other": {"dictName_dictCode": "名称/编号", "name_code_placeholder": "请输入字典名称或编号", "recycle": "回收站", "refleshCache": "刷新缓存", "option_category0": "平台", "option_category1": "公共", "option_category2": "应用", "option_category3": "公共-分类", "option_category4": "公共-平台", "export_filename": "字典信息"}}, "portalset": {"entity": {"portletName": "门户名称", "portletCode": "门户编码", "isDefault": "是否默认配置"}, "verify": {"portletCode": "请输入门户编码", "portletName": "请输入门户名称"}, "other": {"authorize": "授权"}}, "sqlconfig": {"entity": {"sqlName": "名称", "sqlCode": "编码", "sqlQuery": "SQL", "isCache": "是否缓存"}, "verify": {"sqlName": "请输入名称", "sqlCode": "请输入编码", "sqlKey": "请输入key", "sqlText": "请输入text", "orderBy": "请输入排序", "sqlCode_repeat": "编码已存在", "sqlName_repeat": "名称已存在"}, "other": {"export_filename": "SQL 配置"}}, "httpconfig": {"entity": {"httpName": "服务名称", "httpCode": "服务编码", "httpUrl": "服务URL", "returnScript": "返回值脚本", "displayFormat": "显示格式", "httpMethod": "请求类型", "dynamic": "是否动态请求接口", "autocompleteKey": "入参key", "delayTime": "延迟时间(毫秒)", "inputStart": "开始查询位数", "inParameterType": "入参类型", "httpHeader": "请求头", "httpBody": "请求体", "httpAuthConfig": "认证配置", "inParameter": "入参定义", "keyvalue": "键-值", "key": "键", "value": "值", "takeData": "预览数据", "getTakeConfig": "获取提取数据结构", "dataStructure": "数据结构", "dataPreview": "数据预览", "analysisCol": "解析字段", "colType": "字段类型", "analysisApi": "解析API数据"}, "verify": {"httpName": "请输入服务名称", "httpCode": "请输入服务编码", "httpUrl": "请输入服务URL", "httpMethod": "请选择请求类型", "header": "key不能为空", "inParameter": "key不能为空", "value": "value不能为空", "header_repeat": "请求头 key重复", "return_repeat": "返回数据结构 key重复", "inParameter_repeat": "入参定义 key重复"}, "other": {"exprTitle": "value值可用表达式包括：{0}、{1}、{2}、{3}、{4}、{5}、{6}、{7}、{8}、{9}", "qing-she-zhi-urlquerybyid-shu-xing": "请设置url.queryById属性!", "export_filename": "HTTP服务注册"}}, "treesetting": {"entity": {"typeName": "类型名称", "typeCode": "类型编码"}, "verify": {"requireTypeName": "类型名称不能为空", "requireTypeCode": "类型编码不能为空", "repeatTypeCode": "类型编码不能重复", "nodeNotNull": "树形结构必须存在一个节点"}}, "sysconfig": {"entity": {"configCode": "编码", "configName": "描述", "configValue": "值"}, "verify": {}, "other": {}}, "sysfillrule": {"entity": {"ruleName": "规则名称", "ruleCode": "规则编码", "ruleClass": "规则实现类", "ruleParams": "规则参数", "ruleDescription": "描述", "digits": "位数", "pattern": "规则（正则表达式）", "message": "提示文本", "priority": "优先级"}, "verify": {"ruleParams": "请输入JSON字符串：", "ruleParams_format": "只能传递JSON对象，不能传递JSON数组："}, "other": {"generated_code": "生成编码：", "sysFillRule_tab": "编码生成规则", "sysCheckRule_tab": "校验规则", "localRules_tab": "局部规则", "globalRules_tab": "全局规则", "localRules_tip": "局部规则按照你输入的位数有序的校验", "globalRules_tip": "全局规则可校验用户输入的所有字符；全局规则的优先级比局部规则的要高", "pattern_tip": "请输入正确的正则表达式", "priorityOptions1": "优先运行", "priorityOptions0": "最后运行", "test_label": "输入校验文本", "test_title": "校验规则测试"}}, "datasource": {"entity": {"datasourceKey": "数据源标识", "datasourceName": "数据源名称", "datasourceType": "数据源类型", "dbType": "数据库类型", "attributesJson": "连接配置", "url": "数据库url", "username": "用户名", "password": "密码", "driverClassName": "驱动类名称"}, "verify": {}, "other": {}}, "category": {"entity": {"pid": "父级节点", "name": "分类名称", "code": "分类编码"}, "verify": {}, "other": {"add_sub": "添加下级"}}, "syslog": {"entity": {"userid": "登录账号", "username": "姓名", "logContent": "日志内容", "ip": "IP", "os": "操作系统", "browser": "浏览器", "createTime": "创建时间", "operateType": "操作类型", "contentDetail": "详情", "addMenu": "新增菜单", "groupAddMenu": "新增应用权限", "deleteMenu": "删除菜单", "groupDeleteMenu": "删除应用权限", "companyName": "企业名称", "companyCode": "企业编码", "operatorTime": "操作时间"}, "verify": {}, "other": {"login_log": "登录日志", "operate_log": "操作日志", "request_method": "请求方法", "request_parameters": "请求参数", "operateType1": "登录", "operateType2": "注销", "operateType3": "查询", "operateType4": "添加", "operateType5": "更新", "operateType6": "删除", "operateType7": "导入", "operateType8": "导出", "operateType9": "分页查询", "operateType101": "登录成功", "operateType102": "登录失败", "operateType103": "组织管理", "operateType104": "用户管理", "operateType105": "用户冻结解冻", "operateType106": "修改密码", "operateType107": "角色管理", "operateType108": "设置用户角色", "operateType109": "角色授权", "operateType110": "租户管理"}}, "datalog": {"entity": {"dataTable": "表名", "dataId": "数据ID", "dataVersion": "版本号", "createBy": "创建人", "createTime": "创建日期", "code": "字段", "dataVersion1": "版本号1", "dataVersion2": "版本号2"}, "verify": {}, "other": {"dataCompare": "数据比较", "dataCompare_tip1": "请选择两条数据", "dataCompare_tip2": "请选择相同的数据库表和数据ID进行比较", "okText": "知道了"}}, "monitor": {"entity": {"param": "参数", "text": "描述", "value": "当前值", "timestamp": "请求时间", "method": "请求方法", "uri": "请求URL", "status": "响应状态", "timeTaken": "请求耗时"}, "verify": {}, "other": {"updateTime": "更新时间", "lastUpateTime": "上次更新时间", "updateNow": "立即更新", "httpCount": "共追踪到{0}条近期HTTP请求记录", "httppaging": "显示{0} - {1}条记录，共{2}条记录", "systemInfo": "服务器信息", "jvmInfo": "JVM信息", "redisInfo": "Redis监控", "httpTrace": "请求追踪"}}, "quartzjob": {"entity": {"jobName": "任务名称", "jobClassName": "任务类名", "description": "描述", "cronExpression": "cron表达式", "parameter": "参数", "status": "状态"}, "verify": {"cronExpression": "请输入cron表达式"}, "other": {"status_option1": "正常", "status_option2": "停止", "start": "启动", "stop": "停止", "start_status": "已启动", "stop_status": "已停止", "start_tip": "确定启动该任务吗？", "stop_tip": "确定停止该任务吗？", "export_filename": "定时任务信息", "execute_one": "执行一次", "execute_tip": "确认执行该任务一次吗？"}}, "message": {"entity": {"esTitle": "标题", "esType": "发送方式", "esReceiver": "接收人", "esTemplateCode": "短信模板标识", "esParam": "发送所需参数", "esContent": "发送内容", "esSendTime": "发送时间", "esSendStatus": "发送状态", "esSendNum": "发送次数", "esResult": "发送失败原因", "remark": "备注", "message": "完成提示消息", "templateCode": "模板编码", "templateType": "模板类型", "templateName": "模板标题", "templateContent": "模板内容", "msgBody": "测试数据"}, "verify": {"esParam": "输入为JSON格式"}, "other": {"esType_option1": "短信", "esType_option2": "邮件", "esType_option3": "微信", "esType_option4": "系统", "status_option0": "未发送", "status_option1": "发送成功", "status_option2": "发送失败", "send_email_code": "验证码已发送至 {0} 邮箱!", "send_phone_code": "验证码已发送至 {0} 手机!", "send_code": "验证码已发送至 {0}!", "sendTest": "发送测试", "sysMessage_tab": "消息管理", "sysMessageTemplate_tab": "消息模板管理", "export_message_filename": "消息", "export_messageTemplate_filename": "消息模板"}}, "announcement": {"entity": {"titile": "标题", "msgCategory": "类型", "priority": "优先级", "msgType": "发布范围", "selectedUser": "指定用户", "msgContent": "内容", "sender": "发布人", "sendTime": "เวลาปล่อย", "pSendTime": "请选择发布时间", "readFlag": "阅读状态"}, "verify": {"publish_confirm": "确定发布吗：", "unpublish_confirm": "确定撤销吗", "readall_confirm": "确定全部标记已读吗", "selectedUser_check": "指定用户不能为空", "time_check": "开始时间不能大于结束时间"}, "other": {"publish": "发布", "unpublish": "撤销", "readall": "全部标记已读", "msgCategory_option1": "通知公告", "msgCategory_option2": "系统消息", "priority_option1": "低", "priority_option2": "中", "priority_option3": "高", "msgType_option1": "指定用户", "msgType_option2": "全体用户", "sendStatus_option0": "未发布", "sendStatus_option1": "已发布", "sendStatus_option2": "已撤销", "no_read": "ยังไม่ได้อ่าน", "have_read": "ได้อ่าน", "msg_label": "通知公告", "qu-chu-li": "去处理"}}, "division": {"entity": {"name": "事业部名称", "code": "事业部编码", "order": "事业部排序", "description": "描述"}, "verify": {"name": "请输入事业部名称", "code": "请输入事业部编码", "codeExisted": "事业部编码已存在"}}}, "messageCenter": {"admin": {"newMessage": "新建消息", "newNews": "新消息", "draw": "撤回", "published": "已发布", "withdrawn": "已撤回", "messageTitle": "ชื่อข้อความ", "pMessageTitle": "请输入消息标题", "selectGroups": "选择分组", "selectBusiness": "选择企业", "allBusiness": "所有企业", "targetBusiness": "目标企业", "pCompanyCode": "请输入企业编码或名称", "exclude": "排除", "joinIn": "加入", "remindType": "提醒类型", "cornerReminder": "角标提醒", "dialogReminder": "弹框提醒", "popUpReminder": "弹出提醒", "delayTime": "延时关闭时间", "unitSecond": "单位秒，手动关闭可以写", "expiredTime": "有效期", "today": "当天有效", "longTerm": "长期有效", "isArchive": "是否存档", "archive": "存档", "notArchive": "不存档", "fileBrowsing": "文件浏览", "fileUpload": "文件上传", "file": "附件", "selectCompany": "请选择企业或企业分组", "titleNotNull": "消息标题不能为空！", "pEnterTitle": "请填写消息内容！", "publishSuccessfully": "发布成功！", "withdrawSuccessfully": "撤回成功！", "remind": "提醒", "remindContent": "当前表单消息内容未保存，是否继续", "fileSize": "附件大小不超过10M！", "selectFile": "请选择需要上传的文件！", "groupName": "分组名称", "hasRead": "确认已读"}}, "eform": {"appgroup": {"entity": {"groupName": "名称", "groupCode": "编码", "groupDesc": "描述", "orderNum": "排序", "parentId": "父级节点", "select-parentId": "请选择父级节点", "icon": "图标", "enableCreateTable": "是否可创建实体表", "backgroundColor": "背景色", "isMobile": "移动端显示", "isCommon": "是否公共", "isCommonApp": "是否公共配置应用", "moduleType": "应用级别", "shadowMode": "Enable Shadow Mode", "level1": "一级模块", "level2": "二级模块", "formOrViewName": "表单/列表名称", "formOrViewCode": "表单/列表编码", "type": "类型"}, "verify": {"groupName": "请输入名称", "groupCode": "请输入编码", "moveApp": "确认要移动当前数据么?"}, "other": {"current_app": "当前应用", "common_app": "公共应用", "group_name": "所属应用", "group_manage": "分类管理", "new_app": "创建应用", "import_batch": "批量导入", "export_batch": "批量导出", "import_new": "导入新增", "import_cover": "导入覆盖", "delete_cache": "清空缓存", "edit_app": "设置应用", "delete_app": "删除应用", "version": "版本号", "comment": "注释", "publish": "发布", "viewpublish": "查看版本", "revert": "恢复", "export_app": "导出应用", "copy_app": "复制应用", "split_success": "拆分成功！"}}, "apptable": {"entity": {"logical_AND": "并且", "key": "key(下拉框绑定数据，下拉框格式为label、value)", "value": "value(接口返回数据key)", "entityModel": "实体模型", "compare": "比较方式", "limitation": "限制条件", "addNo": "新增时不应用", "editNo": "修改时不应用", "detailNo": "详情时不应用", "compareFiled": "比较栏位", "addCompare": "新增比较方式", "compareValue": "比较值", "isHideFields": "是否隐藏字段", "hideFields": "额外字段", "operationField": "操作栏位", "property": "设置属性", "dataRange": "数据范围", "showBlue": "显示蓝星", "noBlue": "去除蓝星", "noValue": "去除数据", "noRequire": "非必填", "equalNum": "等于(数值)", "equalStr": "等于(字符串)", "lessThan": "小于", "greaterThan": "大于", "lessThanEq": "小于等于", "greaterThanEq": "大于等于", "contain": "包含", "not_contain": "不包含", "empty": "数据为空", "noEmpty": "数据不为空", "startWith": "以...开头", "endWitn": "以...结尾", "erModel": "ER模型", "apiErModel": "ER模型(优先于上下文参数)", "erModelSelect": "请选择ER模型", "erModelColumnSelect": "ER模型栏位选择", "memuParam": "菜单参数(优先于Stores)", "stores": "Stores(优先于固定值)", "memuParamPlaceholder": "请输入菜单参数", "storesPlaceholder": "请输入Stores值", "fixedValuePlaceholder": "请输入固定值", "contentColumnSelect": "上下文参数选择", "contentColumn": "上下文参数(优先于菜单参数)", "returnData": "返回数据结构", "checkSql": "检查脚本", "verifySql": "校验脚本(菜单参数以#{xxx}拼接至sql中)", "replaceSql": "替换脚本(替换规则描述中的${xxx})", "dataModel": "数据模型", "formModel": "表单模型", "flowModel": "流程模型", "reportModel": "报表模型", "viewModel": "列表模型", "dictModel": "数据字典", "menuModel": "菜单配置", "languagesModel": "多语言配置", "tableName": "表名称", "tableDesc": "表描述", "dsName": "数据源", "syncTable": "同步表", "isSync": "实体表状态", "objName": "ER模型名称", "objCode": "ER模型标识", "objStructure": "ER模型结构", "persistenceType": "持久化方式", "colCode": "字段名称", "relation": "逻辑关系", "filterRelation": "筛选逻辑", "filterParams": "筛选条件", "addParam": "添加筛选", "clearParams": "清空筛选", "addFilterParams": "添加筛选逻辑", "erModelTab": "ER模型配置", "scriptTab": "执行脚本配置", "generateScript": "生成至执行脚本", "colVal": "条件值", "colName": "表字段备注", "columnName": "表字段", "oldTable": "旧表名", "oldTableDesc": "旧表描述", "newTableName": "新表名", "newTableDesc": "新表描述", "oldObjName": "旧ER模型名称", "oldObjCode": "旧ER模型标识", "newObjName": "新ER模型名称", "newObjCode": "新ER模型标识", "oldFormName": "旧表单名称", "oldFormCode": "旧表单标识", "newFormName": "新表单名称", "newFormCode": "新表单标识", "oldViewName": "旧页面名称", "oldViewCode": "旧页面编码", "newViewName": "新页面名称", "newViewCode": "新页面编码", "sameTableName": "表名不能相同", "sameObjCode": "ER模型标识不能相同", "sameFormCode": "表单标识不能相同", "sameViewCode": "页面编码不能相同", "sameTaskCode": "任务编码不能相同", "colDescription": "字段描述", "colType": "字段类型", "colLength": "字段长度", "colPointLength": "小数点位", "message": "长度提示文本", "isQuery": "是否查询列", "viewShow": "列表是否显示", "formShow": "表单是否显示", "toFormColCode": "对应表单拓展栏位", "formColCode": "对应表单栏位", "importSort": "导入顺序", "isKey": "主键", "isNull": "允许空值", "isSeq": "根据表头自增", "colDefaultVal": "默认值", "checkedValue": "选中时的值", "unCheckedValue": "未选中时的值", "showType": "输入控件类型", "showConfig": "数据显示格式", "dataConfig": "数据转换配置", "validRule": "常规校验", "isRequired": "必填校验", "lengthValid": "长度校验", "er_type": "关系", "er_fks": "外键", "objectBaseInfo": "对象基础信息", "objectCommonInfo": "对象一般信息", "dataShowConfig": "数据显示格式", "objectAdvancedConfig": "对象高级设置", "columnCode": "字段名称", "validColumn": "校验字段", "columnType": "字段类型", "menuParam": "菜单参数", "table": "列表", "tableColumnSelect": "列表字段选择", "ruleDescription": "规则描述", "expectedType": "期望值类型", "checkOperator": "校验操作符", "expectedValue": "期望值", "interfaceUrl": "接口地址"}, "verify": {"confirm_create_table": "确定创建实体表吗", "confirm_sync_table": "确定同步实体表吗", "confirm_handle_type": "存在重复数据，请选择处理方式", "confirm_handle_title": "处理方式", "create_table_success": "建表成功", "table_exist": "表已存在，请执行同步操作", "sync_success": "同步成功", "table_not_exist": "表不存在，请执行建表操作", "system_table_no_update": "系统表不能修改", "confirm_create_er_form": "要自动生成ER模型和表单模型吗", "confirm_create_er": "要自动生成ER模型吗", "tip_sql": "请在主页面输入SQL", "columnName": "字段名称不能为空", "checkSql": "检查脚本不能为空", "columnCode": "字段名称不能为空", "checkOperator": "校验操作符不能为空", "ruleDescription": "规则描述不能为空", "expectedType": "期望值类型不能为空", "expectedValue": "期望值不能为空", "interfaceUrl": "接口地址不能为空", "urlExists": "该接口地址已存在", "compare": "比较方式必填"}, "placeholder": {"dateTimeformat": "请选择日期时间格式", "viewName": "请选择页面编码", "keyField": "请选择存储字段", "labelField": "请选择显示字段", "sqlName": "请选择Sql配置", "httpName": "请选择服务配置", "colLength": "请输入字段长度", "formatExpr": "${'{'}key{'}'}和${'{'}value{'}'}代表原值", "form_show_type": "请选择控件显示类型", "form_placeholder": "默认用字段名占位，也可自定义占位内容", "form_date": "请选择日期格式", "form_time": "请选择时间格式", "form_min": "请输入最小值", "form_max": "请输入最大值", "form_step": "请输入步长", "form_minRows": "请输入最小高度", "form_maxRows": "请输入最大高度", "prompt": "显示字段必须设置；存储字段可空，若设置存储字段，则其必须是唯一索引字段", "form_limit": "请输入最大上传数量", "fileTableStyle": "请输入表格样式", "form_bizPath": "请输入存储目录", "form_listType": "请选择样式"}, "other": {"source_gen": "生成代码", "button_update_data": "从实体模型更新数据", "button_copy_data": "确定并复制到数据转换配置", "load_database": "从数据库加载", "db_properties": "数据库属性", "page_properties": "页面属性", "create_table": "创建实体表", "sync_table": "同步实体表", "create_er": "生成ER模型", "create_er_form": "生成ER模型和表单模型", "handleCover": "覆盖", "no-handleCover": "不覆盖", "handleIgnore": "忽略", "handleCancel": "关闭", "data_conversion_config": "数据转换配置", "data_conversion_mode": "数据转换方式", "date_time_format": "日期时间格式", "default_value": "默认值", "label_color": "栏位颜色", "static_options": "静态选项", "option": "选项", "data_dictionary": "数据字典", "sql_configuration": "Sql配置", "service_configuration": "服务配置", "backfill_configuration": "参数CODE值", "page_code": "页面编码", "storage_field": "存储字段", "display_field": "显示字段", "default_first": "是否默认第一项", "formatted_expression": "格式化表达式", "options_expression": "选项格式化表达式", "view_expression": "显示格式表达式", "option_default": "不转换", "option_defaultValue": "自定义默认值", "option_dateTime": "时间格式", "option_options": "静态选项Code转名称", "option_dictOption": "数据字典Code转名称", "option_sqlConfig": "通过Sql配置转换", "option_httpConfig": "通过服务配置转换", "option_user": "用户ID转名称", "option_userCode": "用户Code转名称", "right_button": "右侧按钮", "option_dept": "部门ID转名称", "option_deptCode": "部门Code转名称", "option_page": "通过页面模型进行数据转换", "option_image": "以图片形式展示", "option_file": "以附件形式展示", "option_links": "以超链接形式展示", "option_switch": "以开关形式展示", "option_download": "申报端附件展示", "form_show_type": "控件显示类型", "form_placeholder": "占位内容", "shi-fou-ti-shi": "是否提示", "ti-shi-yu": "提示语", "form_date": "日期格式", "form_time": "时间格式", "form_options": "静态选项(优先于数据字典)", "form_dictOption": "数据字典(优先于Sql配置)", "form_sqlConfig": "Sql配置(优先于服务配置)", "form_column_config": "动态列配置", "form_min": "最小值", "form_max": "最大值", "form_step": "步长", "form_precision": "精度", "form_minRows": "最小高度", "form_maxRows": "最大高度", "form_other_config": "其他配置", "value_range": "数值范围", "form_viewName": "页面编码", "form_fields": "回填字段", "form_limit": "最大上传数量", "limit_size": "上传文件大小限制", "suffix": "上传文件后缀", "suffixPlaceHolder": "以分号';'分隔 例如: png;jpeg;doc", "suffixTips": "上传文件后缀限制:", "form_upload_type": "上传地址类型", "delay_upload": "是否手动上传", "declare_config": "申报端配置", "customs_config": "关务上传基础路径", "upload_classify": "业务类型", "upload_file_type": "文件类型", "fileTableStyle": "表格样式", "declarePortUrl": "申报端上传接口路径,例如:acmp/api", "customsPortUrl": "关务上传接口路径,例如:cs/api", "form_bizPath": "存储目录", "uploadShowListHead": "上传列表是否隐藏列表头", "uploadShowCheckedCol": "上传列表仅显示勾选列", "separator": "分隔符", "form_listType": "样式", "option_showTime": "时间选择器", "start_today": "从今天开始", "start_tomorrow": "从明天开始", "date_multiple": "日期多选", "option_clearable": "可清除", "option_range": "范围日期", "number_range": "范围数字", "option_single": "可单选", "option_multiple": "多选", "allowDirectInput": "可输可选", "allowDirectLength": "可输可选长度", "option_allowHalf": "允许半选", "option_drag": "允许拖拽", "option_hidden": "隐藏", "option_showSearch": "可搜索", "option_ignoreCase": "过滤时忽略大小写", "option_year": "年度", "er_add_mainTable": "设置主表", "er_close_objRelation": "关闭对象关系串", "er_show_objRelation": "打开对象关系串", "er_add_subTable": "添加子表", "er_mainTable": "主表", "er_objectRelation": "对象关系", "er_repeatTable": "存在重复表:", "parentField": "子表外键 对应 主表字段", "childField": "子表字段 对应 主表外键", "loading_flag": "Loading标志", "flag_show_value": "标志显示值", "dynamic_enable": "行内图片", "dynamic_upload": "图片上传", "dynamic_img_position": "图片位置", "dynamic_img_distance": "与文字距离", "dynamic_img_width": "图片宽度", "dynamic_img_height": "图片高度", "dynamic_unit": "单位：px", "dynamic_show": "图片显示值", "dynamic_show_placeholder": "等于列表中显示的值", "show_expand_field": "显示拓展字段", "upload_bill_type": "单据类型", "upload_remark": "备注", "excel_save_url": "自定义保存接口", "excel_refresh_url": "自定义刷新接口"}, "dataFilter": {"data_filtering": "数据过滤", "data_add": "数据新增", "add_data": "新增数据", "enable": "启用", "filter_content_method": "过滤内容方式", "select_filter_content_method": "过滤内容方式", "filter_content": "过滤的内容", "sql": "通过SQL进行过滤", "key": "下拉框的Key值", "text": "下拉框的Text值", "sql_placeholder": "请输入SQL，仅支持单列查询，例：select distinct xxx as value from table", "static": "通过静态数据进行过滤", "static_option": "静态选项"}}, "appimport": {"entity": {"taskName": "任务名称", "oldTaskName": "旧任务名称", "newTaskName": "新任务名称", "taskCode": "任务编码", "oldTaskCode": "旧任务编码", "newTaskCode": "新任务编码", "importMode": "导入模式", "importType": "导入类型", "add": "新增导入", "update": "修改导入", "delete": "删除导入", "save": "新增和修改导入", "single-table": "单表", "multi-table-single-sheet": "多表单sheet", "multi-table-multi-sheet": "多表多sheet", "multi-table-single-sheet-thailand": "多表单sheet(泰国)", "pageTitle": "页面标题", "headStartRow": "表头导入起始行", "startRow": "导入起始行", "listStartRow": "表体导入起始行", "customParam": "自定义参数", "sheetName": "sheet名称", "businessPk": "业务主键", "association": "关联关系", "childField": "子表字段 对应 主表字段", "isExcelField": "是否Excel字段"}, "verify": {"taskName": "请输入任务名称", "taskCode": "请输入任务编码", "importMode": "请输入导入模式", "importType": "请输入导入类型", "pageTitle": "请输入页面标题", "headStartRow": "请输入表头导入起始行", "startRow": "请输入导入起始行", "listStartRow": "请输入表体导入起始行", "customParam": "请输入自定义参数", "sheetName": "请输入sheet名称", "businessPk": "请输入业务主键", "taskCode_repeat": "任务编码已存在"}, "button": {"template-setting": "模板设置", "rule-setting": "规则设置"}}, "appform": {"common": {"online_form": "电子表单", "registration_form": "注册表单", "business_form_title": "业务表单", "approval_opinions_title": "审批意见", "process_chart_title": "流程图"}, "entity": {"formName": "表单名称", "formCode": "表单标识", "formVersion": "表单版本", "objectName": "ER模型", "formStatus": "表单状态", "tableStatus": "列表状态", "pcUrl": "PC端地址", "mobileUrl": "移动端地址", "delClass": "数据删除接口", "serviceName": "服务编码"}, "verify": {"formName": "请输入表单名称", "formCode": "请输入表单标识", "ERmodel": "请选择ER模型", "formCode_format": "表单标识不能包含汉字"}, "business": {"serviceName": "业务服务名称", "logicDescription": "逻辑描述备注", "isPersistence": "业务类型", "addBusinessItem": "新增服务", "editBusinessItem": "编辑服务", "seeBusinessItem": "查看服务", "deleteBusinessItem": "删除服务", "delete_success": "删除成功", "executeBusiness": "执行编排", "sqlBusiness": "sql执行器", "dataSync": "数据同步", "dataCopy": "数据复制", "originObject": "源对象", "targetObject": "目标对象", "takeOriginColumns": "提取源字段", "targetListColumns": "当前列表字段", "originObjectColumns": "源对象字段", "targetObjectColumns": "目标对象字段", "fixedValue": "固定值", "businessObject": "业务对象", "businessColumns": "业务字段", "columnsConfig": "设置字段", "addColumns": "添加字段", "generateRules": "复制后生成规则", "pCode-company": "pCode企业信息", "pCode-complex": "pCode商品信息", "customParam": "导入中的自定义参数", "elements": "申报要素", "storage_table_name": "入库表名", "storage_table_name_placeholder": "请输入入库表名", "map_column": "映射列", "pCode_map_column": "pCode入库映射列", "pCode_map_column_note": "回填的列，不选回填当前列", "content": "内容", "customParamPlaceholder": "请输入自定义参数名", "p_code_source": "pCode数据来源", "http_type_support": "目前仅支持 COUNTRY_ALL,CURR_ALL", "column_passed": "表中需要传递的列", "elements_table": "申报要素入库表名", "creditcode": "社会信用代码", "source_col": "来源列", "contextPlaceholder": "请选择上下文", "pCodePlaceholder": "请选择PCode类型", "businessCheck": "业务校验", "dataStorage": "数据入库", "executeAfterStorage": "入库后执行", "seqPreviewAndSetting": "顺序预览与设置", "unit1": "法一单位", "unit2": "法二单位", "taxRule": "税则描述", "regulatoryConditions": "监管条件", "inspmonitorcond": "检验检疫", "ciq": "CIQ", "preShipmentInspect": "装运前检验", "usedMachineBan": "旧机电", "importBan": "禁止", "impDiscount": "最惠国", "impTempRate": "暂定税率", "specialAgreement": "原产国为美国时的特别协定"}, "other": {"pc_form_design": "PC表单设计", "mobile_form_design": "移动表单设计", "pc_form_test": "测试", "pc_form_url": "添加页地址", "placeholder_delClass": "请输入数据删除接口，使用SpringBean的名称", "placeholder_serviceName": "分布式下需要输入对应服务的编码", "form_url_label": "添加页地址", "back_fill": "回车回填", "back_fill_scene": "场景", "back_fill_scene_add": "添加场景配置", "pcode_back_fill": "PCode服务设置", "pcode_service_code": "PCode服务编码", "pcode_service_code_add": "添加PCode服务编码", "pcode_back_fill_field": "回填关联字段", "pcode_back_fill_field_add": "添加回填关联字段", "pcode_back_fill_choose_placeholder": "请选择回显数据字段", "pcode_back_fill_input_placeholder": "请输入pCode返回的key值", "change_fill": "变更回填", "click_fill": "Click回填", "change_event": "不触发变更事件", "detail_no_event": "详情时不触发", "enter_fill_event": "触发回车回填事件", "default_enter_fill": "触发回车回填"}, "api": {"api_settings": "Api服务设置", "api_settings_add": "添加Api服务设置", "path": "请求路径", "input_path": "请输入请求路径", "method": "请求方式", "request_param": "请求参数", "setting": "设置", "param_name": "参数名称", "input_param_name": "请输入参数名称", "param_name_null": "参数名称不能为空", "param_source": "参数来源", "select_param_source": "请选择参数来源", "param_source_null": "参数来源不能为空", "param_value": "参数值", "select_param_value": "请选择参数值", "input_param_value": "请输入参数值", "param_value_null": "参数值不能为空", "erModel": "ER模型", "context": "上下文", "button_datasource": "按钮数据源", "fixed": "固定值", "input_api_key": "请输入Api返回的key值", "type": "类型", "field_value": "字段值", "data_source": "数据源", "label": "标签", "value": "值"}, "sql": {"sql_setting": "Sql服务设置", "sql_setting_add": "添加Sql服务设置", "sql_create": "SQL生成与解析", "setting": "设置", "sql_param": "动态参数来源", "erModel": "ER模型", "context": "上下文", "back_list": "回参列表", "param_code": "参数编码", "param_name": "参数名称", "input_param_name": "请输入参数名称", "table_name": "列表stores对象名称", "input_table_name": "请输入回显列表名称", "param_value": "参数值", "select_param_value": "请选择参数值", "parse_sql": "暂无数据，请先解析SQL", "mapping_sql": "请选择SQL映射字段", "data_filter": "数据过滤", "filter_type": "过滤方式"}, "showHide": {"show_hide": "联动显隐设置", "add_show_hide_scene": "添加联动显隐场景", "linkage_unit": "联动控件", "unit_option": "控件操作", "select_unit": "请选择控件", "after_other_scene": "其他场景完成后执行"}, "scene": {"form_scene": "表单场景时操作", "add_disabled": "新增时禁用", "edit_disabled": "编辑时禁用", "detail_disabled": "查看时禁用", "add_hidden": "新增时隐藏", "edit_hidden": "编辑时隐藏", "detail_hidden": "查看时隐藏"}, "button": {"followOperate": "后续操作", "isLazy": "是否懒加载页面"}, "edit": {"editDefault": "编辑时是否设置默认值"}}, "appreport": {"entity": {"widgetName": "部件名称", "widgetCode": "部件标识", "datasetName": "数据集名称", "datasetCode": "数据集标识", "httpMethod": "请求", "pleaseEnterAddress": "请填入完整地址", "widgetUrl": "访问地址", "datasetId": "数据集", "dimension": "维度", "measure": "度量", "custom": "描述", "alias": "别名", "exp": "表达式", "col": "字段", "type": "符号", "values": "值", "colCode": "列标识", "colName": "列名称", "isQuery": "查询列", "queryOp": "查询操作符", "queryDefaultValue": "默认查询值", "queryIsShow": "默认展开", "showButton": "是否显示按钮", "buttonScript": "按钮自定义脚本", "promptTextSet": "提示文字配置", "promptText": "提示文字脚本", "promptColor": "提示文字颜色"}, "verify": {"widgetName": "请输入部件名称", "widgetCode": "请输入部件标识", "widgetCode_format": "部件标识不能包含汉字", "alias_repeat": "别名不能重复", "datasetName": "请输入数据集名称", "datasetCode": "请输入数据集标识", "alias_null": "别名不能为空", "exp_null": "表达式不能为空", "col_null": "字段不能为空", "type_null": "符号不能为空", "values_null": "值不能为空", "datasetCode_format": "数据集标识不能包含汉字"}, "other": {"report_widget": "报表部件", "report_datase": "数据集", "viewData": "查看数据", "generateJSON": "生成JSON", "tab_basic": "基本信息", "tab_dimension": "维度和度量", "tab_measure": "计算度量", "tab_filter": "过滤条件", "tab_query": "查询条件"}}, "appview": {"entity": {"viewRedirectId": "跳转数据id", "modifyExport": "修改导出", "viewName": "页面名称", "viewCode": "页面编码", "otherUploadUrl": "上传地址", "afterUploadInfo": "上传成功提示", "fileSize": "文件大小限制", "viewType": "页面类型", "viewStatus": "页面状态", "colCode": "列标识", "colName": "列名称", "colAlias": "列别名", "mergeHeaderName": "合并表头名", "colType": "列类型", "alignType": "对齐方式", "colWidth": "列宽度", "colMaxWidth": "列最大宽度", "isHidden": "显示", "isExport": "可导出", "isFilter": "可过滤", "exportSetting": "修改导出配置项", "isEdit": "是否可修改", "isRequire": "是否必填", "import_require": "Excel必填(导入)", "import_require_help": "", "removeTailingZeros": "是否去零", "autoHeight": "自动高度", "fixed": "固定列", "isEllipsis": "超宽省略", "tuoMin": "脱敏", "actionConfig": "动作配置", "isImport": "可导入", "isRelated": "是否关联", "importRules": "导入规则", "importRulesAdd": "新增导入规则", "importRulesUpdate": "修改导入规则", "importRulesDelete": "删除导入规则", "importRulesSave": "新增和修改导入规则", "importSetting": "导入配置", "isSort": "可排序", "isAsyncImport": "异步导入", "asyncImportRules": "异步导入规则", "add": "新增", "replace": "替换", "update": "修改", "delete": "ลบ", "save": "新增和修改", "isImportAdd": "新增", "isImportUpdate": "修改", "isImportDelete": "ลบ", "isImportSave": "新增和修改", "queryAlias": "查询别名", "isQuery": "查询列", "isDefaultQuery": "默认查询列", "queryOp": "查询操作符", "queryDefaultValue": "默认查询值", "queryIsHidden": "默认展开", "queryOrder": "定制排序", "openType": "操作类型", "shadowTradeCode": "所属企业", "shadowType": "影子类型", "shadowParentId": "替换按钮", "buttonName": "按钮名称", "buttonGroup": "按钮组", "buttonType": "按钮类型", "buttonStyle": "按钮样式", "buttonIcon": "按钮图标", "buttonIconClass": "按钮图标颜色", "viewIcon": "列表图标", "dialogSize": "打开方式", "linkForm": "关联表单", "linkView": "关联页面", "exportDetailViewName": "导出明细表体列表", "objectName": "ER模型", "isMultiple": "是否禁用多选", "outParameter": "出参配置", "showCondition": "显示条件", "detailVisible": "查看时可见", "buttonAction": "按钮动作", "asyncImport": "异步导入", "confirmBox": "确认框", "isExpand": "按钮平铺", "relaWarnItem": "关联预警项", "warnCustomize": "预警条件设置", "isPermission": "权限控制", "permission": "权限标识", "openScript": "自定义脚本", "showScript": "显示脚本", "watchScript": "watch脚本", "butScriptDetails": "前后处理", "parameterKey": "入参标识", "parameterName": "入参描述", "parameterType": "参数类型", "isRequired": "非空校验", "requireType": "校验类型", "outKey": "出参标识", "sqlOrder": "自定义SQL排序", "defaultQuery": "默认查询", "checkType": "复选框", "isFlow": "是否流程", "flowScope": "流程范围", "refreshAfterView": "编辑查看后刷新", "showSaveQuery": "是否启用保存查询条件", "isLineNum": "行号", "isPage": "分页", "pageNum": "分页大小", "actionWidth": "操作列宽度", "scrollEnable": "横向滚动", "scrollWidth": "滚动宽度", "fixedAction": "固定操作列", "fixedBpmColumn": "固定流程列", "queryConditionShow": "查询条件默认展开", "breadCrumbShow": "导航面包屑是否显示", "queryParamsNum": "查询条件每行显示数量", "isPageSize": "是否保存分页大小", "isHeadBorder": "是否启用表头边框", "sqlExecutor": "sql执行器", "nestedTables": "是否启用嵌套子表格", "settingNestedTables": "嵌套子表格", "nestedTablesName": "嵌套子表格名称", "mappingRelations": "映射关系", "tableFieldName": "请选择父表栏位名称", "mappingFieldName": "请输入子表映射字段名称", "pageMountedScript": "页面加载完事件", "tagClick": "tag click 事件", "tagClose": "tag close event 事件", "handleResetQuery": "重置搜索事件", "loadApiScript": "加载外部Api数据Js事件", "beforeScript": "加载前Js事件", "afterScript": "加载后Js事件", "beforeJava": "加载前Java事件", "afterJava": "加载后Java事件", "beforeGroovy": "加载前Groovy事件", "afterGroovy": "加载后Groovy事件", "customRowFunc": "自定义行属性", "customRowClick": "行选中事件", "showLocation": "显示位置", "isLoading": "是否使用Loading", "buttonVerify": "按钮校验", "customSqlParam": "按钮跳转列表自定义参数", "selectCustomView": "请选择跳转前的页面", "paramSource": "参数来源", "paramSource_search": "查询条件", "paramSource_col": "列表参数", "param": "参数", "selectParam": "请选择参数", "paramText": "解析到的动态参数为", "changeLinkage": "变更联动", "importIndex": "排序", "tian-xie-shuo-ming": "填写说明"}, "changeLinkage": {"enable": "是否启用", "scene": "场景设置", "query": "查询条件", "field_add": "添加关联查询条件", "field": "关联查询条件", "linkage_setting": "联动设置", "field_choose": "请选择关联查询条件"}, "verify": {"parameterKey_repeat": "入参标识不能重复", "viewName": "请输入页面名称", "viewCode": "请输入页面编码", "viewType": "请选择页面类型", "dataSourceDefault": "请选择数据源", "colCode": "列标识不能为空", "colName": "列名称不能为空", "colType": "列类型不能为空", "openType": "操作类型不能为空", "buttonName": "按钮名称不能为空", "parameterKey": "入参标识不能为空", "parameterName": "入参描述不能为空", "parameterType": "参数类型不能为空", "sql_parse": "返回字段必须包含id，否则无法正确解析页面，请重新编辑SQL", "link_page": "请先配置关联页面", "viewCode_format": "页面编码不能包含汉字"}, "other": {"handleAddLayout": "新增组合页面", "handleAddDragLayout": "新增自由组合页面", "handleAddTable": "新增列表页面", "handleAddTree": "新增树形页面", "handleAddMobile": "新增移动页面", "pageUrl": "访问地址", "basic_info": "基本信息", "table_config": "列表显示", "query_config": "查询条件", "button_config": "按钮配置", "shadow_button_config": "影子按钮配置", "parameter_config": "入参声明", "parse_sql_tip": "提示：解析到的查询表为", "sqlOrder_tip": "请输入排序语句，例如: 字段1 desc, 字段2 asc", "parse_sql_help": "查询中可使用表达式：#{'{'}parameter.xx{'}'}，xx表示的变量包括：通用上下文变量：[ userId、loginName、deptId、deptCode、orgId、orgCode、currentUserName、currentLoginName、currentUser、currentOrgName、currentOrg、currentDeptName、currentDept、currentCompCode、currentCompName、currentSocialCreditCode ]；跳转列表时专用变量：[ previousIds(前一列表勾选的值，pgSql用法示例：string_to_array(id, ',') <{'@'} ARRAY[string_to_array(#{'{'}parameter.previousIds{'}'}, ',')]  )；其他变量可通过按钮跳转列表自定义参数设置获取]", "button_script_help": "脚本中可使用上下文：${'{'}xx{'}'}，xx表示的变量包括：通用上下文变量：[ currentUser、currentUserName、currentLoginName、currentOrg、currentOrgName、currentDept、currentDeptName、currentCompCode、currentCompName、currentSocialCreditCode、currentDivisionId、currentDivisionCode、currentDivisionName、currentTenantId、currentTenantCode、currentTenantName ]", "java_event_tip": "请使用SpringBean", "nothing": "无", "permission_help": "菜单中配置的授权标识是【页面编码:权限标识】，此处填写的仅是权限标识", "queryDefaultValue_help": "支持{'$'}{'{'}变量{'}'}", "column_verification": "字段校验", "sql_verification": "sql校验", "interface_verification": "接口校验"}, "label": {"single": "必须选中且只能选中一条数据", "multiple": "ต้องตรวจสอบข้อมูลอย่างน้อยหนึ่งรายการ", "uncheck": "允许不选中数据"}}, "apptemplate": {"entity": {"tempName": "模板名称", "tempCode": "模板标识", "tempType": "模板类型", "tempStructure": "适用数据结构", "tempBodyString": "模板脚本", "tempTypeModel": "应用类型", "tempDesc": "模板描述"}, "verify": {"formName": "请输入模板名称", "formCode": "请输入模板标识", "formName_repeat": "模板名称已存在"}, "other": {}}, "appmarket": {"entity": {"appName": "应用名称", "appCode": "应用编码", "appVersion": "应用版本", "createTime": "上传时间"}, "verify": {}, "other": {"install": "安装", "update": "更新", "confirm_install": "确定安装吗", "confirm_update": "确定更新吗"}}, "formDesign": {"label": {"search_params": "查询条件", "clean_selected": "清空已选", "search_setting": "设置查询列", "gw_setting": "关务运维设置", "expand_column": "拓展字段设置", "Tab1": "页签1", "Tab2": "页签2", "Tab": "页签", "Collapse": "折叠面板", "Collapse1": "折叠面板1", "Collapse2": "折叠面板2", "Collapse3": "折叠面板3", "Add_tab": "增加页签", "title_name": "名称", "hidden_setting": "默认隐藏", "lazy_setting": "懒加载", "max_length": "最大长度", "integer_number": "整数位", "decimal_number": "小数点位", "Add": "添加", "show_arrow": "显示箭头", "validatorMsg_only": "唯一性校验", "single": "唯一", "Add_regular_check": "增加正则校验", "Add_length_check": "增加长度校验", "Add_single_check": "增加唯一校验", "Add_group_check": "增加组合校验", "placeholder_select": "请选择", "placeholder_input": "请输入", "required_message": "不可为空", "colLength_message": "填写内容超过长度", "colLength_message1": "长度不能超过", "colLength_message2": "位长度!", "colLength_num_message1": "必须为数字,整数位最大", "colLength_num_message2": "位,小数最大", "colLength_num_message3": "位!", "required": "必填", "blue_required": "蓝星校验", "red_required": "红星校验", "first_no_check": "蓝星不检验", "validScript": "自定义校验", "disabled": "不可编辑", "fixColum": "是否固定列", "buttonConfig": "详细配置", "isDefault": "系统默认", "TableField_title": "字段设置", "tempKey": "虚拟字段", "tempKey_content": "虚拟字段请以 _temp_ 开头，否则入库时会存在异常", "min_width": "最小宽度", "max_width": "最大宽度", "total": "合计", "total_now": "按当前页统计", "total_all": "全部数据统计", "align": "对齐", "custom_button_title": "自定义按钮配置", "custom_query_column": "自定义查询配置", "init_query_column": "初始化查询列", "confirm_init_query_column": "确认需要清空自定义查询配置中的查询条件吗", "yulan": "预览", "generateJSON": "生成JSON", "useTemplate": "使用模板", "defaultModel": "默认对象", "clean": "已清空", "panel_er": "ER模型", "panel_kj": "控件", "panel_gjkj": "高级控件", "panel_bjkj": "布局控件", "biao-dan-shu-xing-she-zhi": "表单属性设置", "biao-dan-bu-ju": "表单布局", "shui-ping": "水平", "chui-zhi": "垂直", "hang-nei": "行内", "lie-kuan-she-zhi-gong-24": "列宽设置（共24）", "biao-qian-zhan": "标签占:", "shu-ru-kuang-zhan": "输入框占:", "chang-yong-lie-kuan-kuai-su-she-zhi": "常用列宽快速设置", "biao-dan-css": "表单CSS", "shi-fou-dan-yi-chuang-kou": "是否单一窗口样式", "biao-dan-shu-xing": "表单属性", "yin-cang-bi-xuan-biao-ji": "隐藏必选标记", "selfUpdate": "局部渲染", "isRecordDataLog": "记录数据日志", "an-niu-shu-xing": "按钮属性", "yin-cang-biao-dan-an-niu-lan": "隐藏表单按钮栏", "shi-jian-she-zhi": "事件设置", "fieldConfig": "栏位配置(页面加载后执行)", "xin-zeng-qian": "新增前", "xin-zeng-hou": "新增后", "geng-xin-qian": "更新前", "geng-xin-hou": "更新后", "shi-ji-yu-lan-xiao-guo-qing-dian-ji-yu-lan-cha-kan": "实际预览效果请点击预览查看", "kong-jian-shu-xing-she-zhi": "控件属性设置", "wei-xuan-ze-kong-jian": "未选择控件", "biao-qian": "标签", "shang-chuan-shu-xing": "上传属性", "shi-yong-gong-gong-biao-cun-chu-wen-jian-lu-jing": "使用公共表存储文件路径", "shu-ju-shu-xing": "数据属性", "guan-lian-shu-ju-zi-duan": "关联数据字段", "shu-ju-dui-xiang": "数据对象", "shu-ju-zi-duan": "数据字段", "shu-ju-biao-shi": "数据标识", "lian-dong-shu-xing": "联动属性", "shu-ju-zhuan-huan": "数据转换", "qing-xuan-ze-shang-ji-kong-jian": "请选择上级控件", "zuo-wei-html-xian-shi": "作为html显示", "xian-shi-shu-xing": "显示属性", "shu-ju-yuan-chu-shi-hua": "数据源初始化", "an-niu-lian-dong-cha-xun": "按钮联动查询", "biao-ge-she-zhi": "表格设置", "breadCrumbSetting": "面包屑设置", "queryConditionsShow": "查询条件默认展开", "ke-zeng": "可增", "ke-shan": "可删", "ke-bianji": "可编辑", "hideSaveButton": "隐藏保存按钮", "ke-tuo-zhuai": "可拖拽", "chu-shi-hua": "初始化", "xin-zeng-hang-wei-zhi": "新增行位置", "pai-xu-zi-duan": "排序字段", "biao-qian-wei-zhi": "标签位置", "biao-qian-ju-li": "标签距离", "an-niu-cao-zuo": "按钮操作", "choose-an-niu-cao-zuo": "请选择按钮操作", "ti-jiao-biao-dan": "提交表单", "zhong-zhi-biao-dan": "重置表单", "guan-bi-biao-dan": "关闭表单", "saveAndContinue": "保存并继续", "left": "左", "right": "右", "jiao-ben": "脚本", "show-condition": "显示条件", "xiao-yan": "校验", "jin-yong": "禁用", "set-value": "设置值", "cao-zuo-shu-xing": "操作属性", "jin-zhi-shou-shu": "禁止手输", "force-dropdown-value": "不显示非下拉数据", "user-conversion": "用户账号转换", "xian-shi-shu-ru-kuang": "显示输入框", "zhi-xuan-bu-men": "只选部门", "ke-guan-bi": "可关闭", "gao-du": "高度", "kuan-du": "宽度", "zi-shi-ying-nei-rong-gao-du": "自适应内容高度", "relatedSteps": "对应步骤条", "relatedStepsNotNull": "related steps not null", "qi-yong-fu-gai-quan-ju-shang-de-she-zhi": "启用，覆盖全局上的设置", "zha-ge-jian-ju": "栅格间距", "lie-pei-zhi-xiang": "列配置项", "mo-ren-di-yi-ge-ye-qian": "默认第一个页签", "ye-qian-pei-zhi-xiang": "页签配置项", "zhe-die-mian-ban-pei-zhi-xiang": "折叠面板配置项", "cardExtraConfig": "卡片插槽配置项", "zhe-die-mian-ban-yin-cang-jiao-ben": "折叠面板隐藏脚本", "ye-mian-bian-ma-pei-zhi": "页面编码配置", "view-allow-multiple": "列表允许多选", "biao-dan-bian-ma-pei-zhi": "表单编码配置", "dang-qian-yong-hu": "当前用户", "dang-qian-zu-zhi": "当前组织", "gen-jie-dian": "根节点", "quan-xian": "权限", "guo-lv-quan-xian": "过滤权限", "checkStrictly": "是否父子联动", "defaultExpandAll": "是否默认展开所有节点", "treeData": "设置树形组件数据", "nodeName": "节点名称", "requireNodeName": "请输入节点名称", "nodeKey": "节点key", "requireNodeKey": "请输入节点key", "repeatedNodeKey": "节点key不能重复", "parentKey": "父级节点", "isDisable": "是否禁用", "initUrl": "初始化url设置", "initParamScript": "初始化url参数脚本", "clickUrl": "点击url", "select_option": "选项配置(优先于数据字典)", "select_dictOption": "数据字典(优先于Sql配置)", "select_sqlConfig": "Sql配置(优先于服务配置)", "select_service": "服务配置(优先于api数据源)", "api_service": "api数据源", "watch_setting": "数据监控设置", "filed_setting": "栏位设置", "shu-ru-kuang-type": "输入框type", "dang-qian-shi-jian": "当前时间", "dang-qian-ri-qi": "当前日期", "lei-xing": "类型", "wai-jian-zi-duan": "外键字段", "fu-zhu-miao-shu": "辅助描述", "xian-shi-tu-biao": "显示图标", "wu-bian-kuang": "无边框", "wen-zi-dui-qi-fang-shi": "文字对齐方式", "xian-shi-bi-xuan-biao-ji": "显示必选标记", "wen-zi-da-xiao": "文字大小", "wen-zi-jia-cu": "文字加粗", "wen-zi-yan-se": "文字颜色", "bei-jing-yang-shi": "背景样式", "wen-zi-yang-shi": "自定义样式", "wai-kuang-yang-shi": "父容器样式", "qian-tu-biao": "前图标", "hou-tu-biao": "后图标", "ye-wu-ti-xing": "业务提醒", "chao-chu-fan-wei-ti-shi": "超出范围提示", "biao-ge-yang-shi-css": "表格样式CSS", "xian-shi-pu-tong-bian-kuang": "显示普通边框", "xian-shi-hong-se-bian-kuang": "显示红色边框", "xian-shi-bian-kuang": "显示边框", "accordion": "手风琴模式", "arrow_alignment": "箭头图标对齐方式", "shu-biao-jing-guo-dian-liang": "鼠标经过点亮", "jin-cou-xing": "紧凑型", "qing-dian-ji-you-jian-zeng-jia-hang-lie-huo-zhe-he-bing-dan-yuan-ge": "请点击右键增加行列，或者合并单元格", "selectOids": "选中行数据主键", "dang-qian-yong-hu-xing-ming": "当前用户姓名", "dang-qian-yong-hu-deng-lu-ming": "当前用户登录名", "dang-qian-yong-hu-id": "当前用户ID", "dang-qian-zu-zhi-id": "当前组织ID", "dang-qian-bu-men-id": "当前部门ID", "dang-qian-bu-men-ming-cheng": "当前部门名称", "dang-qian-qi-ye-bian-ma": "当前企业编码", "dang-qian-qi-ye-ming-cheng": "当前企业名称", "dang-qian-qi-ye-she-hui-xin-yong-dai-ma": "当前企业社会信用代码", "dang-qian-shi-ye-bu-id": "当前事业部ID", "dang-qian-shi-ye-bu-code": "当前事业部Code", "dang-qian-shi-ye-bu-name": "当前事业部Name", "dang-qian-zu-hu-id": "当前租户ID", "dang-qian-zu-hu-code": "当前租户编码", "dang-qian-zu-hu-name": "当前租户名称", "uuid": "UUID", "cong-ru-can-huo-qu": "从入参获取", "suo-xuan-shu-ju-dui-xiang-wu-xiao": "所选数据对象无效", "que-ding-zhi-hang-chu-shi-hua-ma": "确定执行初始化吗？", "localSystem": "浮云平台", "declarePort": "申报端", "customsConfig": "业务系统", "otherConfig": "第三方", "shi-jian-ge-shi-ru": "时间格式如", "mo-ren-xuan-zhong-xiang-key": "默认选中项(key)", "1-biao-shi-bu-she-zhi-gu-ding-gao-du": "-1表示不设置固定高度", "1-biao-shi-zui-hou-yi-hang-0-biao-shi-di-yi-hang": "-1表示最后一行，0表示第一行", "kong-jian-lei-xing": "控件类型", "dang-qian-zu-zhi-ming-cheng": "当前组织名称", "cong-zuo-ce-xuan-ze-kong-jian-tian-jia": "从左侧选择控件添加", "xiang-xia-he-bing": "向下合并", "xiang-you-he-bing": "向右合并", "zeng-jia-yi-lie": "增加一列", "zeng-jia-yi-hang": "增加一行", "dang-qian-shi-zui-hou-yi-hang-wu-fa-xiang-xia-he-bing": "当前是最后一行，无法向下合并", "dang-qian-biao-ge-wu-fa-xiang-xia-he-bing": "当前表格无法向下合并", "dang-qian-shi-zui-hou-yi-lie-wu-fa-xiang-you-he-bing": "当前是最后一列，无法向右合并", "dang-qian-biao-ge-wu-fa-xiang-you-he-bing": "当前表格无法向右合并", "json-shu-ju": "JSON数据", "dao-ru-json-wen-jian": "导入json文件", "dao-ru-cheng-gong": "导入成功", "dao-ru-shi-bai-shu-ju-ge-shi-bu-dui": "导入失败，数据格式不对", "dao-chu-dai-ma": "导出代码", "xian-shi-ming-cheng-bu-neng-wei-kong": "显示名称不能为空", "an-niu-yang-shi-bu-neng-wei-kong": "按钮样式不能为空", "an-niu-tu-biao-bu-neng-wei-kong": "按钮图标不能为空", "dong-zuo-lei-xing-bu-neng-wei-kong": "动作类型不能为空", "chu-shi-hua-xin-xi-bu-zheng-que-mo-ren-an-niu-diu-shi": "初始化信息不正确，默认按钮丢失", "ti-xing-lie-biao-zi-duan-huan-wei-chu-shi-hua": "提醒：列表字段还未初始化", "mei-you-xuan-zhong-shu-ju": "没有选中数据", "Not_allowed_to_be_empty": "参数 {0} 不允许为空", "mo-ren-an-niu-bu-neng-shan-chu": "默认按钮不能删除", "dong-zuo-lei-xing": "动作类型", "shu-xing-pei-zhi": "属性配置", "duo-xuan": "多选", "qing-xuan-ze-guan-lian-biao-dan": "请选择关联表单", "qing-xuan-ze-da-kai-fang-shi": "请选择打开方式", "shu-ju-bu-cun-zai": "数据不存在", "kai": "开", "guan": "关", "Before_open": "打开前事件", "Before_load": "加载前事件", "After_load": "加载后事件", "Before_save": "保存前事件", "After_save": "保存后事件", "Form_item_click": "表单栏位点击事件", "event_change": "数据监控", "is_event_change": "是否触发数据监控", "afterDeleteFile": "文件删除后事件", "beforeUploadFile": "文件上传前事件", "beforeDeleteFile": "文件删除前事件", "event_blur": "失去焦点", "event_enter": "回车事件", "event_click": "点击事件", "xin-zeng-qian-shi-jian": "新增前事件", "xin-zeng-hou-shi-jian": "新增后事件", "shan-chu-qian-shi-jian": "删除前事件", "shan-chu-hou-shi-jian": "删除后事件", "bian-ji-shi-jian": "编辑事件", "zi-ding-yi-hang-yang-shi": "自定义行样式", "zi-ding-yi-dan-yuan-ge-yang-shi": "自定义单元格样式", "dan-yuan-ge-dian-ji-shi-jian": "单元格点击事件", "zi-ding-yi-he-bing-han-shu": "自定义合并函数", "zi-ding-yi-shu-ju-chu-li-han-shu": "自定义数据处理函数", "zhi": "值", "wei-pei-zhi-shi-jian-dai-ma": "未配置事件代码", "xiao-yan-zhong": "校验中", "shu-ru-xiang-bu-fu-he-yao-qiu": "输入项不符合要求", "mei-you-pei-zhi-guan-lian-biao-dan": "没有配置关联表单", "mei-you-pei-zhi-zi-ding-yi-jiao-ben": "没有配置自定义脚本", "vxe_error": "第 {0} 行 {1} 校验错误：{2}", "huo-qu-shu-ju": "获取数据", "ye-mian-zi-duan": "页面字段", "biao-dan-zi-duan": "表单字段", "qing-xian-pei-zhi-ye-mian-bian-ma": "请先配置页面编码", "qing-xian-pei-zhi-xian-shi-zi-duan": "请先配置显示字段", "dan-ji-huo-tuo-dong-wen-jian-dao-ci-qu-yu": "单击或拖动文件到此区域", "shi-fou-shan-chu-filename": "ลบ {0}?", "infofilename-shang-chuan-shi-bai": "{0} ไม่สามารถอัปโหลด", "zui-da-shang-chuan-shu-liang-wei-thisrecordoptionslimit": "จำนวนการอัปโหลดสูงสุดคือ {0}", "biao-ti": "标题", "ti-shi-yu": "提示语", "multipleType": "多选类型", "enableConfirmBox": "是否启用确认框", "nei-rong": "内容", "jian-zhi-dui-zhuan-huan": "键值对转换", "dan-tiao-shu-ju-zhuan-huan": "单条数据转换", "isFormTip": "完成服务参数和服务返回值的配置之后，点击刷新，重新渲染控件", "zhong-xin-xuan-ran-fu-wu-pei-zhi": "重新渲染服务配置", "preview_pcode_data": "预览pcode数据", "fu-wu-can-shu": "服务参数", "exprTitle": "可输入常量值或表达式，表达式包括{0}、{1}、{2}、{3}、{4}、{5}、{6}、{7}、{8}、{9}，如果是单体数据转换要传递当前值，可使用{10}", "qing-shu-ru-can-shu-zhi": "请输入参数值", "fu-wu-fan-hui-zhi": "服务返回值", "fan-hui-zhi-jiao-ben": "返回值脚本", "httpTab1": "返回值脚本是一段Groovy脚本，采用fastjson的取值写法，初始对象是 body，返回类型是 List<Map<String, String>>，Map中包含key和value两个属性。注意，可以从脚本编辑框的帮助信息中复制初始脚本。", "httpTab2": "返回值脚本是一段Groovy脚本，采用fastjson的取值写法，初始对象是 body，返回类型是 String。注意，可以从脚本编辑框的帮助信息中复制初始脚本。", "xin-jie-dian": "新节点", "bu-neng-shan-chu-zui-hou-yi-ge-gen-jie-dian": "不能删除最后一个根节点", "biao-shi": "标识", "miao-shu": "描述", "qing-shu-ru-miao-shu": "请输入描述", "qing-shu-ru-zi-ding-yi-sql": "请输入自定义SQL", "tian-jia-cha-xun-zi-duan": "添加查询字段", "tian-jia-cha-xun-zhi": "添加查询值", "zhu-jian-zi-duan": "主键字段", "qing-xuan-ze-zhu-jian-zi-duan": "请选择主键字段", "qing-xuan-ze-wai-jian-zi-duan": "请选择外键字段", "xun-huan-tiao-yong": "循环调用", "zhu-biao": "主表", "qing-shu-ru-zhu-biao": "请输入主表", "ke-fou-gou-xuan": "可否勾选", "zi-dong-zhan-kai-ceng-ji": "自动展开层级", "biao-ming": "表名", "qing-shu-ru-biao-ming": "请输入表名", "gen-jie-dian-zhi": "根节点值", "qing-shu-ru-gen-jie-dian-zhi": "请输入根节点值", "qing-xuan-ze-pai-xu-zi-duan": "请选择排序字段", "pai-xu-fang-shi": "排序方式", "qing-xuan-ze-pai-xu-fang-shi": "请选择排序方式", "yun-xu-xin-zeng": "允许新增", "guan-lian-xin-zeng-biao-dan": "关联新增表单", "qing-xuan-ze-xin-zeng-biao-dan": "请选择新增表单", "xin-zeng-bie-ming": "新增别名", "qing-shu-ru-xin-zeng-bie-ming": "请输入新增别名", "yun-xu-bian-ji": "允许编辑", "guan-lian-bian-ji-biao-dan": "关联编辑表单", "qing-xuan-ze-bian-ji-biao-dan": "请选择编辑表单", "bian-ji-bie-ming": "编辑别名", "qing-shu-ru-bian-ji-bie-ming": "请输入编辑别名", "yun-xu-shan-chu": "允许删除", "qing-xuan-ze-er-mo-xing": "请选择ER模型", "shan-chu-qian-jiao-ben": "删除前脚本", "guo-lv-tiao-jian": "过滤条件", "qing-shu-ru-guo-lv-tiao-jian": "请输入过滤条件", "da-kai-fang-shi": "打开方式", "zheng-xu": "正序", "dao-xu": "倒序", "qing-shu-ru-zi-ding-yi-jiao-ben": "请输入自定义脚本", "qing-shu-ru-xian-shi-jiao-ben": "请输入显示脚本", "qian-chu-li-jiao-ben": "前处理脚本", "qing-shu-ru-qian-chu-li-jiao-ben": "请输入前处理脚本", "hou-chu-li-jiao-ben": "后处理脚本", "refreshAfterScript": "执行后刷新列表", "tabHeadId": "组合页面传主键值", "conditionField": "选中数据值", "qing-shu-ru-hou-chu-li-jiao-ben": "请输入后处理脚本", "dong-zuo-pei-zhi": "动作配置", "qian-chu-li-lei": "前处理类", "hou-chu-li-lei": "后处理类", "qing-xuan-ze-shu-ju-zhuan-huan-fang-shi": "请选择数据转换方式", "shu-ju-zi-dian-ming-cheng-zhuan-code": "数据字典名称转Code", "yong-hu-ming-cheng-zhuan-id": "用户名称转ID", "yong-hu-ming-cheng-zhuan-code": "用户名称转Code", "bu-men-ming-cheng-zhuan-id": "部门名称转ID", "bu-men-ming-cheng-zhuan-code": "部门名称转Code", "tong-guo-zi-ding-yi-lei-zhuan-huan": "通过自定义类转换", "tong-guo-zi-ding-yi-jiao-ben-zhuan-huan": "通过自定义脚本转换", "shu-ju-ku-biao": "数据库表", "yuan-zi-duan": "源字段", "qing-xuan-ze-zhuan-huan-qian-dui-ying-de-zi-duan": "请选择转换前对应的字段", "jing-tai-xuan-xiang-ming-cheng-zhuan-code": "静态选项名称转Code", "shu-ju-ku-biao-zi-duan-zhuan-huan": "数据库表字段转换", "mu-biao-zi-duan": "目标字段", "qing-xuan-ze-yao-zhuan-huan-de-mu-biao-zi-duan": "请选择要转换的目标字段", "zi-ding-yi-zhuan-huan-lei": "自定义转换类", "zi-ding-yi-zhuan-huan-jiao-ben": "自定义转换脚本", "groovy-jiao-ben": "Groovy脚本", "bi-tian-xiao-yan": "必填校验", "ti-shi-xin-xi": "提示信息", "wei-yi-xiao-yan": "唯一校验", "excel-wei-yi-xiao-yan": "Excel唯一校验", "public-configuration": "公共配置", "only_combination": "唯一组合校验", "chang-du-xiao-yan": "长度校验", "jing-du-xiao-yan": "精度校验", "qing-shu-ru-zui-da-chang-du": "请输入最大长度", "qing-shu-ru-zui-da-zheng-shu": "请输入最大整数位", "qing-shu-ru-zui-da-xiao-shu": "请输入最大小数位", "qu-jian-xiao-yan": "区间校验", "qu-jian": "区间", "zui-xiao-zhi": "最小值", "zui-da-zhi": "最大值", "zheng-ze-xiao-yan": "正则校验", "zheng-ze-zhi": "正则值", "qing-xuan-ze-zheng-ze-zhi": "请选择正则值", "pcode-xiao-yan": "pCode校验", "pcode-ru-ku": "pCode入库类型", "zi-ding-yi-lei": "自定义类", "zi-ding-yi-groovy-jiao-ben": "自定义Groovy脚本", "dao-ru-gui-ze-pei-zhi": "导入规则配置", "zhuan-huan-gui-ze": "转换规则", "xiao-yan-gui-ze": "校验规则", "chu-can-pei-zhi": "出参配置", "suo-xuan-ye-mian-mo-xing-wei-she-zhi-ru-can-xin-xi": "所选页面模型未设置入参信息", "parameterTip": "所选页面模型入参信息已更新，请重新配置后保存", "xuan-ze-lie-biao-huo-shu": "选择列表或树", "xuan-ze-bao-biao": "选择报表", "xuan-ze-biao-dan": "选择表单", "lie-shu-xing": "列属性", "geng-gai-ye-qian-ming": "更改页签名", "lian-dong-she-zhi": "联动设置", "biao-dan-she-zhi": "表单设置", "lie-biao": "列表", "shu": "树", "ye-qian-ming": "页签名", "xiang-ying-qi-ta-mo-xing-lian-dong": "响应其它模型联动", "kai-qi-mo-ren": "开启(默认)", "lian-dong-qi-ta-mo-xing": "联动其它模型", "xian-yin-gui-ze": "显隐规则", "te-shu-chu-li": "是否一对一特殊处理", "breadCrumbContent": "面包屑内容", "xian-shi-mo-ren": "显示(默认)", "bao-cun-hou-xian-shi": "保存或选择主表后显示", "hidden": "隐藏", "biao-dan-an-niu-lan": "表单按钮栏", "zi-dong": "自动", "yin-cang": "隐藏", "ke-bian-ji": "可编辑", "wei-hu": "维护", "zhi-du": "只读", "ye-mian-lei-xing": "页面类型", "gu-ding-gao-du": "固定高度", "lie-index1": "列{0}", "zan-bu-zhi-chi": "暂不支持", "mo-ban-lie-biao": "模板列表", "ke-yong-zi-duan": "可用字段", "ke-yong-biao-da-shi": "可用表达式", "ke-yong-ji-suan-fu": "可用计算符", "biao-da-shi": "表达式", "qing-shu-ru-biao-da-shi": "请输入表达式", "qing-shu-ru-cha-xun-zhi": "请输入查询值", "cha-xun-zhi": "查询值", "qing-shu-ru-ying-yong-ming-cheng-cha-xun": "请输入应用名称查询", "chang-yong-ying-yong-thiscustomapplistlength": "常用应用({0})", "qing-xuan-ze-yao-sheng-cheng-dai-ma-de-ying-yong": "请选择要生成代码的应用!", "qing-she-zhi-urldelete-shu-xing": "请设置url.delete属性!", "bao-cun-cheng-gong": "บันทึกเรียบร้อยแล้ว", "biao-dan-ye-mian": "表单页面", "biao-dan": "表单", "dian-ji-huo-tuo-zhuai-wen-jian-dao-ru": "点击或拖拽文件导入", "import_button_tip": "下方按钮可下载导入模板，导入数据应符合导入模板要求", "xia-zai-dao-ru-mo-ban": "下载导入模板", "ju-ti-xiang-qing-qing": "具体详情请", "dian-ji-xia-zai": "点击下载", "dao-ru-mo-ban": "导入模板", "wen-jian-xia-zai-shi-bai": "文件下载失败", "no-data": "ไม่มีข้อมูลที่สามารถส่งออกได้!", "mei-you-tong-ji-shu-ju": "没有统计数据", "xin-zeng-gen-jie-dian": "新增根节点", "xin-zeng-zi-jie-dian": "新增子节点", "checkFks_error": "没有主表记录，请先保存或选择主表记录", "xin-zeng-biao-dan": "新增表单", "bian-ji-biao-dan": "编辑表单", "wei-qi-dong": "未启动", "liu-zhuan-zhong": "流转中", "yi-jie-shu": "已结束", "wo-fa-qi": "我发起", "ren-wu-zhuang-tai": "任务状态", "wo-can-yu": "我参与", "no_form_no_view": "没有配置关联表单和关联页面", "mei-you-pei-zhi-er-mo-xing": "没有配置ER模型", "mei-you-pei-zhi-guan-lian-ye-mian": "没有配置关联页面", "cha-kan-biao-dan": "查看表单", "cha-kan-shu-ju": "查看数据", "gai-gong-neng-bu-zhi-chi-shi-yong-nei-bu-ye-qian-da-kai": "该功能不支持使用新页签打开方式", "zan-bu-zhi-chi-zi-biao-qian-tao-dan-chu": "暂不支持子表嵌套弹出", "excelMaxTip": "是否确认导出数据？提示：若您有选中数据，将只导出您选中的数据；若您有查询条件，将只导出符合查询条件的数据；数据的最大导出量为 {0} 条！", "dui-hua-kuang": "对话框", "qing-shu-ru-biao-ming-cheng": "请输入表名称", "qing-xuan-ze-shu-ju": "请选择数据", "xuan-ze-shu-ju-zi-dian": "选择数据字典", "qing-zai-dang-qian-ye-xuan-ze-shu-ju": "请在当前页选择数据", "shu-ju-wu-xiao-pcurl-wei-pei-zhi": "数据无效，pcUrl未配置", "zan-shi-bu-zhi-chi-duo-xuan": "暂时不支持多选", "xuan-ze-fu-wu-pei-zhi": "选择服务配置", "xuan-ze-er-mo-xing": "选择ER模型", "xuan-ze-shu-ju-ji": "选择数据集", "chooseDatasetType": "选择数据集类型", "xuan-ze-sql-pei-zhi": "选择Sql配置", "xuan-ze-biao": "选择表", "xuan-ze-ye-mian": "选择页面", "bian-ma-ming-cheng": "编码/名称", "sql-pei-zhi-bian-ma": "Sql配置编码", "sql-pei-zhi-ming-cheng": "Sql配置名称", "qing-shu-ru-sql-pei-zhi-bian-ma-huo-ming-cheng": "请输入Sql配置编码或名称", "ye-mian": "页面", "liu-cheng": "流程", "bao-biao": "报表", "qing-xuan-ze-suo-shu-ying-yong": "请选择所属应用", "wen-zi-lai-yuan-yu-shu-ju-zi-duan": "文字来源于数据字段", "yi-xuan": "已选", "xuan-ze-shu-ju": "选择数据", "dragModel": "拖动页面模型到此处", "bi-tian-xiao-yan-ti-shi-xin-xi": "必填校验提示信息", "dan-hang-wen-ben": "单行文本", "duo-hang-wen-ben": "多行文本", "zheng-shu": "整数", "shu-zi": "数字", "shen-fen-zheng-hao": "身份证号", "mi-ma": "密码", "nian-yue-ri": "年月日", "wan-zheng-shi-jian": "完整时间", "shu-ru-kuang-lei-xing": "输入框类型", "yu-lan-fei-shi-ji-xiao-guo": "预览非实际效果", "qing-xuan-ze-cha-xun-lei-xing": "请选择查询类型", "qing-shu-ru-cha-xun-top": "请输入查询top", "shu-ju": "数据", "qing-xuan-ze-guo-lv-tiao-jian": "请选择过滤条件", "fen-zu": "分组", "shu-ju-guo-lv": "数据过滤", "zhi-biao": "指标", "qing-xuan-ze-zi-duan": "请选择字段", "qing-xuan-ze-pai-xu": "请选择排序", "zi-duan": "字段", "qing-xuan-ze-biao-da-shi": "请选择表达式", "qing-xuan-ze-ji-suan-gong-shi": "请选择计算公式", "shu-ju-ji-wei-kong": "数据集为空", "kong-jian-wei-kong": "控件为空", "groups-wei-kong": "groups为空", "keys-wei-kong": "keys为空", "values-wei-kong": "values为空", "qing-shu-ru-er-mo-xing-ming-cheng": "请输入ER模型名称", "qing-shu-ru-er-mo-xing-biao-shi": "请输入ER模型标识", "qing-xuan-ze-chi-jiu-hua-fang-shi": "请选择持久化方式", "er-mo-xing-wei-she-zhi": "ER模型未设置", "er-mo-xing-she-zhi-bu-he-fa": "ER模型设置不合法", "er-mo-xing-biao-shi-bu-neng-bao-han-han-zi": "ER模型标识不能包含汉字", "qing-xuan-ze-shang-ji-cai-dan": "请选择上级菜单", "field_max_length_msg": "字段长度必须为1-{0}之间的数字", "xiao-shu-dian-wei-bi-xu-wei-030-zhi-jian-de-shu-zi": "小数点位必须为0-30之间的数字", "xiao-shu-dian-wei-bu-neng-da-yu-zi-duan-chang-du": "小数点位不能大于字段长度", "zi-duan-ming-cheng-bu-neng-zhong-fu": "字段名称不能重复", "zi-duan-ming-wei-bao-liu-zi": "字段名为保留字", "qing-shu-ru-biao-miao-shu": "请输入表描述", "qing-shu-ru-shu-ju-yuan": "请输入数据源", "zi-duan-ming-cheng-bu-neng-wei-kong": "字段名称不能为空", "zi-duan-miao-shu-bu-neng-wei-kong": "字段描述不能为空", "zi-duan-lei-xing-bu-neng-wei-kong": "字段类型不能为空", "biao-ming-cheng-bu-neng-bao-han-han-zi": "表名称不能包含汉字", "mo-ren-zi-duan-bu-neng-shan-chu": "默认字段不能删除", "pu-tong-mo-shi": "普通模式", "gao-ji-mo-shi-jin-cha-xun": "高级模式（仅查询）", "lei-xing-bu-he-fa": "类型不合法", "yong-yu-she-zhi-lie-biao-jian-de-lian-dong-guan-xi": "用于设置列表间的联动关系", "bao-biao-she-ji": "报表设计", "cha-xun-kuang": "查询框", "percent-model": "百分比显示", "hide-xAxis": "是否隐藏用x轴", "xian-shi": "显示", "showQueryCondition": "展开", "closeQueryCondition": "收起", "wei-zhi": "位置：", "qing-xuan-ze-wei-zhi": "请选择位置", "nei-rong2": "内容：", "dataease": "是否大屏链接", "kuan-du2": "宽度：", "gao-du2": "高度：", "qing-shu-ru-biao-ti-mo-ren-wei-zu-jian-ming": "请输入标题，默认为组件名", "kuan-gao": "宽高", "zi-shi-ying": "自适应", "qing-shu-ru-kuan-du": "请输入宽度", "qing-shu-ru-gao-du": "请输入高度", "tu-li": "图例", "zui-xiao-zhi2": "最小值：", "zui-da-zhi2": "最大值：", "qu-jian2": "区间：", "shu-chu-zi-duan": "输出字段：", "guan-lian-lie-biao": "关联列表：", "guan-lian-zi-duan": "关联字段：", "guan-lian-bao-biao": "关联报表：", "qing-xuan-ze-guan-lian-lie-biao": "请选择关联列表", "qing-xuan-ze-guan-lian-zi-duan": "请选择关联字段", "qing-xuan-ze-guan-lian-bao-biao": "请选择关联报表", "yu-lie-biao-lian-dong": "与列表联动", "yu-bao-biao-lian-dong": "与报表联动", "qu-zhi-qu-jian": "取值区间", "tu-biao-lian-dong": "图表联动", "qing-xuan-ze-shu-chu-zi-duan": "请选择输出字段", "shang-zuo": "上-左", "shang-zhong": "上-中", "shang-you": "上-右", "xia-zuo": "下-左", "xia-zhong": "下-中", "xia-you": "下-右", "zuo-shang": "左-上", "zuo-zhong": "左-中", "zuo-xia": "左-下", "you-shang": "右-上", "you-zhong": "右-中", "you-xia": "右-下", "ju-shang": "居上", "ju-xia": "居下", "ju-zhong": "居中", "current_app": "当前应用", "common_app": "公共应用", "she-ji-qu-yu-hua-fen-wei-24-deng-fen": "设计区域划分为24等分", "qing-kong-dang-qian-ye-qian": "清空当前页签", "cha-ru-ye-qian": "插入页签", "shan-chu-ye-qian": "删除页签", "she-zhi-gao-du-de-fang-shi-quan-ju": "设置高度的方式(全局)", "dang-qian-ye-qian": "当前页签", "ye-qian-ming-cheng": "页签名称", "ye-qian-quan-xian-bian-ma": "页签授权编码", "ye-qian-cao-zuo": "页签操作", "bai-fen-bi": "百分比", "jue-dui-zhi": "绝对值", "zu-jian-shu-xing": "组件属性", "bu-neng-bian-ji": "不能编辑", "fixed-label-width": "固定列宽", "enable-fixed-label-width": "启用固定列宽", "label-width": "标签宽度(px)", "zhu-biao-dan": "主表单", "zhu-biao-dan-y": "是", "zhu-biao-dan-n": "否", "align-type": "对其方式", "stepsTitles": "步骤条标题", "stepsHeight": "步骤条高度", "button-show-type": "按钮显示方式", "button-prompt": "按钮提示", "button-setting-level": "按钮设置级别", "button-setting-authority": "权限code", "model-flag": "模块标识字段", "interface-url": "接口URL", "button-show-line-one": "一行一个", "button-show-line-more": "一行多个", "xia-la-an-niu-xiang": "下拉按钮项", "chu-fa-xia-la-xing-wei": "触发下拉的行为", "sider-position": "侧边栏位置", "sider-collapsed": "侧边栏收起", "sider-width": "侧边栏展开时宽度", "sider-collapsed-width": "侧边栏收缩时宽度", "height-type": "高度显示方式", "tabButtons": "Tab条添加按钮", "addButtons": "添加按钮", "lrContainer-type": "栅格类型", "lrContainer-left-cols": "左侧列个数", "lrContainer-right-cols": "右侧列个数", "lrContainer-flex-type": "固定类型", "lrContainer-flex-width": "固定宽度", "tab-position": "tab位置", "fei-zhu-zi-biao": "非主子表Tab", "custom-zhu-biao-id": "自定义主表id列名", "ye-qian-xian-yin-jiao-ben": "页签隐藏规则脚本", "ye-qian-xian-yin": "页签隐藏规则设置", "ye-qian-xian-yin-title": "页签隐藏配置（未配置脚本情况下生效！）", "custom-unfold": "自定义展开配置置（优先级高于默认展开！）", "operation-fields": "运维配置项字段(写法:gwOperationConfig.xxx)", "mo-ren-guan-bi": "关闭（默认）", "zhu-biao-form-id": "主表单ID映射此组件Id", "arrangeType": "业务类型", "function": "功能", "import": "异步导入", "default-setting": "默认值设置", "default-type": "默认值类型", "qing-shu-ru-zheng-zheng-shu-pai-xu-shu-zi": "请输入正整数", "front-button": "前置按钮", "temporary": "暂存", "temporary_confirm": "确认暂存吗", "not-show-prompt": "不显示提示语", "custom_prompt": "自定义提示语"}, "widgetTypeList": {"Column": "基础柱状图", "GroupedColumn": "分组柱状图", "StackedColumn": "堆叠柱状图", "PercentStackedColumn": "百分比柱状", "Radar": "雷达图", "Line": "基础折线图", "GroupedLine": "多折线图", "Area": "基础面积图", "StackedArea": "堆叠面积图", "Bar": "基础条形图", "StackedBar": "堆叠条形图", "PercentStackedBar": "百分比条形", "GroupedBar": "分组条形图", "Pie": "基础饼图", "Donut": "环图", "Funnel": "漏斗图", "Gauge": "仪表盘", "MeterGauge": "刻度仪表盘"}, "independent_form": {"scene": "独立表单场景", "form_operation": "表单操作类型", "loading_data": "数据加载场景", "add_query": "添加查询条件", "query": "查询条件", "select_condition_field": "请选择条件字段", "context": "上下文", "fixed": "固定值", "address": "地址栏", "select_field_value": "请选择字段值", "input_field_value": "请输入字段值", "input_field_name": "请输入字段名", "input_stores_field_name": "请输入stores字段名", "scene_query": "根据查询条件匹配库表数据", "stores": "stores"}}, "localeProvider": {"label": {"randomCodeEvent": "生成随机编码", "select_title": "多语言标签选择", "other": "其他", "switchStatus": "切换编辑/选择状态", "unchanged": "数据未改动", "default_tag": "普通标签", "locale_tag": "多语言标签", "locale_tag_ok": "当前语言正常", "locale_tag_error": "当前语言不正常"}}, "eformConstant": {"validRule": {"email": "邮箱地址", "email_message": "请输入合法的邮箱地址", "phonenumber": "手机号码", "phonenumber_message": "请输入正确的手机号码", "number": "数字", "number_message": "只能输入数字", "variable": "字母或下划线", "variable_message": "只能是字母和下划线", "fields": "首字字母,最长18,仅包含字母、数字、下划线", "fields_message": "首字符为字母,最大长度18,仅包含字母、数字、下划线", "url": "网址", "url_message": "请输入合法的网址", "chinese": "汉字", "letter": "字母", "special": "特殊字符", "chinese_message": "请输中文字符", "qq": "QQ号", "qq_message": "请输入正确的QQ号码", "varirule": "以字母开头", "varirule_message": "只能为字母开头,允许字母、数字和下划线", "digits": "整数", "digits_message": "请输入整数", "digits2": "正整数", "digits2_message": "请输入正整数", "date": "日期", "date_message": "请输入日期格式", "time": "时间", "time_message": "请输入合法的时间", "zipcode": "邮政编码", "zipcode_message": "请输入邮政编码", "size_255": "长度不能超过255位字节长度(一个汉字2位字节长度)!", "size_20": "长度不能超过20位字节长度(一个汉字2位字节长度)!", "numbersOrLetters": "数字或字母", "numbersOrLetters_message": "请输入合法的数字或字母", "randomCombination": "汉字、字母、数字、特殊字符任意组合"}, "showType": {"hidden": "隐藏", "input": "单行文本", "textarea": "多行文本", "number": "数字", "select": "下拉框", "checkbox": "复选框", "radio": "单选框", "switch": "开关", "date": "日期", "time": "时间", "rate": "评分", "slider": "滑动条", "serialno": "流水号", "editor": "富文本", "icon": "图标选择", "uploadFile": "附件上传", "uploadFileOutGoing": "外发上传", "uploadImg": "图片上传", "user": "用户选择", "dept": "部门选择", "page": "弹框选择", "companyUser": "制单人", "editableTable": "子表", "excelTable": "excel表格", "button": "按钮", "showColumn": "设置隐藏列", "showColumnSetting": "设置隐藏列设置", "fixedField": "是否固定字段", "gwSetting": "运维设置", "expandColumn": "拓展栏位设置", "expandViewCode": "对应列表页面", "gwSettingSyncError": "栏位配置信息同步至关务系统失败，原因：", "isGwSetting": "是否运维设置字段", "synchronizeData": "是否同步数据", "synchronizeUrl": "请输入同步url", "buttonList": "按钮集", "alert": "警告提示", "text": "文字", "divider": "分割线", "card": "卡片布局", "grid": "栅格布局", "ybz": "运保杂", "calculateTotal": "单价数量总价", "unitPrice": "单价", "quantity": "数量", "total": "总价", "table": "表格布局", "tabsWitDragAndDrop": "Tab页签+", "tabs": "Tab页签", "verticalTabs": "竖Tab页签", "sider": "侧边栏", "collapses": "折叠面板", "dropdownButton": "下拉按钮", "lrContainer": "左右布局容器", "hoContainer": "水平的布局容器", "stepsContainer": "步骤条容器", "horizontalContainer": "水平容器", "customComponent": "自定义组件", "pleaseEntryStepsTitle": "请输入步骤名称", "declaration": "规范申报", "net_weight": "净重", "scale_factor": "比例因子", "scale_factor_1": "法一比例因子", "scale_factor_2": "法二比例因子", "scale_factor_erp": "ERP比例因子", "erp_unit": "ERP单位", "batch_text": "批量文本框", "assemble_control": "组合控件", "good_specs": "货物规格", "good_property": "货物属性", "tree_control": "树形控件", "workflow": "流程设计", "graph": "业务流"}, "subTableFieldType": {"input": "文本框", "datetime": "日期时间", "normal": "仅文字", "file": "文件", "action": "动作"}, "colType": {"varchar": "字符串", "clob": "大文本", "blob": "二进制", "number": "数字型", "date": "日期"}, "isSync": {"isSync0": "未创建", "isSync1": "已创建", "isSync2": "未同步", "isSync3": "已同步"}, "yesNo_text": {"yes": "是", "no": "否"}, "paramsRowNum": {"two": "2", "three": "3", "four": "4"}, "radioType_text": {"checkbox": "复选框", "radio": "单选框", "none": "不显示"}, "persistenceType": {"db": "数据库", "http": "远程http"}, "objStructure": {"main": "单表", "oneToOne": "一对一", "oneToMany": "一对多"}, "appType": {"pc": "PC应用", "mobile": "移动应用"}, "groupType": {"group": "分类", "app": "应用"}, "tempType": {"form": "表单", "view": "页面", "module": "模块"}, "status": {"enable": "启用", "disabled": "停用"}, "alignType": {"center": "居中", "left": "居左", "right": "居右", "default": "默认"}, "queryOp": {"eq": "等于", "eic": "等于忽略大小写", "lt": "小于", "gt": "大于", "le": "小于等于", "ge": "大于等于", "ne": "不等于", "lk": "相似", "lfk": "左相似", "rhk": "右相似", "in": "在...中", "ni": "不在...中", "bt": "在...之间", "custom": "自定义", "inl": "为空", "nnl": "非空"}, "buttonType": {"toolbar": "工具栏", "inline": "行内"}, "flowScope": {"noLimit": "不限制", "my": "仅显示我的", "all": "显示全部的", "myTask": "我的任务"}, "viewTypeAll": {"layout": "组合页面", "dragLayout": "自由组合页面", "table": "列表页面", "tree": "树形页面", "mobile": "移动页面"}, "tuoMin": {"none": "无", "email": "邮箱地址", "phonenumber": "手机号码", "idcard": "身份证", "address": "地址", "bankcard": "银行卡号", "ext": "自定义"}, "fixed": {"none": "不固定", "left": "固定居左", "right": "固定居右"}, "openTypeForSubTable": {"handleClickAdd": "默认新增", "handleAddByPage": "弹框选择", "handleAddByForm": "弹框填写", "handleExt": "自定义脚本"}, "dialogSize": {"auto": "自适应", "default": "默认", "full": "全屏弹框", "large": "大屏弹框", "middle": "中屏弹框", "small": "小屏弹框", "mini": "迷你弹框", "largeDrawer": "大屏抽屉", "middleDrawer": "中屏抽屉", "smallDrawer": "小屏抽屉", "tab": "新页签打开", "tabInCurrent": "本页签覆盖", "withinTab": "页签下显示"}, "heightType": {"full": "full", "auto": "auto", "fixed": "fixed"}, "batchText": {"placeholder": "支持批量查询 空格，回车或英文逗号或EXCEL复制直接粘贴格式"}}}, "bpm": {"common": {"flow_history": "流转历史", "operate_log": "操作日志", "approval_opinion": "审批意见", "business_handler": "办理人", "bpmStatus": "流程状态", "bpmActivity": "当前步骤", "bpmDetail": "流程详情", "bpmPage": "流程页面"}, "entity": {"group": "流程分类", "name": "模板名称", "key": "模板标识", "version": "版本号", "status": "模板状态", "processDefName": "流程模板名称", "businessTitle": "流程实例标题", "processInstanceState": "流程状态", "startTimeString": "开始时间", "endTimeString": "结束时间", "durationString": "耗时", "startUserName": "发起人", "taskTitle": "任务标题", "taskName": "节点名称", "taskType": "类型", "fromUserName": "发送人", "assigneeName": "处理人", "readerName": "读者", "sendTimeString": "发送时间", "dueDateString": "超时时间", "myInstanceClassify": "流程标识", "varName": "变量名", "varValue": "变量值", "varType": "变量类型", "varCreateTime": "创建时间", "operationTypeString": "处理方式", "message": "处理意见", "passName": "传阅人", "passType": "传阅方式", "passTime": "传阅时间", "passMessage": "传阅意见", "delegateBailee": "受托人", "delegateResource": "委托范围", "delegateResourceName": "名称"}, "verify": {"confirm_delete_template": "确定删除模板吗", "confirm_publish_template": "确定发布模板吗", "delete_tip": " 只有没有实例数据的模板才能删除", "select_show": "请选择要显示的列", "select_show_all": "请完成所有栅栏的配置", "confirm_suspend": "确定挂起吗", "confirm_activate": "确定恢复吗", "tip_batchDeal1": "请选择要批量办理的待办", "tip_batchDeal2": "不能选择待阅，请您通过查询条件过滤选择后再进行批量办理", "tip_batchDeal3": "批量办理仅支持单一流程模板同一审批节点下多条待办任务的一次性批量操作，请您通过查询条件过滤选择后再进行批量办理", "tip_batchDeal4": "符合一起批量办理条件的有{0}条，其他不符合的已自动帮您取消勾选，请确认后重新点击批量办理", "tip_batchPass1": "请选择要批量办理的待阅", "tip_batchPass2": "只能选择待阅，请您通过查询条件过滤选择后再进行批量阅读", "tip_deleteMyProcess": "只有标识为我发起，且没有提交过的流程才能删除", "tip_collectProcess": "收藏成功", "tip_unCollectProcess": "取消收藏成功", "confirm_cancelDelegate": "是否将选中任务取消委托？提示：只有类型为待办的任务才能取消委托", "tip_no_running_task": "没有运行中的任务可用", "tip_no_task": "没有任务可用", "tip_select_current_task": "请选择当前操作节点", "tip_select_target_node": "请选择目标节点", "tip_trusteeid_empty": "受托人ID为空", "tip_delegation_range_empty": "委托范围为空", "tip_switch_tasks": "切换任务", "tip_select_transition": "请选择流转目标", "tip_select_process_template": "请选择流程模板", "tip_assign_user": "指派处理人", "tip_start_sucess": "启动成功", "tip_no_transition": "没有流转目标", "tip_no_permission": "没有权限", "tip_current_task_delegated": "当前任务已委托，被委托人", "tip_assignor": "委托人", "tip_process_suspended": "流程已挂起", "tip_process_template_not_found_for_formcode": "没有找到formCode【{0}】对应的流程模板", "tip_blank_required_or_other_error": "有必填项为空或者其他错误，请检查", "tip_relation_query_fail": "关系树查询失败", "mei-you-quan-xian": "没有权限！", "zu-zhi-ji-gou-cha-xun-shi-bai": "组织机构查询失败：", "que-ren-ban-li-ma": "确认办理吗？", "qing-xuan-ren": "请选人", "qing-tian-xie-yi-jian": "请填写意见", "user_select_error": "所选用户均与当前流程已存在用户重复，请去掉重复数据再继续"}, "analyze": {"processDefAnalyze": "流程模板统计分析", "taskAnalyze": "任务办理统计分析", "taskDueAnalyze": "流程超时统计分析", "processInstanceAnalyze": "流程实例统计分析", "synAnalyzeData": "手动同步流程数据", "allProcessIntanceCount": "发起总数", "runningProcessInstanceCount": "流转中", "completedProcessInstanceCount": "已结束", "processAveragetime": "平均耗时", "taskCount": "任务总数", "startProcessTop": "流程发起实例数排名TOP10", "processInstanceCount": "流程实例数", "taskAverageTimeTop": "办理平均耗时最长TOP10", "averageTime": "办理任务平均耗时", "uncompletedProcessTop": "未完成流程实例数量排名TOP10", "averageTimeTop": "平均耗时最长的节点TOP10", "activityAveragetime": "节点平均耗时", "taskCompletedTop": "完成任务最多TOP10", "taskCompletedCount": "办理任务数量", "todoTaskTop": "待办任务最多TOP10", "todoTaskCount": "待办任务数量", "startTaskTop": "发起流程最多TOP10", "dueTimeProcessTop": "流程平均超时时长TOP10", "dueTimeActivityTop": "节点平均超时时长TOP10", "dueTimeTaskTop": "人员办理平均超时时长TOP10", "dueTime": "平均超时时长", "taskDueCount": "任务超时总数", "taskAvgDuetime": "任务平均超时时长", "taskMaxDuetime": "任务最大超时时长", "processInstanceMaxDurationTop": "耗时最长的流程实例", "processInstanceOvertimeTop": "超时最长的流程实例"}, "design": {"idea_can_be_left_blank": "可不填", "property": "属性", "press_type": "催办方式", "press_type_mail": "邮件", "press_type_sms": "短信", "press_type_blank_tips": "催办需要至少选择一种催办方式", "passRound": "传阅", "enable_add_passRound": "可同时增加传阅人", "other": "其他", "enable_sendto_backnode": "允许直接送退回节点", "specifyNode": "指定节点", "specifyNodePlaceholder": "选择节点；不选择代表不限制节点；", "after_java": "后处理类", "after_java_placeholder": "请输入事件类的SpringBean名称", "after_script": "后处理脚本", "after_script_button_name": "自定义Groovy脚本", "default_name": "默认名称", "custom_name": "自定义名称", "method_name": "方法名", "idea": "意见", "idea_submit_title": "流程审批意见", "idea_pass_round_title": "流程传阅意见", "idea_common": "常用意见", "idea_agree": "同意", "idea_disagree": "不同意", "idea_read": "已阅", "idea_add_to_common": "添加到常用意见", "idea_not_blank_warning": "当前输入框无内容", "idea_required_placeholder": "请输入意见", "icon": "图标", "required": "必填", "not_required": "可不填", "due_date_pre_tips": "任务办理期限为", "due_date_after_tips": "即最晚完成时间为", "process_select_user": "流程选人", "select_user_tab_name_user": "用户", "select_user_tab_name_dept": "แผนก", "select_user_tab_name_role": "角色", "select_user_tab_name_position": "职位", "select_user_tab_name_relation": "关系", "relation_customclass_label": "自定义实现类", "relation_customclass_placeholder": "请输入自定义实现类的SpringBean名称", "relation_customclass_title": "自定义实现类选人", "relation_variable_title": "流程变量选人", "select_user_search_username_placeholder": "请输入姓名查询", "select_user_selected": "已选", "variable": "变量", "variable_select": "选中", "variable_source": "来源", "custom_column": "自定义字段", "mapping_value": "映射值", "variable_source_eform": "电子表单", "variable_srouce_custom": "自定义", "variable_add": "新增变量", "variable_expression": "表达式", "variable_expression_placeholder": "请输入表达式", "variable_expression_available": "可用表达式", "variable_expression_userId": "登录人ID", "variable_expression_userName": "登录名名称", "variable_expression_deptId": "登录部门ID", "variable_expression_deptName": "登录部门名称", "variable_expression_deptShortName": "登录部门简称", "variable_expression_orgName": "登录企业名称", "variable_expression_getUserName": "通过用户ID转用户名称", "variable_expression_getDeptName": "通过部门ID转部门名称", "variable_expression_getDeptShortName": "通过部门ID转部门简称", "variable_expression_getOrgName": "通过企业ID转企业名称", "variable_expression_formatDateStr": "格式化时间", "time_set": "时间设置", "process_flow_steps": "流程流转步骤", "xin-zeng-shu-ju-zi-duan": "新增数据字段", "ke-bian-ji-quan-xuan-quan-qu-xiao": "可编辑(全选/全取消)", "bi-tian-quan-xuan-quan-qu-xiao": "必填(全选/全取消)", "shu-ju-zi-duan": "数据字段", "shu-ju-biao-qian": "数据标签", "auto_del_fields_msg": "自动将不可编辑但必填的字段，从配置列表中排除", "ke-bian-ji-zi-duan": "可编辑字段：", "bi-tian-zi-duan": "必填字段："}, "other": {"process_design": "流程设计", "start_permission": "启动权限", "new_version": "编辑新版本", "start_url": "启动页地址", "publish_template": "发布模板", "enable_template": "启用模板", "disable_template": "停用模板", "add_role": "添加角色", "allow_all_role": "允许所有角色", "handleSuspend": "挂起", "handleActivate": "恢复", "showVariable": "流程变量", "showVariable_placeholder": "请输入流程变量", "showVariable_select_placeholder": "请选择流程变量类型", "bpmTrack": "流程跟踪", "showBpmTask": "办理人管理", "showBpmTaskReader": "读者管理", "addAssignee": "增加办理人", "subAssignee": "减少办理人", "addPassUser": "增加传阅人", "subPassUser": "减少传阅人", "addReadUser": "增加读者", "subReadUser": "减少读者", "processJump": "跳转", "batchDeal": "批量办理", "batchPass": "批量已阅", "option_todo": "待办", "option_pass": "待阅", "option_todoed": "已办", "option_passed": "已阅", "placeholder_searchByName": "请输入流程名称", "label_frequentProcess": "常用流程", "cancelDelegate": "取消委托", "delegate_type1": "我的委托任务", "delegate_type2": "我给他人的委托", "delegate_type3": "他人给我的委托", "delegate_set_title": "委托设置", "my_delegated_tasks_title": "我的委托任务", "process_dialog": "流程对话框", "custom_button": "自定义按钮", "fa-qi": "发起", "xuan-ze-liu-cheng-mo-ban": "选择流程模板", "qing-xuan-ze-mo-ban-fen-lei": "请选择模板分类", "qing-shu-ru-jiao-se-ming-cheng-cha-xun": "请输入角色名称查询", "xia-yi-bu": "ขั้นตอนต่อไป"}, "bpmConstant": {"processDefStatus": {"design": "设计", "active": "发布", "suspended": "停用"}, "processInstanceState": {"ACTIVE": "流转中", "COMPLETED": "已结束", "SUSPENDED": "挂起"}, "myInstanceClassify": {"start": "我发起", "participate": "我参与"}}}, "portalDesign": {"components": {"portletPage": {"title": "门户小页", "layoutTemp": "模板页面", "grid": {"grid1": "模板一", "grid255": "模板二"}}}, "description": {"containerEmpty": "拖动小页到此处", "jia-zai-shi-bai": "加载失败", "bao-cun-shi-bai": "保存失败", "que-ren-shan-chu-ci-men-hu-ma": "确认删除此门户吗 ?", "she-zhi": "设置", "tuo-dong-xiao-ye": "拖动小页", "fu-zhi-xiao-ye": "复制小页", "shan-chu-xiao-ye": "删除小页"}, "actions": {"deletPort": "删除此门户"}, "config": {"widget": {"name": "标题", "title": "控件属性", "width": "宽度", "height": "高度", "titleRequired": "显示标题", "widthtooltip": "设计区域划分为12等分", "pageUrl": "小页路径"}}}, "codegen": {"label": {"data_model_selection": "数据模型选择", "form_page_config": "表单及页面配置化", "infor_config_code_gen": "信息配置及代码生成", "attribute_selection": "属性选择", "with_accessories": "是否带附件", "layout_upDown": "上下", "layout_leftRight": "左右", "templateType_singleTable": "单表", "templateType_singleTableFlow": "单表带流程", "templateType_mainSubTabTiling": "主子表页签平铺", "templateType_mainSubTabTilingFlow": "主子表页签平铺带流程", "templateType_mainTilingSubTab": "主表平铺子表页签", "templateType_mainTilingSubTabFlow": "主表平铺子表页签带流程", "templateType_mainTiling": "主表平铺", "templateType_mainTilingFlow": "主表平铺带流程", "form_line_control_num": "表单每行的控件数", "module_infor_config": "模块信息配置", "back_module_config": "后端模块配置", "javaPackage": "Java代码包路径", "genFile": "生成文件", "genFile_java": "后端代码", "genFile_web": "前端代码", "front_module_config": "前端模块配置", "front_vue_path": "前端VUE路径"}, "button": {"previous_step": "ก่อนหน้า", "next_step": "ขั้นตอนต่อไป", "genCode": "สร้างรหัส", "genSpringbootProject": "生成后端工程", "select_er_model": "请选择ER模型"}, "verify": {"templateTypeValid": "请选择模板类型"}, "entity": {"tableName": "表名", "moduleDesc": "功能描述", "moduleDesc_placeholder": "请输入功能描述", "templateType": "模板类型", "withSubTable": "关联子表", "mainManangerLayout": "页面布局", "codeCreationDate": "创建时间", "formColNewRow": "独占一行", "listIsShow": "列表", "packagePath": "基础包路径", "packagePath_placeholder": "请输入基础包路径", "moduleName": "组件名", "moduleName_placeholder": "请输入组件名", "authorName": "作者", "authorName_placeholder": "请输入作者", "authorEmail_placeholder": "请输入邮箱"}}, "warn": {"warnrule": {"entity": {"warningItemId": "预警项", "ruleCode": "规则标识", "ruleName": "规则名称", "dispatchFrequency": "调度频率", "cronExpr": "cron表达式", "createBy": "创建人", "createTime": "创建时间", "resultPageLoadScript": "预警结果页加载脚本"}, "verify": {"warningItemId": "预警项不能为空", "ruleCode": "规则标识不能为空", "ruleName": "规则名称不能为空", "dispatchFrequency": "调度频率不能为空", "cronExpr": "cron表达式不能为空"}, "other": {"button-set-warn-condition": "预警条件", "busi-design": "业务设计", "basic_info": "基本信息", "table_design": "表格设计", "query_config": "查询条件", "busi_config": "业务配置", "warn-item-rule-null": "预警项对应预警规则为空！", "parse_sql_err": "未找到表对应的实体模型，解析失败，请手动配置！", "before_script_help": "编写存储过程，入参按照占位符替换（全部替换成字符串处理，匹配为空则置null），例如call p_procedure('xx',null)。", "warn_script_help": "仅支持查询Sql模式，尽量避免使用select * 查询所有字段,按需配置需要使用的字段。", "script_placeholder_help": "新增模式下，显示企业租户对应占位符；编辑模式下，追加预警条件占位符。", "parse_sql_help": "查询的字段避免使用子查询 AS 别名、复杂函数(field) AS 别名的方式直接解析，可将复杂字段 '' as 别名，解析成功后再去替换。", "forbid_repeat_click": "请勿重复点击！", "tab_col_sync": "tab列同步", "condition_placeholder": "条件占位符", "before_placeholder": "前置占位符", "warn_placeholder": "预警占位符", "after_placeholder": "后置占位符", "warn_condition": "预警条件", "procedure_warn": "请输入存储过程", "warn_item_type": "预警项类型", "disable_condition_set": "业务触发类规则不涉及预警条件！", "col_code_define_tip": "注意：warn_status/warnStatus 字段为系统占用字段,请输入其他", "result_page_load_script_help": "支持占位符格式替换，包括企业租户上下文和预警条件。支持两种使用场景:预警条件说明 setConditionSetDesc(html),结果页表格前文字提示 setTableButtonRightContent(html,width)。"}}, "interfacetablecol": {"entity": {"colCode": "通知项字段标识", "colName": "通知项字段名称"}, "verify": {}, "other": {}}, "sqlconfig": {"entity": {"beforeDsCode": "前置数据源", "warnDsCode": "预警数据源", "afterDsCode": "后置数据源", "beforeSql": "前置脚本", "warnSql": "预警脚本", "afterSql": "后置脚本"}, "verify": {}, "other": {}}, "setconfig": {"entity": {"warnField": "预警字段", "operateType": "操作类型", "warnFieldValue": "预警字段值", "warnFieldMin": "预警字段最小值", "warnFieldMax": "预警字段最大值", "fieldPrecision": "精度"}, "verify": {"rule-customize-col-uninitialized": "预警规则定制预警字段未初始化！"}, "other": {"warnSet": "预警设置", "delete-row-confirm": "此操作影响已初始化的企业配置信息，确认删除吗？", "confirm-yes": "是", "confirm-no": "否"}}, "warnresult": {"entity": {"warningItemName": "预警项名称", "warningItemCode": "预警项标识", "logTotal": "日志记录次数", "recentLogTime": "最近日志记录时间", "recentTaskTime": "最近预警任务时间"}, "verify": {"rule-customize-col-uninitialized": "预警规则定制预警字段未初始化！"}, "other": {"warnSet": "定制预警设置", "recent-warn-result": "最近预警结果", "email-send-logs": "邮件发送日志", "site-email-send-logs": "站内信发送日志", "warn-result-export": "预警结果导出", "relieve-warn": "解除预警", "recover-warn": "恢复预警", "warn-status": "状态"}}, "warntask": {"entity": {"sendStatus": "发送状态", "updateTime": "发送时间", "recipientEmailAddress": "收件人邮箱地址", "duplicateSenderEmailAddress": "抄送人邮箱地址", "duration": "显示时间(秒)", "warnType": "通知类型", "userNoList": "通知人", "subject": "主题", "content": "内容", "errMsg": "发送失败原因"}, "verify": {}, "other": {"emailInfo": "邮件信息", "siteEmailInfo": "站内信信息", "button-email-resend": "邮件重发", "button-site-email-resend": "站内信重发", "warnResult": "预警结果"}}, "businessconfig": {"entity": {"isSubjectPlaceholder": "是否主题占位符", "isContentPlaceholder": "是否正文占位符", "isCustomWarn": "是否预警条件", "isBusiNotice": "是否业务通知配置", "busiNoticeType": "业务通知类型", "busiNoticeConfig": "业务通知配置", "isEmailCol": "是否邮件附件列", "isBusiPk": "预警解除/恢复关键字", "isAfterKeywords": "是否后置处理关键字"}, "verify": {}, "other": {}}, "emailtemplate": {"entity": {"warningItemId": "预警项", "emailTemplateCode": "邮箱模板标识", "emailTemplateName": "邮箱模板名称", "emailSubject": "邮件主题", "emailContent": "邮件正文", "resultAttachType": "预警结果附件发送", "createBy": "创建人", "createTime": "创建时间"}, "verify": {"warningItemId": "预警项不能为空", "emailTemplateCode": "邮箱模板标识不能为空", "emailTemplateName": "邮箱模板名称不能为空", "emailSubject": "邮件主题不能为空", "emailContent": "邮件正文不能为空", "resultAttachType": "预警结果附件发送不能为空", "placeholderMessage": "占位符内容不能为空"}, "other": {"triggerMode": "触发方式", "subjectBtn": "主题占位符", "contentBtn": "正文占位符", "selectPlaceholder": "选择占位符", "placeholderMessage": "占位符内容", "attachList": "附件列表", "uploadNullFormId": "没有主表记录id，请先保存再上传", "warnResultAttachUploadTip": "提示：勾选‘是’，结合预警规则业务配置勾选邮件附件列，将预警结果以excel附件形式追加到邮件附件中。", "subjectTip": "提示：标题栏内支持占位符替换，占位符字段一般为值固定的字段，默认只取第一条数据集内字段值。", "contentTip": "提示：正文内表格支持纵向和横向表格。纵向表格为上下结构，第一行放置标题，第二行放置占位符。横向表格为左右结构，只支持向右拓展，第一列放置标题列，第二列放置占位符(数据集只取第一条)。"}}, "siteemailtemplate": {"entity": {"siteEmailTemplateCode": "站内信模板标识", "siteEmailTemplateName": "站内信模板名称", "siteEmailSubject": "站内信主题", "siteEmailContent": "站内信正文", "duration": "显示时间(秒)", "warnType": "通知类型"}, "verify": {"siteEmailTemplateCode": "站内信模板标识不能为空", "siteEmailTemplateName": "站内信模板名称不能为空", "siteEmailSubject": "站内信主题不能为空", "siteEmailContent": "站内信正文不能为空", "duration": "显示时间(秒)不能为空", "warnType": "通知类型不能为空"}, "other": {}}, "noticeconfig": {"entity": {"warningItemName": "预警项名称", "warningItemCode": "预警项标识", "recipient": "收件人", "duplicateSender": "抄送人", "businessField": "业务关联字类型", "businessValue": "业务关联字"}, "verify": {"warningItemId": "预警项不能为空", "recipient": "收件人不能为空", "businessField": "业务关联字类型不能为空", "businessValue": "业务关联字不能为空"}, "other": {"noticeUserManage": "预警通知人管理", "warnNotice": "预警通知", "busiRelaSet": "业务关联字设置", "recipientName": "收件人", "duplicateSenderName": "抄送人", "delete-row-confirm": "确认删除吗？", "confirm-yes": "是", "confirm-no": "否", "triggerMode": "触发方式", "deleteItemConfirm": "是否删除选中数据?"}}, "warnConstant": {"dispatchFrequency": {"midnight": "凌晨0点", "onepointam": "凌晨1点", "twopointam": "凌晨2点", "threepointam": "凌晨3点", "fourpointam": "凌晨4点", "fivepointam": "凌晨5点", "sixpointam": "上午6点", "sevenpointam": "上午7点", "eightpointam": "上午8点", "ninepointam": "上午9点", "tenpointam": "上午10点", "elevenpointam": "中午11点", "twelvepointam": "中午12点", "onepointpm": "下午1点", "twopointpm": "下午2点", "threepointpm": "下午3点", "fourpointpm": "傍晚4点", "fivepointpm": "傍晚5点", "sixpointpm": "晚上6点", "sevenpointpm": "晚上7点", "eightpointpm": "晚上8点", "ninepointpm": "晚上9点", "tenpointpm": "晚上10点", "elevenpointpm": "晚上11点"}, "operateType": {"greater_than": "大于", "less_than": "小于", "equal": "等于", "like": "相似", "greater_than_equal": "大于等于", "less_than_equal": "小于等于", "no_equal": "不等于", "between": "到期区间", "warn_before": "到期前", "warn_after": "到期后"}, "precision": {"day": "天", "hour": "小时"}, "warningItemType": {"common": "通用", "customize": "定制"}, "taskSendStatus": {"waitSend": "待发送", "sendSuccess": "发送成功", "sendFail": "发送失败"}, "triggerMode": {"platFormWarn": "平台预警", "businessTrigger": "业务触发"}, "tradeInfo": {"currentCompCode": "企业编码", "currentCompName": "企业名称", "currentSocialCreditCode": "企业社会信用代码", "currentTenantId": "租户ID", "currentTenantCode": "租户编码", "currentTenantName": "租户名称"}, "warnStatus": {"warn": "预警", "cancel": "注销"}}}, "other": {"login_please": "", "last_page": "นี่เป็นหน้าสุดท้ายและไม่สามารถปิดได้อีกต่อไป!"}, "importComponent": {"importIndex": {"select_file": "选择文件", "file_preview": "文件预览", "import_status": "状态", "start_row": "导入起始行", "file_upload": "文件上传", "import_type": "导入方式", "end_row": "导入截止行", "download_template": "模板下载", "import_correct_data": "导入正确数据", "export_error_data": "导出错误数据", "export_warn_data": "导出警告数据", "close": "页面关闭", "last_import_time": "最近导入时间：", "field_setting_comment": "模板若不能满足需求，请使用自定义字段配置", "enable_custom_field": "启用自定义字段配置", "custom_field": "字段配置", "require": "必填", "title": "导入", "error_msg": "- 导入的文件内容为空！", "error_msg1": "上传的文件格式不正确！", "error_msg2": "请选择需要导入的文件", "error_msg3": "导入截止行必须大于导入起始行", "error_msg4": "保存失败！", "success_msg": "保存成功", "status_enum": {"00": "无", "A10": "待校验", "B10": "校验中", "B20": "校验异常", "B30": "导入模版不匹配", "C10": "校验完毕 - 全部正确", "C20": "校验完毕 - 全部错误", "C30": "校验完毕 - 部分错误", "C40": "校验完毕 - 存在警告", "D10": "待导入", "E10": "导入中", "F10": "导入异常", "G10": "导入完毕"}, "import_type_enum": {"1": "新增", "2": "修改", "3": "删除", "4": "新增或修改导入"}, "confirm": {"title": "提醒", "content": "您确定要导入正确的数据吗?", "okText": "确定", "cancelText": "取消"}}, "templateTransfer": {"error_msg": "如下列值不合法，需是字母a~z的组合且最长为3位", "error_msg1": "至少编辑一列！", "title": "字段配置", "comment_title": "输入框填写规则：", "word": "文字", "letter": "字母", "comment_title1": "单元格内容填写规则", "comment_content_letter": "单元格里填写Excel中对应列的列名，比如A", "comment_content_letter1": "单元格内容必须是英文字母a-z的组合，且最长为3位", "comment_content_word": "单元格里填写Excel中对应列的列名，比如 物料名称", "comment_content_word1": "单元格内容可以填自定义的列名称，最长为50位"}, "common": {"export": "导出", "back": "返回", "preview": "预览", "save": "保存", "close": "关闭"}}}}