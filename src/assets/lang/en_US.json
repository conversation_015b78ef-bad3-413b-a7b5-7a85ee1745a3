{"m": {"logo": {"full_name": "Floating Clouds Platform", "simply_name": "Floating Clouds"}, "common": {"button": {"gotoDownload": "view", "add": "New", "add_yellow": "New(Yellow)", "addRedirect": "New Redirect", "update": "Modify", "update_blue": "Modify(Blue)", "updateRedirect": "Modify Redirect", "delete": "Delete", "debugger": "Debugger", "delete_orange": "Delete(Orange)", "batch_delete": "<PERSON><PERSON> Delete", "import": "Import", "import-xdo": "Import(support xdo)", "handleAsyncImport": "Async Import", "handleImportAddXls": "Async Add Import", "handleImportUpdateXls": "Async Update Import", "handleImportDeleteXls": "Async Delete Import", "handleImportSaveXls": "Async Add And Update Import", "import_red": "Import(Red)", "export": "Export", "habit_export": "Export in custom order", "publish_version": "version release", "roll_back": "rollBack", "export_green": "Export(Green)", "editExport": "Edit Export", "exportDetail": "Export Detail", "allExport": "all export", "selectedExport": "selected export", "exportasync": "async export", "query": "Query", "reset": "Reset", "selectAll": "Select All", "byUser": "User Level", "byEnterprise": "Enterprise Level", "close": "Close", "ok": "OK", "cancel": "Cancel", "select": "Select", "upload": "Upload", "browse": "Browse", "selectFile": "Select File", "selectPicture": "Select Picture", "more": "More", "save": "Save", "back": "Back", "config": "Config", "setting": "Setting", "clear": "Clear", "enable": "Enable", "disable": "Disable", "refresh": "Refresh", "related": "Related", "relatedTableButton": "Related table button", "relatedViews": "Related views", "relatedScripts": "Related scripts", "expand": "Expand", "fold": "Fold", "open": "Open", "see": "See", "detail": "Detail", "detailRedirect": "Detail Redirect", "addFlow": "New Flow", "detailFlow": "Detail Flow", "openSubView": "Open SubView", "custom": "Custom", "takeData": "Take Data", "addPreValidate": "add pre validate", "otherUpload": "Other Upload", "closeList": "Close List", "riskWarn": "<PERSON>n", "otherUpload_valid": "Please configure the interface upload button action", "design": "Design", "test": "Test", "selectDbTable": "Select Table From DB", "selectApp": "Select App", "selectEntityTable": "Select Table From Entity", "setCountSql": "Set Count Sql", "parseSql": "Parse SQL", "copy": "Copy", "copy_purple": "<PERSON><PERSON>(Purple)", "upload_pic": "Upload Pictures", "editSourceFile": "Edit Source File", "editScript": "<PERSON>", "preview": "Preview", "restore": "restore", "pre_form_design": "Preview form design", "initializeInterface": "Initialize Interface", "selectTableGenInterface": "Select Table Generate Interface", "addInterface": "Add Interface", "document": "Document", "tuo-dong": "drag", "close_left": "Close left", "close_right": "Close right", "close_other": "Close other", "column_detail": "column detail", "change": "change", "openView": "Open View", "defalut_click": "defalut click", "set_parameter": "Set parameter", "zan_cun": "Staging", "move": "move", "initAppLocale": "init app locale", "existSimilarityLocale": "exist similarity locale", "initAppLocale_confirm": "confirm init locale?", "stillSaveLocale": "still save locale", "bu-chong-kong-lan-wei": "cover empty", "quan-liang-fu-gai": "cover all", "custom_report": "custom report", "expand_setting": "Expanded field settings", "save_template": "save", "del_template": "delete"}, "track": {"xiu-gai-mi-ma-cheng-gong": "Password changed successfully", "qie-huang-qi-ye-cheng-gong": "Successfully switched to enterprise"}, "title": {"add": "New", "update": "Modify", "detail": "Detail", "delete": "Confirm Delete", "operate": "Confirmation Prompt", "error": "Error", "warning": "Warning", "inParam": "In parameter Info", "debugInfo": "Debug Info", "fixed_left": "fixed left", "fixed_right": "field right"}, "label": {"number": "S/N", "operate": "Operation", "columnsDetail": "columns detail", "match": "match", "status": "Status", "sort": "Sort", "fileName": "File Name", "arrangeId": "Business Id", "arrangeName": "Business Name", "arrangeDescription": "Business Description", "whether_use": "Whether use", "enableCite": "Allow other app use", "citedIds": "Exec other app business after current business", "notification": "notification update column", "notificationTitle": "notification title", "notificationLength": "notification content length", "notificationLimit": "notification limit length:", "addBusinessService": "add business service", "businessArrange": "business arrange", "asyncImport": "Async Import", "customReport": "custom report", "modifyExport": "Modify Export", "initializeScript": "initialize <PERSON><PERSON><PERSON>", "commonScript": "common Script", "appAql": "SQL Setting", "arrangeComponent": "arrange component", "switchConfirm": "Page not saved yet,are you sure switch arrange component?", "copy": "copy", "page_record_num": " total {0} records", "yes": "Yes", "no": "No", "parameter_key": "Parameter key", "parameter_value": "Parameter value", "prompt": "Prompt", "all": "all", "part": "Part", "begin_time": "Begin Time", "end_time": "End Time", "begin_date": "Begin Date", "end_date": "End Date", "upload_time": "Upload Time", "uploader": "Uploader", "to_date": "To", "year": "year", "month": "month", "days": "days", "day": "day", "hours": "hours", "hours_short": "hours", "minutes": "minutes", "minutes_short": "minutes", "seconds": "seconds", "millisecond": "millisecond", "custom_SQL": "Custom SQL", "count_SQL": "Count <PERSON>", "option_select": "Please Select", "label_color_placeholder": "The value is the same as the CSS Color property", "option_select_dialog": "Please Select Dialog Size", "Monday": "Monday", "Tuesday": "Tuesday", "Wednesday": "Wednesday", "Thursday": "Thursday", "Friday": "Friday", "Saturday": "Saturday", "Sunday": "Sunday", "code": "Code", "Chinese": "Chinese", "English": "English", "Thai": "Thai", "app": "App", "team": "Team", "Maintain_data": "Maintain data", "interfaceName": "Interface Name", "page_total": "{0}-{1} Total {2} record", "statusUpdate": "update status", "syncData": "sync", "statistics": "statistics", "sysDefault": "default", "sysTenant": "tenant", "sysComp": "company"}, "tip": {"delete_confirm": "Are you sure to delete the record?", "delete_confirm2": "{0} item is selected. Do you want to delete the selected data?", "save_confirm": "Are you sure save?", "locale_cover": "Whether to cover multi-language data?", "generate_confirm": "Are you sure generate js?", "close_confirm": "Are you sure close?", "enable_confirm": "Are you sure enable it?", "disable_confirm": "Are you sure disable it?", "select_one_warn": "Please select a record", "view_redirect_id_not_found": "Please set viewRedirectId to this button", "select_one_only": "you can only select one record", "pic_warn": "Please upload pictures", "length_warn": "The length cannot exceed {0} characters", "execute_success": "Execute successfully", "operate_success": "Operation is successful", "operate_error": "The operation failure", "check_success": "The verification has been completed. After downloading the data, you can enjoy the free foreign trade dolphin service!", "check_error": "The registration information is inconsistent with the card information. Please reinsert the card!", "return_data_null": "The returned data is null", "begin_date_tip": "Please select a start date", "end_date_tip": "Please select a end date", "file_upload_sucess": "File uploaded successfully", "file_upload_error": "Failed to upload files", "file_uploading": "File uploading", "code_format": "The code cannot contain Chinese characters", "sql_warn": "Please enter the SQL", "sql_ok": "SQL parsing succeeded", "verify_required": "Required", "code_unique": "Code cannot be repeated", "code_required": "Code is required", "positive_digits_tip": "Please enter a positive integer", "enter_script": "Please enter script", "no_data_modification": "No data modification", "cannotNull": " cannot be empty", "access_denied": "access denied", "afternoon1": "Good afternoon", "afternoon2": "Good afternoon", "dict_code_null": "Dictionary Code cannot be empty!", "disabled_current_action": "The current environment prohibits this operation", "disabled_ti_yan": "Disabled in the experience environment", "multiple_login": "Your account has been logged in elsewhere, if it is not your operation, please change your password in time!", "evening": "Good evening", "morning1": "Good morning", "morning2": "Good morning", "network_timeout": "network timeout", "resource_not_found": "Sorry, resource not found", "system_prompt": "system hint", "unauthorized": "Login timeout, please log in again", "deleteFileFailed": "Delete failed", "checkFileSize": "{0} Too large, max {1}M", "password_reminder": "Password reminder", "to_change_password": "Your password is about to expire, click OK to change the password", "arrangeIdIsEmpty": "Arrange Id Is Empty!"}, "popup": {"jiao-ben-hou": "Open popup：", "popup-setting": "Popup setting", "popup-type": "Popup type", "popup-code": "Popup code", "popup-status": "Popup status", "popup-size": "Popup size", "large": "Large", "middle": "Middle", "small": "Small", "mini": "Mini", "auto": "Auto"}, "import": {"modal-title": "Custom import config", "enable": "Enable or not", "system-code": "System code", "input-system-code": "Please input system code（systemCode）", "task-code": "Task code", "input-task-code": "Please input task code（taskCode）", "param-setting": "Param setting", "title": "Page title", "refreshParent": "After close whether to refresh the list page", "input-title": "Please input page title", "extendKey": "Extend key", "input-extendKey": "Please input extend key（extendKey）", "fieldsMapConfig": "Enable custom field", "baseUri": "Base Uri", "input-baseUri": "Please input Base Uri（baseUri）", "importKey": "Import key", "input-importKey": "Please input import key（importKey）", "task-description": "Task description", "import-start-line": "Import start line", "multi-table-import": "Enable multi-table import", "extended-task": "multi-table import task", "main-child-relation": "main-child relation", "child_field": "child field", "please_child_field": "please select child field", "main_foreign": "main foreign", "please_main_foreign": "please select main foreign"}, "export": {"modal-title": "Custom export config", "enable": "Enable or not", "url": "Url path", "begins-with": "begins with/", "filename": "File name", "input-filename": "Please input file name(Can be blank)", "http-request-type": "Http request type", "carry-query-condition": "Carry query condition", "query-condition-format": "Query condition format", "hump": "<PERSON><PERSON>", "underline": "Underline", "scene-selection": "Scene selection", "api": "API interface", "filter": "Data filter", "filter-setting": "Filter setting", "col-name": "Col name", "select-col-name": "Please input col name", "export-default": "export default", "input-export-default": "Please input export default"}, "other": {"bao-mu": "dusk", "fu-xiao-lan": "dawn blue", "huo-shan": "volcano", "ji-guang-lv": "Aurora Green", "ji-ke-lan": "Geek Blue", "jiang-zi": "sauce purple", "ming-qing": "Mingqing", "ri-mu": "sunset", "not-permission-page": "not permission visite page", "not-open": "This function is not open yet"}, "table": {"noData": "no data"}}, "components": {"title": {"ychSelectUser": "Select User By Department", "ychSelectDepart": "Select Department", "columnSet": "<PERSON>umn <PERSON>", "showSearchSettings": "Display setting hidden column or not", "searchSettingsLevel": "Set the query column level", "showColumnSettings": "Show column setting", "searchSettingId": "Set unique value of hidden column", "businessArrangeDebugger": "Business arrange debugger"}, "label": {"columnShow": "Column Show", "bu-men-xuan-ze-kong-jian": "Department selection controls", "dian-ji-cha-kan-shi-yong-bang-zhu": "Click to view the help", "qing-dian-ji-xuan-ze-bu-men": "Please click to select a department", "qing-dian-ji-xuan-ze-yong-hu": "Please click to select user", "qing-yi-ci-dian-ji-thispointextlistjoin": "Please click【{0}】in turn", "shi-yong-bang-zhu": "Using help", "tree_valid_error": "The value of the component JTreeSelect-condition is wrong, a json string is required!", "wei-zhi-de-cao-zuo": "unknown operation", "xiang-you-hua-dong-wan-cheng-yan-zheng": "Swipe right to complete verification", "yi-dao-zui-hou": "has come to the end", "yong-hu-xuan-ze-kong-jian": "User selection control", "zan-wu-bang-zhu-xin-xi": "No help information yet", "dataSource": "Data source column", "isSelected": "Selected columns", "searchSettingId": "Please enter a unique value"}, "verify": {"searchSettingId": "The length cannot exceed 8 bits"}, "cron": {"title": "cron expression", "seconds1": "Every second", "seconds2": "Second perform", "seconds3": "Second start", "seconds4": "number of seconds(Can choose more)", "minutes1": "Every minutes", "minutes2": "minutes perform", "minutes3": "minutes start", "minutes4": "number of minutes(Can choose more)", "hours1": "Every hours", "hours2": "hours perform", "hours3": "hours start", "hours4": "number of hours(Can choose more)", "days1": "Every day", "days2": "weeks perform", "days3": "start", "days4": "start perform", "days5": "start start", "days6": "day of week(Can choose more)", "days7": "days(Can choose more)", "days8": "last day of month", "days9": "last business day of month", "days10": "last one of month", "days11": "before end of month", "days12": "Latest working day（Monday to Friday）to current month", "days13": "this month", "days14": "time", "month1": "Every month", "month2": "month perform", "month3": "month start", "month4": "months(Can choose more)", "month5": "Between each month", "year1": "Every year", "year2": "year perform", "year3": "year start", "year4": "year(Can choose more)", "year5": "Between each year", "common1": "every", "common2": "Cycle from", "common3": "to", "common4": "from"}, "icon": {"title": "select icon", "warnTitle": "Please select the icon", "iconType1": "Direction", "iconType2": "Instructions", "iconType3": "Edit", "iconType4": "Data", "iconType5": "Common", "iconType6": "Brand and identity", "iconType7": "IconFont(mobile)"}}, "system": {"login": {"entity": {"username": "Account", "password": "Password", "mobile": "Phone Number", "captcha": "Verification Code ", "randomCode": "Random Code", "verify": "Verify", "complete": "Complete", "captcha_short": "Verify Code", "confirmpassword": "Confirm Pass", "username_phone": "Account / Mobile", "compCode": "Please fill in the 10 digit code of the customs of the business unit", "compName": "Please entry compName", "socialCreditCode": "Please entry socialCreditCode", "selectUserName": "Please entry UserName"}, "verify": {"specific_template_name": "Please specific template name", "mobile": "Please enter mobile phone number", "mobile_format": "phone number is incorrect", "username": "Please enter account", "name": "Please enter username", "password": "Please input a password", "password_inconsistency": "The two passwords are inconsistent", "confirmpassword": "Please enter the confirmation password", "mobile_placeholder": "11 digit mobile number", "placeholder_register": "Used to retrieve the password, please fill in correctly!", "captcha": "Please enter verification code", "randomCode": "Please enter Random code", "account_already_exists": "Account already exists", "username_already_exists": "Username already exists!", "company_already_exists": "Company already exists!", "email_already_exists": "Mailbox already exists", "password_strength": "Insufficient password strength", "register_fail": "Register fail", "register_success": "Your account: {0} registered successfully", "register_login": "Register success,Please login!", "username_phone": "Please enter your account / mobile number", "username_notexist": "Account does not exist", "emial_notexist": "Email does not exist", "verify_fail": "Validation error", "change_password_fail": "Failed to change password", "change_password_success": "Password changed successfully", "compCode": "Please fill in the 10 digit code of the customs of the business unit", "compCodeLength": "The length of the customs of the business unit is 10", "noRegister": "The customs broker cannot be registered", "compName": "Please entry compName", "compNameLength": "Up to 50 enterprise names", "socialCreditCode": "Please entry socialCreditCode", "socialCreditCodeLength": "The length of the unified social credit code is 18 digits", "userNameLength": "Up to 20 user name is 20", "realnameLength": "Up to 10 real name is 10", "mobileLength": "The length of mobile is 11 digits", "emailLength": "Up to 10 email is 50"}, "other": {"login_type1": "Account Password", "login_type2": "Phone Verification", "captchaVerify": "<PERSON><PERSON><PERSON>", "getCaptcha": "Get Code", "getRandomCode": "Get Random Code", "alteration": "forget password", "register": "register account", "register_short": "Register", "loginBtn": "<PERSON><PERSON>", "deptSelect": "Login Dept Select", "captchaSend": "Verification code being sent", "welcomeback": "Welcome back", "welcome": "Welcome", "loginFailed": "<PERSON><PERSON> failed", "loginError": "An error occurred. Please try again later", "noDept": "You do not belong to a department, please confirm the account", "verifybox_title": "Please complete security verification", "verify_success": "verify successfully", "verify_failed": "verify failed", "dang-qian-xi-tong-wu-deng-lu-yong-hu": "There are no logged-in users in the current system!", "strength": "Strength", "login_use_existing_account": "Login with an existing account", "back_to_home": "Back to home page", "button_submit": "Submit", "bck_to_login_tip": "Will return to the login page in {0} seconds", "admin_account": "Administrator account", "general_account": "General account", "sso_null": "SSO configuration is incomplete"}}, "portal": {"entity": {"downloadCenter": "download center", "exportTaskName": "export task name", "exportTaskCode": "export task code", "exportFileName": "export file name", "exportInit": "init", "insertTime": "export time", "status": "status", "exportIng": "exporting", "exportFailed": "failed", "exportSuccess": "successed", "insertUser": "insert user", "action": "action", "theme": "Theme", "console": "<PERSON><PERSON><PERSON>", "fullScreen": "FullScreen", "nuFullScreen": "Exit FullScreen", "department": "Department", "notDepartment": "Not Department", "addSecondLang": "Add Second Language", "changePassword": "Change Password", "changeDepartment": "Switch Department", "changeCorp": "Switch Corp", "logout": "Logout", "switchLanguage": "Switch Language", "settings": "Settings", "home": "Home", "homepage": "HomePage", "workbench": "Workbench", "largescreen": "LargeScreen", "searchMenu": "Search Menu", "message": "Notice", "layoutset": "Layout Settings", "leftNavigation": "Left Navigation", "topNavigation": "Top Navigation", "skinset": "Skin Settings", "oldPassword": "Old Password", "newPassword": "New Password", "newPassword2": "Repeat New Password", "wechatScan": "Wechat scan binding", "messageCenter": "Message Center", "messageView": "Message View", "chooseHb": "Choose hand book", "navigation": "Navigation", "closeNavigation": "Close Navigation", "expandMenus": "Expand Menus", "openNewTab": "Open in new browser", "collapsedMenus": "Collapsed Menus", "homeTab": "Default <PERSON>s", "quickStart": "Quick Start", "addMore": "Add More", "myApp": "My Application", "selectedMenu": "Selected Menus", "optionalMenu": "Optional Menus", "quickModal": "Customize shortcuts", "downloadTip": "Operating Tip", "return": "Got it", "search": "Search", "menuNav": "Menu Navigation", "policy": "Policies and regulations", "addressBook": "Address Book", "appMarket": "App Market", "onlineService": "Online Service", "help": "Help"}, "verify": {"logout": "Are you sure logout?", "noSwitch": "There is no company to switch to", "chooseCompany": "Please choose the company", "chooseTenant": "Please select tenant", "chooseDivision": "Please choose the division"}, "other": {"deptselect_tip": "You belong to multiple departments. Please select the current department", "deptselect": "Select login department", "deptselect_title": "Switch Company", "current_dept": "Current Company", "noDept_tip": "You have not set department", "divisionselect": "Select division", "dept": "Company", "division": "Division"}, "tips": {"quickSettings": "Go and set up your shortcut menu~", "noFuncCurrApp": "No function menu under current application~", "addQuickMenus": "Add shortcuts from the optional menu below~", "noOptionalMenus": "No menu options~", "selectedMenu": "27 at most, drag and drop to adjust sorting"}}, "portlet": {"mytask": {"myTodoTask": "Todo Task", "myReadTask": "Read Task", "detailFromTask": "Detail"}, "process": {"frequentlyProcess": "Frequently Process", "startProcess": "Start Process"}, "announcement": {"notification": "Notification"}}, "position": {"entity": {"positionName": "Position Name", "positionCode": "Position Code", "positionOrder": "Position Order", "description": "Description", "username": "User Name", "realname": "Real Name", "status_dictText": "Status"}, "verify": {"positionName": "Please enter a position name", "positionCode": "Please enter a position code", "positionName_repeat": "The position name already exists", "positionCode_repeat": "The position code already exists"}, "other": {"href_user": "User", "selected_position": "Selected position"}}, "role": {"entity": {"roleName": "Role Name", "roleType": "Role Type", "roleCode": "Role Code", "roleOrder": "Role Order", "description": "Description"}, "type": {"agent": "agent"}, "verify": {"roleName": "Please enter a role name", "roleCode": "Please enter a role code", "roleCode_Chinese": "Role code unable to input Chinese characters"}, "other": {"add_role": "New Role", "copy_role": "Copy Role", "back_role": "Back role", "front_role": "Front role", "setting_menu": "Setting menu", "setting_user": "Setting user"}}, "tenant": {"entity": {"tenantCode": "Tenant Code", "tenantName": "Tenant Name", "tenant": "Tenant", "operationFlag": "Operation Auth", "loginUrl": "Login URL", "maxUsers": "Max Users", "enableDivision": "Enable division", "enableProxy": "Is Proxy", "enableCustomBroker": "Is CustomBroker", "enableIgnoreCase": "Enable Ignore Case", "mainComp": "Main Comp", "mainCompCode": "Main Comp Code", "mainCompName": "Main Comp Name", "mainUser": "Main User", "mainUserCode": "Main User Code", "mainUserName": "Main User Name", "proxyCompCode": "Proxy Comp Code", "proxyCompName": "Proxy Comp Name", "proxyUserCode": "Proxy User Code", "proxyUserName": "Proxy User Name"}, "verify": {"tenantCode": "Please enter Tenant Code", "tenantName": "Please enter Tenant Name", "tenantCode_repeat": "The tenant code already exists", "maxUsers": "Please enter max Users", "mainComp": "Please choose Main Comp", "mainUser": "Please choose Main User", "proxyCompCode": "Please enter Proxy Comp Code", "proxyCompCodeNotExist": "Proxy Comp Not Exist", "proxyUserCode": "Please enter Proxy User Account", "proxyUserCodeNotExist": "Proxy User Not Exist"}, "other": {"have": "have", "none": "none", "change": "Please choose tenant", "nowTenant": "Now Tenant", "logoutTenant": "Logout tenant", "logoutSuccess": "Logout tenant success"}, "button": {"switch_tenant": "switch tenant", "setting_menu": "setting menu"}}, "corpTag": {"entity": {"tagCode": "Tag code", "tagName": "Tag name", "bind_org": "Bind org", "bind_user": "Bind user", "remark": "remark", "createTime": "Create time", "tagSetting": "Tag Setting", "related_org": "Related org", "related_role": "Related role", "related_user": "Related user"}, "verify": {"tagCode": "Please enter tag code", "tagName": "Please enter tag Name", "tagCodeExisted": "Tag code existed", "notBoundTenant": "Not switch tenant", "notOperationPermit": "No operation and maintenance authority", "notTenant": "Tenant not found"}}, "job": {"entity": {"jobName": "Job Name", "jobCode": "Job Code", "jobOrder": "Job Order", "description": "Description"}, "verify": {"jobName": "Please enter a job name", "jobCode": "Please enter a job code", "jobName_repeat": "The job name already exists", "jobCode_repeat": "The job code already exists"}, "other": {"export_filename": "Job"}}, "org": {"entity": {"orgName": "Org Name", "orgCode": "Org Code", "orgType": "Org Type", "orgOrder": "Org Order", "parentId": "<PERSON><PERSON>", "orgNameAbbr": "Short Name", "depName": "Department Name", "companyName": "Company Name", "compCode": "Company Code", "socialCreditCode": "Social Credit Code", "ip_white": "<PERSON> white", "ip_address": "IP address", "enable_password_expiration": "Password expiration", "password_expired_day": "Password expired day", "password_expired_alert_day": "Password expired alert day", "lock_user": "Lock user", "lock_black_password": "Lock black password", "multiple_login": "Multiple login", "enable": "Enable", "disable": "Disable", "expired": "Expired", "not_expired": "Not Expired", "default": "<PERSON><PERSON><PERSON>", "prohibit": "Prohibit", "allow": "Allow", "largeScreenAddress": "Large screen address", "largeScreenEnAddress": "Large screen en address", "largeScreenThAddress": "Large screen th address", "largeScreenLocaleSetting": "Large screen address locale settings", "homePageAddress": "Home page address", "chargeNotice": "Foreign trade dolphin registered user use control", "factoryCode": "factory code"}, "verify": {"orgName": "Please enter an organization name", "orgType": "Please select an organization type", "orgOrder": "Please enter an organization order", "compCodeLength": "Company code length must be 10 characters", "socialCreditCodeLength": "Social credit code length must be 18 characters", "compCodeExist": "Company code is exist"}, "other": {"add_sub": "New Sub", "handle_update_path": "Are you sure about collating the data？", "orgType_org": "organization", "orgType_dept": "department", "company_name": "company", "href_division": "ConfigDivision"}}, "user": {"entity": {"username": "Login Name", "realname": "User Name", "password": "Password", "passwordPlaceholder": "At least three combinations of uppercase and lowercase letters, numbers, and special characters!", "userNum": "User <PERSON>um", "userSecret": "User Secret", "userOrder": "User Order", "isRealUser": "Is Real User", "majarDepName": "<PERSON><PERSON>", "mainCompanyName": "Main CompanyName", "userQq": "QQ", "avatar": "Avatar", "birthday": "Birthday", "sex": "Sex", "email": "Email", "phone": "Phone", "role": "role", "status": "Status", "workNo": "WorkNo", "telephone": "Phone", "confirmpassword": "Confirm password", "selectedRole": "Select Role", "selectedJob": "Select Job", "compCode": "compCode", "compName": "compName", "socialCreditCode": "socialCreditCode", "user_source": "User source", "enable_vcode": "Enable two-factor login auth", "force_change_pass": "Whether to force password change", "show_water_mark": "Whether to display watermark", "domain_key": "Domain key", "domain_name": "Domain name", "user_type": "User type", "normal": "Normal", "normalType": "Normal Type", "admin": "Admin", "proxy": "Proxy", "proxy_edit": "Proxy Edit", "fill_proxy_edit": "Please Fill Proxy Edit", "password_expiration": "Password expiration", "multiple_login": "Multiple login", "enable": "Enable", "disable": "Disable", "expired": "Expired", "not_expired": "Not Expired", "default": "<PERSON><PERSON><PERSON>", "prohibit": "Prohibit", "allow": "Allow", "enable_phone_login": "Enable phone login", "enable_email_notification": "Enable email notification", "user_status": "User status", "remark": "remark", "show": "Show", "no-show": "Blank", "operable_org": "operable business", "forward_code": "forward code", "broker_code": "broker code", "comp_code": "comp code", "proxy_type": "Proxy Type", "proxy_code": "Proxy Code", "customs_code": "Customs Code", "customs_code_length": "The customs code can only be ten digits", "require_org": "Require org", "require_type": "Agent type must be selected", "require_code": "Agent code must be filled in", "proxy_repeat": "The same agent type exists for the same operational enterprise", "least_one": "forward、broker、comp fill in at least one"}, "verify": {"username": "Please enter your user account", "password": "The password consists of 8 digits, upper and lower case letters, and special characters", "confirmpassword": "Please re-enter your login password", "diffpassword": "The two password are different", "phoneexist": "The mobile number already exists", "phoneformat": "Please enter the mobile phone number in the correct format", "emailexist": "The email already exists", "emailformat": "Please enter an Email in the correct format", "userexist": "The user already exists", "workNoexist": "The work number already exists", "deptexist": "The department has been added", "realname": "Please enter a user name", "workNo": "Please enter your work number", "telephone": "Please enter the correct landline number", "confirmfrozen": "Are you sure to freeze the account", "confirmunfrozen": "Are you sure to unfreeze the account", "restore_user": "Are you sure to restore the account", "physical_delete": "Are you sure physical delete the account", "isadmin_warn": "This operation is not allowed for the administrator account.", "confirmdept": "Please select a department", "majarDepName": "Please select a majar department", "phone": "Please enter your user phone", "email": "Please enter your user email", "domain_exist": "Domain key + name already exists"}, "other": {"select_user_title": "Select User", "basic_info": "Basic Info", "ext_info": "Part-time Company", "frozen": "Frozen", "unfrozen": "Unf<PERSON>zen", "recycle": "Recycle bin", "restore_user": "Restore User", "physical_delete": "Physical Delete", "changepwd": "Change Password", "dept_label": "Department", "position_label": "Position", "dept_position_label": "Department and Position selection", "add_user": "Add User", "sex_male": "male", "sex_female": "female", "export_filename": "User Info", "division_info": "Division Info", "admin_add": "Admin add", "user_register": "User Register", "third_party": "Third party", "max_user_exceed": "Maximum number of users exceeded"}}, "permission": {"entity": {"name": "<PERSON>u Name", "menuType": "Menu Type", "sortNo": "Sort", "groupId": "App Menu", "component": "Component", "url": "Menu url", "parentId": "<PERSON><PERSON>", "componentProps": "Parameters", "icon": "Icon", "color": "Icon Color", "menuHidden": "Hidden Menu", "defaultShortcutMenu": "Default Shortcut Menu", "isKeepalive": "IsKeepalive", "isDeclare": "Use Cloud", "internalOrExternal": "External URL", "perms": "Permission Identify", "permsType": "Permission Policy", "status": "Status", "back_show": "Back Show", "zhCn": "Chinese", "enUs": "English", "menuCode": "Menu Code", "language1": "Extended language 1", "language2": "Extended language 2", "language3": "Extended language 3", "reminderMenu": "Reminder menu", "allowVisitorAccess": "Allow visitor Access", "tenantMenu": "Tenant menu", "tenantRange": "Tenant Range", "functionGroup": "Function Group"}, "verify": {"name": "Please enter a menu title", "menuCode": "Please enter a menu code", "component": "Please enter front-end components", "url": "Please enter the menu path", "permsType": "Please enter an authorization policy", "menuType": "Please check your type and information", "perms": "Please enter the authorization id", "number": "Please enter a positive integer", "duplicate_perms": "The authorization id already exists", "existsMenuCode": "Menu code already exists", "menuCodeLength": "The length cannot exceed 100 bits", "functionGroup": "Please enter the function group"}, "other": {"menu_permission_label": "Menu Permission", "group_permission_label": "Group Permission", "unfold_fold": "Unfold/Fold", "all_none": "All/None", "father_son_link": "Father son link", "select_role": "Please select a role", "back_menu": "Back menu", "front_menu": "Front menu", "add_sub": "Add Sub", "data_rule": "Data Rules", "premission_aq": "Menu FAQ", "add_sub_label": "Add Sub Menu", "permission_type0": "<PERSON>u Name", "permission_type1": "Level 1 menu", "permission_type2": "Sub menu", "permission_type3": "Page resources", "virtualMenu": "Virtual Menu", "select_component": "Select", "form_component": "Form", "flow_component": "Process", "report_component": "Report", "view_component": "Page", "route_component": "Aggregation menu", "iframe_component": "Iframe tab", "dataEase_component": "DataEase develop tab", "perms_placeholder": "Separate multiple entries with commas (,), for example,user:list,user:create", "init_root_menu": "Initialize the root menu", "menu_workbench": "Home", "menu_process": "Process", "menu_application": "Application", "permsType_1": "Visible/Accessible(Visible/Accessible after authorization)", "permsType_2": "Editable(Disabled without authorization)", "status_1": "<PERSON><PERSON>", "status_0": "Invalid"}}, "dict": {"entity": {"dictName": "Dict Name", "dictCode": "Dict Code", "category": "Category", "description": "Description", "itemText": "Item Name", "itemValue": "Item Value", "sortOrder": "Sort", "visibleCheck": "Is Visible", "appName": "appName", "groupCode": "group code", "extraInfo": "extra info"}, "verify": {"dictName": "Please enter a dictionary name", "dictCode": "Please enter a dictionary code", "itemText": "Please enter a item name", "itemValue": "Please enter a item value", "moveApp": "Are you sure you want to move the current data?"}, "other": {"dictName_dictCode": "Dict Name/Code", "name_code_placeholder": "Please enter a dictionary name or code", "recycle": "Recycle Bin", "refleshCache": "<PERSON><PERSON><PERSON>", "option_category0": "platform", "option_category1": "common", "option_category2": "application", "option_category3": "common-group", "option_category4": "common-platform", "export_filename": "Dict Info", "split_success": "Split successfully!"}}, "portalset": {"entity": {"portletName": "Portal Name", "portletCode": "Portal Code", "isDefault": "<PERSON>"}, "verify": {"portletCode": "Please enter the portal code", "portletName": "Please enter the portal name"}, "other": {"authorize": "Authorize"}}, "sqlconfig": {"entity": {"sqlName": "Name", "sqlCode": "Code", "sqlQuery": "SQL", "isCache": "Whether to cache"}, "verify": {"sqlName": "Please enter name", "sqlCode": "Please enter code", "sqlKey": "Please enter key", "sqlText": "Please enter text", "orderBy": "Please enter sort", "sqlCode_repeat": "Code already exists", "sqlName_repeat": "Name already exists"}, "other": {"export_filename": "SQL Config"}}, "httpconfig": {"entity": {"httpName": "Service Name", "httpCode": "Service Code", "httpUrl": "Service URL", "returnScript": "return script", "displayFormat": "display format", "httpMethod": "Request Type", "dynamic": "Dynamic Request", "inputStart": "Start Input", "autocompleteKey": "Request Key", "delayTime": "Delay Time(millisecond)", "inParameterType": "Parameter Type", "httpHeader": "Request Header", "httpBody": "Request Body", "httpAuthConfig": "<PERSON>th Config", "inParameter": "Parameter", "keyvalue": "Key-Value", "key": "key", "value": "value", "takeData": "take data", "getTakeConfig": "get take data config", "dataStructure": "data structure", "dataPreview": "data preview", "analysisCol": "analysis column", "colType": "column type", "analysisApi": "analysis api data"}, "verify": {"httpName": "Please enter service name", "httpCode": "Please enter service code", "httpUrl": "Please enter service URL", "httpMethod": "Please select Http Method", "header": "Key cannot be empty", "inParameter": "Key cannot be empty", "value": "Value cannot be empty", "header_repeat": "Request header key duplicate", "return_repeat": "Return repeat key duplicate", "inParameter_repeat": "Parameter key duplicate"}, "other": {"exprTitle": "The available expressions for value value include: {0}, {1}, {2}, {3}, {4}, {5}, {6}, {7}, {8}, {9}", "qing-she-zhi-urlquerybyid-shu-xing": "Please set the url.queryById property!", "export_filename": "HTTP Service Registry"}}, "treesetting": {"entity": {"typeName": "Type name", "typeCode": "Type code"}, "verify": {"requireTypeName": "Type name cannot be empty", "requireTypeCode": "Type code cannot be empty", "repeatTypeCode": "Type code cannot be repeated", "nodeNotNull": "There must be a node in the tree structure"}}, "sysconfig": {"entity": {"configCode": "Config Code", "configName": "Config Name", "configValue": "Config Value"}, "verify": {}, "other": {}}, "sysfillrule": {"entity": {"ruleName": "Rule Name", "ruleCode": "Rule Code", "ruleClass": "Rule Class", "ruleParams": "Rule Parameter", "ruleDescription": "Description", "digits": "Digits", "pattern": "Regular Expression", "message": "Tooltip Text", "priority": "Priority"}, "verify": {"ruleParams": "Enter a JSON string：", "ruleParams_format": "You can only pass JSON objects, not JSON arrays："}, "other": {"generated_code": "generated code：", "sysFillRule_tab": "Code generation rule", "sysCheckRule_tab": "Validation rules", "localRules_tab": "Local Rules", "globalRules_tab": "Global Rules", "localRules_tip": "Local rules are calibrated in order of the number of digits you enter", "globalRules_tip": "A global rule validates all characters entered by the user. Global rules have a higher priority than local rules", "pattern_tip": "Enter a correct regular expression", "priorityOptions1": "Priority to run", "priorityOptions0": "Last to run", "test_label": "Input verification text", "test_title": "Check rule test"}}, "datasource": {"entity": {"datasourceKey": "Datasource Key", "datasourceName": "Datasource Name", "datasourceType": "Datasource Type", "dbType": "Database Type", "attributesJson": "Attributes", "url": "URL", "username": "Username", "password": "Password", "driverClassName": "DriverClassName"}, "verify": {}, "other": {}}, "category": {"entity": {"pid": "<PERSON><PERSON>", "name": "Category Name", "code": "Category Code"}, "verify": {}, "other": {"add_sub": "Add Child"}}, "syslog": {"entity": {"userid": "Login name", "username": "User Name", "logContent": "Log contents", "pagePath": "Page path", "ip": "IP", "os": "OS", "browser": "Browser", "createTime": "Create Time", "operateType": "Operate Type", "contentDetail": "Detail", "addMenu": "<PERSON><PERSON>", "groupAddMenu": "Add Group", "deleteMenu": "DeleteMenu", "groupDeleteMenu": "Delete Group", "companyName": "Company Name", "companyCode": "Company Code", "operatorTime": "Operator Time", "tenantName": "Tenant Name", "tenantCode": "Tenant Code"}, "verify": {}, "other": {"login_log": "<PERSON><PERSON>g", "operate_log": "Operate Log", "request_method": "Request Method", "request_parameters": "Request Parameters", "operateType1": "login", "operateType2": "logout", "operateType3": "query", "operateType4": "add", "operateType5": "update", "operateType6": "delete", "operateType7": "import", "operateType8": "export", "operateType9": "paging query", "operateType101": "login success", "operateType102": "login fail", "operateType103": "org manage", "operateType104": "user manage", "operateType105": "user freeze thaw", "operateType106": "change password", "operateType107": "role manage", "operateType108": "set user role", "operateType109": "role auth", "operateType110": "tenant manage"}}, "datalog": {"entity": {"dataTable": "Table Name", "dataId": "Data ID", "dataVersion": "Version", "createBy": "Create User", "createTime": "Create Time", "code": "Field", "dataVersion1": "Version1", "dataVersion2": "Version2"}, "verify": {}, "other": {"dataCompare": "Data Compare", "dataCompare_tip1": "Select two data items", "dataCompare_tip2": "Select the same database table and data ID for comparison", "okText": "I know"}}, "monitor": {"entity": {"param": "Parameter", "text": "Description", "value": "Value", "timestamp": "Request Time", "method": "Method", "uri": "Request URI", "status": "Status", "timeTaken": "Duration"}, "verify": {}, "other": {"lastUpateTime": "Last Upate Time", "updateNow": "Update Now", "httpCount": "A total of {0} recent HTTP request records were traced", "httppaging": "Display {0} - {1} records, a total of {2} records", "systemInfo": "Server Information", "jvmInfo": "JVM Information", "redisInfo": "Redis Information", "httpTrace": "Http Trace"}}, "quartzjob": {"entity": {"jobName": "Job Name", "jobClassName": "Job ClassName", "description": "Description", "cronExpression": "Cron Expression", "parameter": "Parameter", "status": "Status"}, "verify": {"cronExpression": "Enter the cron expression"}, "other": {"status_option1": "Normal", "status_option2": "Stop", "start": "Start", "stop": "Stop", "start_status": "Started", "stop_status": "Stoped", "start_tip": "Are you sure to start the  task?", "stop_tip": "Are you sure to stop the  task?", "export_filename": "Scheduled Task Info", "execute_one": "Execute one", "execute_tip": "Are you sure to execute one the task？"}}, "message": {"entity": {"esTitle": "Message Title", "esType": "Send Type", "esReceiver": "Receiver", "esTemplateCode": "Template Code", "esParam": "parameter", "esContent": "Message Content", "esSendTime": "Send Time", "esSendStatus": "Status", "esSendNum": "Send Count", "esResult": "Failure Cause", "remark": "Remark", "message": "finished message", "templateCode": "Template Code", "templateType": "Template Type", "templateName": "Template Name", "templateContent": "Template Content", "msgBody": "Test Data"}, "verify": {"esParam": "The input is in JSON format"}, "other": {"esType_option1": "Mobile Message", "esType_option2": "Email Message", "esType_option3": "Weixin Message", "esType_option4": "System Message", "status_option0": "not send", "status_option1": "Send success", "status_option2": "Send failure", "send_email_code": "The verification code has been sent to {0} email!", "send_phone_code": "The verification code has been sent to {0} phone!", "send_code": "The verification code has been sent!", "sendTest": "Send Test", "sysMessage_tab": "Message Manage", "sysMessageTemplate_tab": "Message Template Manage", "export_message_filename": "Message", "export_messageTemplate_filename": "Message Template"}}, "announcement": {"entity": {"titile": "Title", "msgCategory": "Type", "priority": "Priority", "msgType": "Publish Scope", "selectedUser": "Specify Users", "msgContent": "Content", "sender": "Publisher", "sendTime": "Publish Time", "pSendTime": "Please Enter Send Time", "readFlag": "Read Status"}, "verify": {"publish_confirm": "Are you sure  publish the announcement?", "unpublish_confirm": "Are you sure  unpublish the announcement?", "readall_confirm": "Are you sure mark read all messages?", "selectedUser_check": "The specified user cannot be empty", "time_check": "The start time cannot be later than the end time"}, "other": {"publish": "Publish", "unpublish": "Unpublish", "readall": "<PERSON> All", "msgCategory_option1": "announcement", "msgCategory_option2": "system message", "priority_option1": "low", "priority_option2": "middle", "priority_option3": "high", "msgType_option1": "specify users", "msgType_option2": "all users", "sendStatus_option0": "not publish", "sendStatus_option1": "published", "sendStatus_option2": "undo publish", "no_read": "no read", "have_read": "have read", "msg_label": "Notification", "qu-chu-li": "to deal with"}}, "division": {"entity": {"name": "Division Name", "code": "Division Code", "order": "Division order", "description": "Description"}, "verify": {"name": "Please enter division Name", "code": "Please enter division Code", "codeExisted": "Division Code existed"}}}, "messageCenter": {"admin": {"newMessage": "New Message", "newNews": "New News", "draw": "Draw", "published": "Published", "withdrawn": "Withdrawn", "messageTitle": "Message Title", "pMessageTitle": "Please Enter Message Title", "selectGroups": "Select Groups", "selectBusiness": "Select Business", "allBusiness": "All Business", "targetBusiness": "Target Business", "pCompanyCode": "Please enter the enterprise code or name", "exclude": "Exclude", "joinIn": "Join In", "remindType": "Remind Type", "cornerReminder": "<PERSON> Reminder", "dialogReminder": "<PERSON><PERSON>", "popUpReminder": "Pop<PERSON><PERSON>", "delayTime": "Delay off time", "unitSecond": "The unit is second, manual shutdown can be written", "expiredTime": "Expired Time", "today": "Valid for the day", "longTerm": "long term effective", "isArchive": "Whether to archive", "archive": "Archive", "notArchive": "Do Not Archive", "fileBrowsing": "File Browsing", "fileUpload": "File Upload", "file": "File", "selectCompany": "Please select a company or business group!", "pEnterTitle": "Please fill in the content of the message!", "publishSuccess": "Published Successfully!", "withdrawSuccessfully": "Withdraw Successfully!", "remind": "Remind", "remindContent": "The content of the current form message has not been saved, whether to continue", "fileSize": "The size of the attachment should not exceed 10M!", "selectFile": "Please select the file to upload!", "groupName": "Group Name", "hasRead": "Confirm read"}}, "eform": {"appgroup": {"entity": {"groupName": "Name", "groupId": "Id", "groupCode": "Code", "groupDesc": "Describe", "orderNum": "Sort", "parentId": "Parent", "select-parentId": "Please select parent", "icon": "Icon", "enableCreateTable": "can create table", "backgroundColor": "Background Color", "isMobile": "Mobile display", "isCommon": "Is Common", "isCommonApp": "Whether public configuration application", "moduleType": "Module Type", "shadowMode": "Enable Shadow Mode", "level1": "Level 1 module", "level2": "Level 2 module", "formOrViewName": "Form/list name", "formOrViewCode": "Form/list code", "type": "type"}, "verify": {"groupName": "Please enter a name", "groupCode": "Please enter a code"}, "other": {"current_app": "Current Application", "common_app": "Common Application", "group_name": "Group Name", "group_manage": "Group Manage", "new_app": "New App", "import_batch": "Import Batch", "export_batch": "Export Batch", "import_new": "Import New", "import_cover": "Import Cover", "delete_cache": "Delete Cache", "edit_app": "Edit App", "delete_app": "Delete App", "comment": "comment", "publish": "publish", "revert": "revert", "version": "version", "viewpublish": "view publish", "export_app": "Export App", "copy_app": "Copy App"}}, "apptable": {"entity": {"logical_AND": "Logical AND", "key": "key (drop-down box binding data, drop-down box format is label, value)", "value": "value(Interface returns data key)", "entityModel": "Entity Model", "compare": "Comparison method", "limitation": "limitation factor", "addNo": "Do not apply when adding", "editNo": "Do not apply when edit", "detailNo": "Do not apply when detail", "compareFiled": "Comparison filed", "addCompare": "Add comparison method", "compareValue": "Comparison value", "isHideFields": "Whether to hide fields", "hideFields": "Other Fields", "operationField": "Operation field", "property": "set a property", "dataRange": "Data range", "showBlue": "Show Blue Stars", "noBlue": "Remove Blue Stars", "noValue": "Remove Value", "noRequire": "Not required", "equalNum": "equal(Num)", "equalStr": "equal(Str)", "lessThan": "less than", "greaterThan": "greater than", "lessThanEq": "less than or equal to", "greaterThanEq": "greater than or equal to", "contain": "contain", "not_contain": "not contain", "empty": "Data is empty", "noEmpty": "Data is not empty", "startWith": "Start with", "endWitn": "End witn", "erModel": "ER Model", "apiErModel": "ER model (takes precedence over context parameters)", "erModelSelect": "ER Model Select", "erModelColumnSelect": "ER Model Column Select", "memuParam": "<PERSON><PERSON> param(Priority over stores)", "stores": "Stores(Priority over fixed value)", "memuParamPlaceholder": "Please enter menu parameters", "storesPlaceholder": "Please enter stores", "fixedValuePlaceholder": "Please enter Fixed value", "contentColumnSelect": "Context parameter selection", "contentColumn": "Context parameters (takes precedence over menu parameter)", "returnData": "Return data structure", "checkSql": "check sql", "verifySql": "Verification Sql(Menu parameters are spliced into sql with #{xxx})", "replaceSql": "Replace Sql(Replace ${xxx} in rule description)", "dataModel": "Data Model", "formModel": "Form Model", "flowModel": "Flow Model", "reportModel": "Report Model", "viewModel": "Page Model", "dictModel": "Dict Config", "menuModel": "<PERSON><PERSON>", "languagesModel": "Language Config", "tableName": "Table Name", "tableDesc": "Description", "dsName": "DataSource", "syncTable": "Synchronization table", "isSync": "Table State", "objName": "ER Name", "objCode": "ER Code", "objStructure": "ER Structure", "persistenceType": "Persistent mode", "colCode": "Column Code", "relation": "logical relation", "filterParams": "filter params", "filterRelation": "filter relation", "addParam": "add param", "addFilterParams": "add filter relations", "clearParams": "clear params", "erModelTab": "ER Model Config", "scriptTab": "<PERSON><PERSON><PERSON> Config", "generateScript": "generate into Script Config", "colVal": "value", "colName": "Column Name", "columnName": "Column Name", "oldTable": "Old Table Name", "oldTableDesc": "Old Table Describe", "newTableName": "New Table Name", "oldObjName": "Old Obj Name", "oldObjCode": "Old Obj Code", "newObjName": "New Obj Name", "newObjCode": "New Obj Code", "oldFormName": "Old Form Name", "oldFormCode": "Old Form Code", "newFormName": "New Form Name", "newFormCode": "New Form Code", "oldViewName": "Old View Name", "oldViewCode": "Old View Code", "newViewName": "New View Name", "newViewCode": "New View Code", "newTableDesc": "New Table Describe", "sameTableName": "Table names cannot be the same", "sameObjCode": "Obj Code cannot be the same", "sameFormCode": "Form Code cannot be the same", "sameViewCode": "View Code cannot be the same", "sameTaskCode": "Task Code cannot be the same", "colDescription": "Column Description", "colType": "Column Type", "colLength": "Column Length", "colPointLength": "Point Length", "message": "Length prompt script", "isQuery": "Whether to query the column", "viewShow": "Whether View Show", "formShow": "Whether Form Show", "toFormColCode": "Corresponding form extension fields", "formColCode": "Corresponding form field", "importSort": "Import order", "isKey": "Primary key", "isNull": "<PERSON>", "isSeq": "Is Seq", "colDefaultVal": "Default Value", "checkedValue": "Checked Value", "unCheckedValue": "Unchecked Value", "showType": "Show Type", "showConfig": "Show Config", "dataConfig": "Data Conversion", "validRule": "Validation Rules", "isRequired": "Required <PERSON>", "lengthValid": "Validate length", "er_type": "Relationship", "er_fks": "Foreign Key", "objectBaseInfo": "Object BaseInfo", "objectCommonInfo": "Object CommonInfo", "dataShowConfig": "Data Show Config", "objectAdvancedConfig": "Object Advanced Config", "columnCode": "Column Name", "validColumn": "<PERSON><PERSON>", "columnType": "Column Type", "menuParam": "<PERSON>u param", "table": "Table", "tableColumnSelect": "Table Column Select", "ruleDescription": "Rule Description", "expectedType": "Expected Type", "checkOperator": "Check Operator", "expectedValue": "Expected Value", "interfaceUrl": "Interface Url"}, "verify": {"confirm_create_table": "Are you sure to create the entity table", "confirm_sync_table": "Are you sure to synchronize entity tables", "confirm_handle_type": "Duplicate data exists, Select a deal mode", "confirm_handle_title": "deal mode", "create_table_success": "Create table success", "table_exist": "The table already exists. Please synchronize operation", "sync_success": "Synchronous success", "table_not_exist": "The table does not exist. Create a table", "system_table_no_update": "The system table cannot be modified", "confirm_create_er_form": "Do you want to automatically generate the ER model and the form model", "confirm_create_er": "Do you want to automatically generate the ER model", "tip_sql": "Please enter SQL on the main page", "columnName": "Field name cannot be empty", "checkSql": "The check sql cannot be empty", "checkOperator": "The check operator cannot be empty", "columnCode": "Column Name cannot be empty", "ruleDescription": "Rule description cannot be empty", "expectedType": "Expected type cannot be empty", "expectedValue": "Expected value cannot be empty", "interfaceUrl": "The interface url cannot be empty", "urlExists": "The interface url already exists", "compare": "Comparison method is required"}, "placeholder": {"dateTimeformat": "Please select a date and time format", "viewName": "Please select the page code", "keyField": "Please select the storage field", "labelField": "Please select the display field", "sqlName": "Select Sql configuration", "httpName": "Select Service configuration", "colLength": "Please enter the field length", "formatExpr": "${'{'}key{'}'} and ${'{'}value{'}'} represent original values", "form_show_type": "Select display type", "form_placeholder": "The default placeholder is the field name, or you can customize the placeholder content", "form_date": "Please select a date format", "form_time": "Please select a time format", "form_min": "Please enter the minimum value", "form_max": "Please enter the maximum value", "form_step": "Please enter the step size", "form_minRows": "Please enter the minimum height", "form_maxRows": "Please enter the maximum height", "prompt": "Display fields must be set; A storage field can be empty. If a storage field is set, it must be a unique index field", "form_limit": "Please enter the maximum number of uploads", "fileTableStyle": "Please enter the tabel style", "form_bizPath": "Please enter a storage directory", "form_listType": "Please select a style", "delimiter": "please enter the delimiter", "template_name": "please enter the template name", "template_select": "please select the template"}, "other": {"source_gen": "Generate source", "button_update_data": "Update data from entity", "button_copy_data": "copy data to config", "load_database": "Load from database", "db_properties": "DB Properties", "page_properties": "Page Properties", "create_table": "Create Table", "sync_table": "Sync Table", "create_er": "Create ER Model", "create_er_form": "Create ER And Form", "handleCover": "Cover", "no-handleCover": "Not covered", "handleIgnore": "Ignore", "handleCancel": "Cancel", "data_conversion_config": "Data Conversion Config", "data_conversion_mode": "Data Conversion", "date_time_format": "Datetime Format", "default_value": "Default value", "label_color": "Label Color", "static_options": "Static Options", "option": "Option", "data_dictionary": "Data Dictionary", "sql_configuration": "Sql Config", "service_configuration": "Service Config", "backfill_configuration": "Backfill field configuration", "page_code": "Page Code", "storage_field": "Storage Field", "display_field": "Display Field", "default_first": "Default the first item", "formatted_expression": "Formatted Expression", "options_expression": "Options Formatted Expression", "view_expression": "View Formatted Expression", "option_default": "No convert", "option_defaultValue": "Customize default value", "option_dateTime": "Time format", "option_options": "The static option Code transfer  name", "option_dictOption": "Data dictionary Code transfer name", "option_sqlConfig": "Configure the transformation through Sql", "option_httpConfig": "Configure the transformation through Http", "option_user": "User ID transfer  name", "option_userCode": "User code transfer  name", "right_button": "right button", "option_dept": "Department ID transfer  name", "option_deptCode": "Department Code transfer  name", "option_page": "Data transformation through the page model", "option_image": "Display in the form of pictures", "option_file": "Display as an attachment", "option_links": "Display it as a hyperlink", "option_switch": "Display it as a switches", "option_download": "declare file download link", "form_show_type": "Display type", "form_placeholder": "Placeholder content", "shi-fou-ti-shi": "Prompt or not", "ti-shi-yu": "hint", "form_date": "Date format", "form_time": "Tate format", "form_options": "Static options (prior to data dictionary)", "form_dictOption": "Data dictionary (prior to Sql configuration)", "form_sqlConfig": "Sql configuration (prior to service configuration)", "form_column_config": "column config", "form_min": "Minimum value", "form_max": "Maximum value", "form_step": "Step length", "form_precision": "Precision", "form_minRows": "Minimum height", "form_maxRows": "Maximum height", "form_other_config": "Other configuration", "value_range": "Value range", "form_viewName": "Page code", "form_fields": "Backfill field", "form_limit": "Maximum upload quantity", "limit_size": "upload file size", "suffix": "upload file suffix", "suffixPlaceHolder": "separate by ';' e.g. png;jpeg;doc", "suffixTips": "upload file suffix limit:", "form_upload_type": "upload address type", "delay_upload": "upload later", "declare_config": "declare port config", "customs_config": "customs port config", "upload_classify": "upload classify", "upload_file_type": "upload file type", "fileTableStyle": "Table style", "declarePortUrl": "declare port url,eg: acmp/api", "customsPortUrl": "customs port url,eg:cs/api", "fileQueryParams": "Query File Params", "formId": "Main form id", "form_bizPath": "Storage directory", "uploadShowListHead": "upload hid list head", "uploadShowCheckedCol": "upload only show checked col", "separator": "separator", "form_listType": "Style", "option_showTime": "Time selector", "start_today": "start today", "start_tomorrow": "start tomorrow", "date_multiple": "Date multiple", "option_clearable": "can remove", "option_range": "The date range", "number_range": "The number range", "option_single": "The date single", "option_multiple": "multi-select", "allowDirectInput": "allow direct input", "allowDirectLength": "allow direct length", "option_allowHalf": "Allow half", "option_drag": "Allowed to drag and drop", "option_hidden": "hidden", "option_year": "picker year", "option_showSearch": "searchable", "option_ignoreCase": "Ignore case when filtering", "er_add_mainTable": "Set the main table", "er_close_objRelation": "Closes the object relationship string", "er_show_objRelation": "Open the object relationship string", "er_add_subTable": "Add a child table", "er_mainTable": "The main table", "er_objectRelation": "Object relational", "er_repeatTable": "Duplicate tables exist:", "childField": "Sub table fields correspond to primary table foreign keys", "parentField": "The foreign key of the child table corresponds to the field of the main table", "loading_flag": "Loading flag", "flag_show_value": "Flag show value", "dynamic_enable": "Inline image", "dynamic_upload": "Image upload", "dynamic_img_position": "Image position", "dynamic_img_distance": "Distance from text", "dynamic_img_width": "Image width", "dynamic_img_height": "Image height", "dynamic_unit": "Unit：px", "dynamic_show": "Image show value", "dynamic_show_placeholder": "Equal to the value displayed in the list", "show_expand_field": "Show extension field", "upload_bill_type": "Document type", "upload_remark": "Remark", "excel_save_url": "custom save url", "excel_refresh_url": "custom refresh url"}, "dataFilter": {"data_filtering": "Data filtering", "data_Add": "Data Add", "add_data": "Add Data", "enable": "Enable", "filter_content_method": "Filter content method", "select_filter_content_method": "Please select filter content method", "filter_content": "Filter content", "sql": "Filtering through SQL", "key": "Select Key", "text": "Select Text", "sql_placeholder": "Please enter SQL, Only single field queries are supported, for example:select distinct xxx as value from table", "static": "Filtering through static data", "static_option": "Static option"}}, "appimport": {"entity": {"taskName": "task name", "oldTaskName": "old task name", "newTaskName": "new task name", "taskCode": "task code", "oldTaskCode": "old task code", "newTaskCode": "new task code", "importMode": "import mode", "importType": "import type", "add": "add import", "update": "update import", "delete": "delete import", "save": "add and update import", "single-table": "single table", "multi-table-single-sheet": "multi table single sheet", "multi-table-multi-sheet": "multi table multi sheet", "multi-table-single-sheet-thailand": "multi table multi sheet(Thailand)", "pageTitle": "page title", "headStartRow": "head start row", "startRow": "start row", "listStartRow": "list start row", "customParam": "custom params in import", "sheetName": "sheet name", "businessPk": "business primary key", "association": "association", "childField": "Sub table fields correspond to primary table foreign keys", "isExcelField": "IS Excel Field", "isAllowEdit": "IS Allow Edit", "confirmTitle": "Warning", "confirmContent": "The business primary key has not been filled in. The import will be updated in full. Are you sure to submit?"}, "verify": {"taskName": "please enter task name", "taskCode": "please enter task code", "importMode": "please enter import mode", "importType": "please enter import type", "pageTitle": "please enter page title", "headStartRow": "please enter head start row", "startRow": "please enter start row", "listStartRow": "please enter list start row", "customParam": "please enter custom param", "sheetName": "please enter sheet name", "businessPk": "please enter business primary key", "taskCode_repeat": "task code repeat"}, "button": {"template-setting": "template setting", "rule-setting": "rule setting"}}, "appform": {"common": {"online_form": "Online form", "registration_form": "Registration form", "business_form_title": "Business Form", "approval_opinions_title": "Approval Opinions", "process_chart_title": "Process Diagram"}, "entity": {"formName": "Form Name", "formCode": "Form Code", "formVersion": "Form Version", "objectName": "ER Model", "formStatus": "Form Status", "tableStatus": "Table Status", "pcUrl": "PC URL", "mobileUrl": "Mobile URL", "delClass": "Data Delete API", "serviceName": "Service Code"}, "verify": {"formName": "Please enter a form name", "formCode": "Please enter the form code", "ERmodel": "Please enter the ER model", "formCode_format": "The form code cannot contain Chinese characters", "select_file": "Please select the file to be uploaded first!"}, "business": {"serviceName": "service name", "logicDescription": "logic description", "isPersistence": "business type", "addBusinessItem": "add service item", "editBusinessItem": "edit service item", "seeBusinessItem": "see service item", "deleteBusinessItem": "delete service item", "delete_success": "delete success", "executeBusiness": "execute business", "sqlBusiness": "sql business", "dataSync": "data sync", "dataCopy": "data copy", "originObject": "origin object", "targetObject": "target object", "takeOriginColumns": "take origin columns", "targetListColumns": "target list columns", "originObjectColumns": "origin object columns", "targetObjectColumns": "target object columns", "fixedValue": "fixed value", "businessObject": "business object", "businessColumns": "business columns", "columnsConfig": "columns config", "addColumns": "add columns", "generateRules": "generate rules", "pCode-company": "pCode company info", "pCode-complex": "pCode complex info", "elements": "declaration elements", "storage_table_name": "storage table name", "storage_table_name_placeholder": "please input storage table name", "map_column": "map column", "pCode_map_column": "pCode map column", "pCode_map_column_note": "Column to back fill, uncheck back fill current column", "customParam": "custom params in import", "content": "content", "customParamPlaceholder": "please input custom params", "p_code_source": "pCode data source", "http_type_support": "Current only support COUNTRY_ALL,CURR_ALL", "column_passed": "Columns in the table that need to be passed", "elements_table": "Declaration element storage table name", "creditcode": "credit code", "source_col": "source col", "contextPlaceholder": "please select context", "pCodePlaceholder": "please select pCode type", "businessCheck": "business check", "dataStorage": "data storage", "executeAfterStorage": "execute after storage", "seqPreviewAndSetting": "Seq preview and settings", "unit1": "Unit1", "unit2": "Unit2", "taxRule": "Tax Rule", "regulatoryConditions": "Regulatory Conditions", "inspmonitorcond": "Inspmonitorcond", "ciq": "CIQ", "preShipmentInspect": "Pre Shipment Inspect", "usedMachineBan": "Used Machine Ban", "importBan": "Import Ban", "impDiscount": "Imp Discount", "impTempRate": "Imp Temp Rate", "specialAgreement": "Special Agreement", "fill-in-format": "Fill in format", "debugLevel": "debug level", "executeStmt": "execute statements", "executeParameters": "execute param", "executeRes": "execute result", "debugDetail": "Debug detail"}, "other": {"pc_form_design": "PC Form Design", "mobile_form_design": "Mobile Form Design", "pc_form_test": "Form Test", "pc_form_url": "Form URL", "placeholder_delClass": "Enter the data deletion interface, using the name of the SpringBean", "placeholder_serviceName": "In distributed mode, you need to enter the service code", "form_url_label": "Add page URL", "back_fill": "Enter back fill", "back_fill_scene": "Scene", "back_fill_scene_add": "Add scene setting", "pcode_back_fill": "PCode enter back fill", "pcode_service_code": "Service code", "pcode_service_code_add": "Add service code", "pcode_back_fill_field": "Back fill fields", "pcode_back_fill_field_add": "Add back fill fields", "pcode_back_fill_choose_placeholder": "Please select the fill data field", "pcode_back_fill_input_placeholder": "Please enter the key returned by pCode", "change_fill": "Change fill", "click_fill": "Click fill", "change_event": "Not trigger change event", "detail_no_event": "Not trigger on details", "enter_fill_event": "<PERSON>gger enter backFill event", "default_enter_fill": "Triggers enter backFill", "dbclick_cell_tip": "Double click cell editing"}, "api": {"api_settings": "Api service settings", "api_settings_add": "Add Api service settings", "path": "Request path", "input_path": "Please input request path", "method": "Request method", "request_param": "Request param", "setting": "Setting", "param_name": "Param name", "input_param_name": "Please input param name", "param_name_null": "Param name cannot be empty", "param_source": "Param source", "select_param_source": "Please select param source", "param_source_null": "Param source cannot be empty", "param_value": "Param value", "select_param_value": "Please select param value", "input_param_value": "Please input param value", "param_value_null": "Param value cannot be empty", "erModel": "ER Model", "context": "Context", "button_datasource": "Button data source", "fixed": "Fixed", "input_api_key": "Please input Api result key", "type": "Type", "field_value": "Field value", "data_source": "Data source", "label": "Label", "value": "Value"}, "sql": {"sql_setting": "Sql service settings", "sql_setting_add": "Add Sql service settings", "sql_create": "SQL generation and parsing", "setting": "Setting", "sql_param": "Dynamic parameter source", "erModel": "ER Model", "context": "Context", "back_list": "Back list", "param_code": "Param code", "param_name": "Param name", "input_param_name": "input param name", "table_name": "table name", "input_table_name": "input table name", "select_param_value": "select param value", "param_value": "param value", "parse_sql": "No Data,Please parse SQL", "mapping_sql": "Please select SQL mapping field", "data_filter": "Data filter", "filter_type": "Filter type"}, "showHide": {"show_hide": "Linkage show hide settings", "add_show_hide_scene": "Add Linkage show hide scene", "linkage_unit": "Linkage unit", "unit_option": "Unit option", "select_unit": "Please select unit", "after_other_scene": "Execute after other scene completed"}, "scene": {"form_scene": "Form scene operation", "add_disabled": "Disable when add", "edit_disabled": "Disable when edit", "detail_disabled": "Disable when view", "add_hidden": "Hidden when add", "edit_hidden": "Hidden when edit", "detail_hidden": "Hidden when detail"}, "button": {"followOperate": "Subsequent operations", "isLazy": "Whether to lazy load the page"}, "edit": {"editDefault": "Whether to set default value when editing"}}, "appreport": {"entity": {"widgetName": "Widget Name", "widgetCode": "Widget Code", "datasetName": "Dataset Name", "datasetCode": "Dataset Code", "httpMethod": "Http request", "pleaseEnterAddress": "please enter address", "widgetUrl": "Widget URL", "datasetId": "Dataset", "dimension": "Dimension", "measure": "Measure", "custom": "Describe", "alias": "<PERSON><PERSON>", "exp": "Expression", "col": "Column", "type": "Symbol", "values": "Value", "colCode": "Column Code", "colName": "Column Name", "isQuery": "Is Query", "queryOp": "Query Symbol", "queryDefaultValue": "<PERSON><PERSON><PERSON>", "queryIsShow": "Default Expand", "showButton": "Whether to show button", "buttonScript": "Button custom script", "promptTextSet": "Prompt Text Setting", "promptText": "Prompt Text Script", "promptColor": "Prompt Text Color"}, "verify": {"widgetName": "Please enter a widget name", "widgetCode": "Please enter a widget code", "widgetCode_format": "Widget code cannot contain Chinese characters", "alias_repeat": "Aliases cannot be repeated", "datasetName": "Please enter a dataset name", "datasetCode": "Please enter a dataset code", "alias_null": "Aliases cannot be empty", "exp_null": "Expressions cannot be empty", "col_null": "Fields cannot be empty", "type_null": "Symbols cannot be empty", "values_null": "The value cannot be null", "datasetCode_format": "Dataset Code cannot contain Chinese characters"}, "other": {"report_widget": "Widget", "report_datase": "Dataset", "viewData": "View Data", "generateJSON": "Generate JSON", "tab_basic": "Basic Infor", "tab_dimension": "Dimension And Measure", "tab_measure": "Calculate Measure", "tab_filter": "Filter Conditions", "tab_query": "Query Conditions"}}, "appview": {"entity": {"viewRedirectId": "View redirect id", "modifyExport": "Modify export prompt", "viewId": "Page Id", "viewName": "Page Name", "reportTitle": "Report Title", "reportSql": "Report SQL", "viewCode": "Page Code", "otherUploadUrl": "Other Upload Url", "afterUploadInfo": "Upload Success Info", "afterDeleteScript": "Delete Success Info", "fileSize": "file size limit", "viewType": "Page Type", "viewStatus": "Page Status", "colCode": "Column Code", "colName": "Column Name", "colAlias": "<PERSON><PERSON><PERSON>", "mergeHeaderName": "<PERSON><PERSON>", "colType": "Type", "alignType": "Align", "colWidth": "<PERSON><PERSON><PERSON>", "colMaxWidth": "<PERSON>", "isHidden": "Hidden", "isExport": "Export", "isFilter": "Filter", "exportSetting": "Edit Export Configuration Items", "isEdit": "Is Edit", "isRequire": "Is Require", "import_require": "Excel Required(Import)", "import_require_help": "", "removeTailingZeros": "removeTailingZeros", "autoHeight": "autoHeight", "fixed": "Fixed", "isEllipsis": "El<PERSON><PERSON>", "tuoMin": "Encrypt", "actionConfig": "Action", "isImport": "Import", "isRelated": "Is it related", "importRules": "Import Rule", "importRulesAdd": "Import Rule Add", "importRulesUpdate": "Import Rule Update", "importRulesDelete": "Import Rule Delete", "importRulesSave": "Import Rule Add And Update", "importSetting": "Import Setting", "isSort": "Sort", "isAsyncImport": "Async Import", "add": "Add", "replace": "Replace", "update": "Update", "delete": "Delete", "save": "Add And Update", "isImportAdd": "Add", "isImportUpdate": "Update", "isImportDelete": "Delete", "isImportSave": "Add And Update", "asyncImportRules": "Async Import Rule", "queryAlias": "Query <PERSON>", "isQuery": "Is Query", "isDefaultQuery": "Is Default Query", "queryOp": "Operator", "queryDefaultValue": "<PERSON><PERSON><PERSON>", "queryIsHidden": "Is Hidden", "queryOrder": "Sort", "openType": "Operation Type", "shadowTradeCode": "Shadow Trade Code", "shadowType": "Shadow Type", "shadowParentId": "<PERSON><PERSON>", "buttonName": "Name", "buttonType": "Type", "buttonStyle": "Style", "buttonIcon": "Icon", "buttonIconClass": "Icon Color", "viewIcon": "View Icon", "buttonGroup": "Button Group", "dialogSize": "Open Type", "linkForm": "Associated Form", "linkView": "Associated Page", "exportDetailViewName": "Export parts list body list", "objectName": "ER Model", "isMultiple": "Disable Multiple", "outParameter": "Out Parameter", "showCondition": "Show", "detailVisible": "Visible during viewing", "buttonAction": "Button Action", "asyncImport": "Async Import", "confirmBox": "confirm dialog", "isExpand": "Is Expand", "relaWarnItem": "Related warning items", "warnCustomize": "Warning condition setting", "isPermission": "Is Permission", "permission": "Permission", "openScript": "<PERSON><PERSON><PERSON>", "showScript": "Show Script", "watchScript": "<PERSON> Script", "butScriptDetails": "Event", "parameterKey": "Parameter Key", "parameterName": "Parameter Name", "parameterType": "Parameter Type", "isRequired": "Is Required", "requireType": "Require Type", "outKey": "Out Parameter Key", "sqlOrder": "Custom SQL Sort", "defaultQuery": "De<PERSON><PERSON>", "checkType": "CheckBox", "isFlow": "Is Process", "flowScope": "Process Scope", "refreshAfterView": "Refresh after view or edit", "showSaveQuery": "Whether to enable saving query conditions", "isLineNum": "LineNum", "isPage": "Paging", "pageNum": "<PERSON>", "actionWidth": "Operation Column Width", "scrollEnable": "<PERSON><PERSON>", "scrollWidth": "<PERSON><PERSON>", "fixedAction": "Fixed Operate Column", "fixedBpmColumn": "Fixed Process Column", "queryConditionShow": "Query Condition Show", "breadCrumbShow": "BreadCrumb Show", "queryParamsNum": "Query Params Row Num", "isPageSize": "Whether to save paging size", "isHeadBorder": "Whether to enable header border", "sqlExecutor": "Sql Execotor", "nestedTables": "Whether to enable nested tables", "settingNestedTables": "Nested tables", "nestedTablesName": "Nested tables Name", "mappingRelations": "Mapping Relations", "tableFieldName": "Please select the parent table field name", "mappingFieldName": "Please enter the sub mapping field name", "customPaginationShowTotal": "Custom Pagination Show Total", "pageMountedScript": "Page Mounted Script", "tagClick": "tag click", "tagClose": "tag close", "handleResetQuery": "handle reset query", "loadApiScript": "Load Api Data Script", "loadOnceScript": "Before loadData Script(only once)", "beforeScript": "Before Script", "loadBeforeScript": "Before Search Script", "loadAfterScript": "After Search Script", "afterScript": "After Script", "beforeJava": "Before Java", "afterJava": "After Java", "beforeGroovy": "Before Groovy", "afterGroovy": "After Groovy", "customRowFunc": "Row Function", "customRowClick": "Row Function", "showLocation": "Display Position", "isLoading": "Is Loading", "buttonVerify": "Button Verify", "customSqlParam": "Button jump list custom parameter", "selectCustomView": "Please select the page before jump", "paramSource": "Param source", "paramSource_search": "Query", "paramSource_col": "Col", "param": "Param", "selectParam": "Please select the param", "paramText": "The resolved dynamic parameters", "changeLinkage": "Change Linkage", "importIndex": "Sort", "tian-xie-shuo-ming": "Filling Instructions"}, "changeLinkage": {"enable": "Enable", "scene": "Scene setting", "query": "Query criteria", "field_add": "Add Query criteria", "field": "Query criteria", "linkage_setting": "Linkage setting", "field_choose": "Please choose query criteria"}, "verify": {"parameterKey_repeat": "The entry parameter key must be unique", "viewName": "Please enter a page name", "viewCode": "Please enter a page code", "viewType": "Please enter a page type", "dataSourceDefault": "Please select a datasource", "colCode": "Column code cannot be empty", "colName": "Column name cannot be empty", "colType": "Column type cannot be empty", "openType": "The operation type cannot be empty", "buttonName": "The button name cannot be empty", "parameterKey": "Parameter key  cannot be empty", "parameterName": "Parameter name  cannot be empty", "parameterType": "Parameter type  cannot be empty", "sql_parse": "The return field must contain the ID, otherwise the page cannot be properly parsed, please re-edit the SQL", "link_page": "Configure the association page first", "viewCode_format": "Page code cannot contain Chinese characters"}, "other": {"handleAddLayout": "New Composite Page", "handleAddDragLayout": "New Free Layout Page", "handleAddTable": "New Table Page", "handleAddReport": "New Report", "handleAddTree": "New Tree Page", "handleAddMobile": "New Mobile Page", "pageUrl": "Page Url", "basic_info": "Basic Information", "table_config": "Table Config", "query_config": "Query Config", "button_config": "<PERSON><PERSON> Config", "shadow_button_config": "<PERSON> Config", "parameter_config": "Parameter Config", "parse_sql_tip": "Tip: the query table parsed is", "sqlOrder_tip": "Enter sort statements, such as: field 1 desc, field 2 ASC", "parse_sql_help": "The expression #{parameter.xx} can be used in the query. The variables represented by xx include：Common context variable：[ userId、loginName、deptId、deptCode、orgId、orgCode、currentUserName、currentLoginName、currentUser、currentOrgName、currentOrg、currentDeptName、currentDept、currentCompCode、currentCompName、currentSocialCreditCode ]；Variable for jump list: [ previousIds(the id value checked in the previous list，pgSql Example：string_to_array(id, ',') <@ ARRAY[string_to_array(#{parameter.previousIds}, ',')]  );other can get from Button jump list custom parameter]", "button_script_help": "The expression ${'{'}xx{'}'}can be used in the query. The variables represented by xx include：Common context variable：[ currentUser、currentUserName、currentLoginName、currentOrg、currentOrgName、currentDept、currentDeptName、currentCompCode、currentCompName、currentSocialCreditCode、currentDivisionId、currentDivisionCode、currentDivisionName、currentTenantId、currentTenantCode、currentTenantName ]", "java_event_tip": "Please use the SpringBean", "nothing": "none", "permission_help": "The authorization id configured in the menu is [page Code: Permission ID].Only the permission id is entered here", "queryDefaultValue_help": "Support ${variable}", "column_verification": "Column Verification", "sql_verification": "Sql Verification", "interface_verification": "Interface Verification"}, "label": {"single": "Only one piece of data must be selected", "multiple": "At least one piece of data must be selected", "uncheck": "Allow unchecked data"}}, "apptemplate": {"entity": {"tempName": "Template Name", "tempCode": "Template Code", "tempType": "Template Type", "tempStructure": "Data Structure", "tempBodyString": "Template <PERSON><PERSON><PERSON>", "tempTypeModel": "Application Type", "tempDesc": "Template Description"}, "verify": {"formName": "Please enter a template name", "formCode": "Please enter a template code", "formName_repeat": "The template name already exists"}, "other": {}}, "appmarket": {"entity": {"appName": "Application Name", "appCode": "Application Code", "appVersion": "Application Version", "createTime": "Create Time"}, "verify": {}, "other": {"install": "Install", "update": "Update", "confirm_install": "Are you sure install?", "confirm_update": "Are you sure update?"}}, "formDesign": {"label": {"search_params": "Query params", "clean_selected": "clear selected", "search_setting": "Search Setting", "gw_setting": "Gw Setting", "expand_column": "Extended field settings", "Tab1": "Tab1", "Tab2": "Tab2", "Tab": "Tab", "Collapse": "Collapse", "Collapse1": "Collapse1", "Collapse2": "Collapse2", "Collapse3": "Collapse3", "Add_tab": "Add tab", "title_name": "name", "hidden_setting": "Hidden", "lazy_setting": "Lazy", "max_length": "Max length", "integer_number": "Integer Length", "decimal_number": "Point Length", "Add": "Add", "show_arrow": "Show Arrow", "validatorMsg_only": "single check", "single": "pk", "Add_regular_check": "Add regular check", "Add_length_check": "Add length check", "Add_single_check": "Add single check", "Add_group_check": "Add group check", "placeholder_select": "Please select", "placeholder_input": "Please enter", "required_message": "required fields", "colLength_message": "The content exceeds the length", "colLength_message1": "Length cannot exceed ", "colLength_message2": "digits!", "colLength_num_message1": "Must be a number,integer cannot exceed", "colLength_num_message2": "digits,decimal cannot exceed", "colLength_num_message3": "digits!", "required": "Required", "blue_required": "Blue required", "red_required": "Red required", "first_no_check": "Blue star no check", "validScript": "ValidScript", "disabled": "Disabled", "fixColum": "Whether to fix the column", "buttonConfig": "Config", "isDefault": "System", "TableField_title": "Fields", "tempKey": "TempKey", "tempKey_content": "The TempKey must start with _temp_. Otherwise, there will be an exception when entering the db", "min_width": "Min width", "max_width": "Max width", "total": "Total", "total_now": "Statistics based on current page", "total_all": "All statistics", "align": "Align", "custom_button_title": "Custom button config", "custom_query_column": "Custom query columns config", "init_query_column": "Initialize query columns", "confirm_init_query_column": "confirm init query column", "clean": "cleared", "defaultModel": "default object", "generateJSON": "Generate JSON", "codeTree": "Code Tree", "panel_bjkj": "Layout Controls", "panel_er": "ER Model", "panel_gjkj": "Advanced Controls", "panel_kj": "Controls", "useTemplate": "Use Templates", "yulan": "Preview", "an-niu-shu-xing": "Button properties", "biao-dan-bu-ju": "Form layout", "shi-fou-dan-yi-chuang-kou": "Whether to use single window style", "biao-dan-css": "Form css", "biao-dan-shu-xing": "Form properties", "biao-dan-shu-xing-she-zhi": "Form Property Settings", "biao-qian-zhan": "label:", "chang-yong-lie-kuan-kuai-su-she-zhi": "Column width quick settings", "disable-enter-next-control": "disable enter next control", "show-placeholder-on-disable": "show placeholder when disabled", "chui-zhi": "vertical", "geng-xin-hou": "after update", "geng-xin-qian": "before update", "hang-nei": "inline", "lie-kuan-she-zhi-gong-24": "Column width settings (24 in total)", "shi-ji-yu-lan-xiao-guo-qing-dian-ji-yu-lan-cha-kan": "for the actual preview effect, please click the preview to view", "shi-jian-she-zhi": "Event settings", "fieldConfig": "Field configuration(Execute after the page is loaded)", "shu-ru-kuang-zhan": "Input:", "shui-ping": "horizontal", "xin-zeng-hou": "after add", "xin-zeng-qian": "before add", "yin-cang-bi-xuan-biao-ji": "hide mandatory tags", "selfUpdate": "selfUpdate", "isRecordDataLog": "record data log", "yin-cang-biao-dan-an-niu-lan": "hide form button bar", "1-biao-shi-bu-she-zhi-gu-ding-gao-du": "-1 means do not set a fixed height", "1-biao-shi-zui-hou-yi-hang-0-biao-shi-di-yi-hang": "-1 means the last row, 0 means the first row", "an-niu-cao-zuo": "button operation", "choose-an-niu-cao-zuo": "choose button operation", "bei-jing-yang-shi": "background style", "biao-ge-she-zhi": "Form settings", "biao-ge-yang-shi-css": "Table style CSS", "biao-qian": "Label", "biao-qian-wei-zhi": "label position", "biao-qian-ju-li": "label distance", "breadCrumbSetting": "breadcrumb setting", "queryConditionsShow": "query conditions show", "ke-zeng": "CanAdd", "ke-shan": "CanDel", "ke-bianji": "CanEdit", "hideSaveButton": "HiddenSave", "showQueryCondition": "show queryConditions", "closeQueryCondition": "close queryConditions", "cao-zuo-shu-xing": "Operation properties", "chao-chu-fan-wei-ti-shi": "out of range alert", "chu-shi-hua": "Init", "cong-ru-can-huo-qu": "Get from input", "dang-qian-bu-men-id": "Current Department ID", "dang-qian-bu-men-ming-cheng": "current department name", "dang-qian-ri-qi": "current date", "dang-qian-shi-jian": "current time", "dang-qian-yong-hu": "Current user", "dang-qian-yong-hu-deng-lu-ming": "current user login name", "dang-qian-yong-hu-id": "current user ID", "selectOids": "Select row data primary key", "dang-qian-yong-hu-xing-ming": "current user name", "dang-qian-zu-zhi": "current organization", "dang-qian-zu-zhi-id": "Current Organization ID", "dang-qian-qi-ye-bian-ma": "Current comp code", "dang-qian-qi-ye-ming-cheng": "Current comp name", "dang-qian-qi-ye-she-hui-xin-yong-dai-ma": "Current comp social credit code", "dang-qian-shi-ye-bu-id": "Current division ID", "dang-qian-shi-ye-bu-code": "Current division code", "dang-qian-shi-ye-bu-name": "Current division name", "dang-qian-zu-hu-id": "Current tenant ID", "dang-qian-zu-hu-code": "Current tenant code", "dang-qian-zu-hu-name": "Current tenant name", "uuid": "UUID", "fu-zhu-miao-shu": "auxiliary description", "gao-du": "high", "gen-jie-dian": "root node", "guan-lian-shu-ju-zi-duan": "Linked Data Fields", "guo-lv-quan-xian": "Filter permissions", "hou-tu-biao": "after icon", "jiao-ben": "<PERSON><PERSON><PERSON>", "show-condition": "Show Condition", "jin-cou-xing": "Compact", "jin-yong": "disabled", "set-value": "Set value", "filter-options": "Filter options", "jin-zhi-shou-shu": "hand losing is prohibited", "force-dropdown-value": "force dropdown value", "user-conversion": "user account conversion", "ke-guan-bi": "can be closed", "ke-tuo-zhuai": "draggable", "relatedSteps": "related steps", "relatedStepsNotNull": "related steps not null", "ke-zeng-shan": "Can be added or deleted", "kong-jian-lei-xing": "Control type", "kong-jian-shu-xing-she-zhi": "Control Property Settings", "kuan-du": "width", "left": "left", "lei-xing": "type", "lian-dong-shu-xing": "Linkage property", "lie-pei-zhi-xiang": "column configuration item", "mo-ren-di-yi-ge-ye-qian": "De<PERSON>ult first tab", "mo-ren-xuan-zhong-xiang-key": "Default selected item (key)", "pai-xu-zi-duan": "Sort field", "qi-yong-fu-gai-quan-ju-shang-de-she-zhi": "enable, override global", "qian-tu-biao": "front icon", "qing-dian-ji-you-jian-zeng-jia-hang-lie-huo-zhe-he-bing-dan-yuan-ge": "Please right-click to add rows and columns, or merge cells", "qing-xuan-ze-shang-ji-kong-jian": "Please select a parent control", "quan-xian": "permission", "que-ding-zhi-hang-chu-shi-hua-ma": "Are you sure to perform initialization?", "localSystem": "ICMS", "declarePort": "declarePort", "customsConfig": "customs config", "otherConfig": "other config", "right": "right", "shang-chuan-shu-xing": "upload properties", "shi-jian-ge-shi-ru": "time format such as", "shi-yong-gong-gong-biao-cun-chu-wen-jian-lu-jing": "Use a common table to store file paths", "shu-biao-jing-guo-dian-liang": "The mouse is lit", "shu-ju-biao-shi": "data identification", "shu-ju-dui-xiang": "Data object", "shu-ju-shu-xing": "data attribute", "shu-ju-zhuan-huan": "data conversion", "shu-ju-zi-duan": "Data field", "shu-ru-kuang-type": "input box type", "suo-xuan-shu-ju-dui-xiang-wu-xiao": "The selected data object is invalid", "ti-jiao-biao-dan": "submit Form", "wai-jian-zi-duan": "foreign key field", "wei-xuan-ze-kong-jian": "Control not selected", "wen-zi-da-xiao": "font size", "wen-zi-dui-qi-fang-shi": "text alignment", "wen-zi-jia-cu": "bold text", "wen-zi-yan-se": "text color", "wen-zi-yang-shi": "custom style", "wai-kuang-yang-shi": "parent container style", "wu-bian-kuang": "<PERSON><PERSON><PERSON>", "xian-shi-bi-xuan-biao-ji": "Show mandatory tags", "xian-shi-hong-se-bian-kuang": "Show red border", "xian-shi-pu-tong-bian-kuang": "Show normal border", "xian-shi-bian-kuang": "Show border", "accordion": "accordion", "arrow_alignment": "Arrow alignment", "xian-shi-shu-ru-kuang": "show input box", "xian-shi-shu-xing": "display properties", "shu-ju-yuan-chu-shi-hua": "Init dataSource", "an-niu-lian-dong-cha-xun": "Button Query", "xian-shi-tu-biao": "show icon", "xiao-yan": "Check", "xin-zeng-hang-wei-zhi": "Add row position", "checkStrictly": "Is there a father-son linkage", "defaultExpandAll": "Whether to expand all nodes by default", "treeData": "Set tree component data", "nodeName": "Node name", "requireNodeName": "Require node name", "nodeKey": "Node key", "requireNodeKey": "Require node key", "repeatedNodeKey": "Node key cannot be repeated", "parentKey": "Parent node", "isDisable": "Whether to disable", "initUrl": "Initialize url settings", "initParamScript": "Initialize url parameter script", "clickUrl": "Click url", "select_option": "Static options (level 1)", "select_dictOption": "Data dictionary (level 2)", "select_sqlConfig": "Sql configuration (level 3)", "select_service": "Service configuration (level 4)", "api_service": "Api data source (level 5)", "watch_setting": "Data monitoring settings", "filed_setting": "Filed settings", "ye-mian-bian-ma-pei-zhi": "Pop-up page config", "view-allow-multiple": "Pop-up page allow multiple", "biao-dan-bian-ma-pei-zhi": "Form page config", "ye-qian-pei-zhi-xiang": "Tab configuration items", "zhe-die-mian-ban-pei-zhi-xiang": "Collapse configuration items", "cardExtraConfig": "Card Extra Config", "zhe-die-mian-ban-yin-cang-jiao-ben": "Collapse hide script", "ye-wu-ti-xing": "business reminder", "zha-ge-jian-ju": "grid spacing", "zhi-xuan-bu-men": "select department only", "zhong-zhi-biao-dan": "reset form", "guan-bi-biao-dan": "close form", "saveAndContinue": "save and continue", "closeAndRefresh": "close and refresh", "saveAndRefresh": "save and refresh", "zi-shi-ying-nei-rong-gao-du": "Responsive content height", "zuo-wei-html-xian-shi": "display as html", "dang-qian-zu-zhi-ming-cheng": "current organization name", "an-niu-tu-biao-bu-neng-wei-kong": "button icon cannot be empty", "an-niu-yang-shi-bu-neng-wei-kong": "Button style cannot be empty", "chu-shi-hua-xin-xi-bu-zheng-que-mo-ren-an-niu-diu-shi": "Incorrect initialization information, default button missing", "cong-zuo-ce-xuan-ze-kong-jian-tian-jia": "Select the control to add from the left", "dang-qian-biao-ge-wu-fa-xiang-xia-he-bing": "The current table cannot be merged down", "dang-qian-biao-ge-wu-fa-xiang-you-he-bing": "The current table cannot be merged to the right", "dang-qian-shi-zui-hou-yi-hang-wu-fa-xiang-xia-he-bing": "The current is the last row and cannot be merged down", "dang-qian-shi-zui-hou-yi-lie-wu-fa-xiang-you-he-bing": "Currently the last column, cannot merge to the right", "dao-chu-dai-ma": "export code", "dao-ru-cheng-gong": "Import successful", "dao-ru-json-wen-jian": "import json file", "dao-ru-shi-bai-shu-ju-ge-shi-bu-dui": "Import failed, the data format is incorrect", "dong-zuo-lei-xing": "Action type", "dong-zuo-lei-xing-bu-neng-wei-kong": "Action type cannot be empty", "duo-xuan": "Multiple choice", "guan": "close", "json-shu-ju": "JSON data", "kai": "open", "mei-you-xuan-zhong-shu-ju": "no data selected", "Not_allowed_to_be_empty": "parameter {0} not allowed to be empty", "mo-ren-an-niu-bu-neng-shan-chu": "Default button cannot be deleted", "qing-xuan-ze-da-kai-fang-shi": "Please select how to open", "qing-xuan-ze-guan-lian-biao-dan": "Please select an associated form", "shu-ju-bu-cun-zai": "data does not exist", "shu-xing-pei-zhi": "property configuration", "ti-xing-lie-biao-zi-duan-huan-wei-chu-shi-hua": "Reminder: List field has not been initialized", "xian-shi-ming-cheng-bu-neng-wei-kong": "Display name cannot be empty", "xiang-xia-he-bing": "merge down", "xiang-you-he-bing": "merge right", "zeng-jia-yi-hang": "add a line", "zeng-jia-yi-lie": "add a column", "Before_open": "Before open", "Before_load": "Before load", "After_load": "After load", "Before_save": "Before save", "After_save": "After save", "Form_item_click": "Form item click", "event_change": "Change", "is_event_change": "Whether to trigger data monitoring", "afterDeleteFile": "After delete file", "beforeUploadFile": "Before upload file", "afterUploadFile": "After upload file", "upload_event": "Upload Event", "beforeDeleteFile": "Before delete file", "event_blur": "Blur", "event_enter": "Enter", "event_click": "Click", "bian-ji-shi-jian": "Edit event", "dan-yuan-ge-dian-ji-shi-jian": "Cell click event", "shan-chu-hou-shi-jian": "After delete event", "shan-chu-qian-shi-jian": "Before delete event", "xin-zeng-hou-shi-jian": "After add event", "xin-zeng-qian-shi-jian": "Before add event", "zi-ding-yi-hang-yang-shi": "Custom row style", "zi-ding-yi-dan-yuan-ge-yang-shi": "Custom cell style", "zi-ding-yi-he-bing-han-shu": "Custom merge function", "zi-ding-yi-shu-ju-chu-li-han-shu": "Custom data processing", "zhi": "value", "wei-pei-zhi-shi-jian-dai-ma": "Event code not configured", "bian-ji-bie-ming": "Edit alias", "biao-dan-zi-duan": "form field", "biao-ming": "Table Name", "biao-shi": "logo", "biao-ti": "Title", "ti-shi-yu": "Hint", "multipleType": "Select multiple types", "enableConfirmBox": "enable ConfirmBox", "bu-men-ming-cheng-zhuan-code": "Department Name to Code", "bu-men-ming-cheng-zhuan-id": "Department name to ID", "bu-neng-shan-chu-zui-hou-yi-ge-gen-jie-dian": "cannot delete the last root node", "da-kai-fang-shi": "open way", "dan-ji-huo-tuo-dong-wen-jian-dao-ci-qu-yu": "Click or drag files to this area", "dan-tiao-shu-ju-zhuan-huan": "single data conversion", "dao-xu": "reverse order", "dong-zuo-pei-zhi": "Action configuration", "exprTitle": "You can enter constant values ​​or expressions, expressions including {0}, {1}, {2}, {3}, {4}, {5}, {6}, {7}, {8}, {9} \n, if it is a single data conversion to pass the current value, you can use {10}", "fan-hui-zhi-jiao-ben": "return value script", "fu-wu-can-shu": "Service parameters", "fu-wu-fan-hui-zhi": "service return value", "gen-jie-dian-zhi": "root node value", "guan-lian-bian-ji-biao-dan": "Associative edit form", "guan-lian-xin-zeng-biao-dan": "Associate a new form", "guo-lv-tiao-jian": "filter", "hou-chu-li-jiao-ben": "postprocessing script", "refreshAfterScript": "refresh after script", "tabHeadId": "tab+ page headId", "conditionField": "select row", "hou-chu-li-lei": "post-processing class", "httpTab1": "The return value script is a Groovy script, using <PERSON><PERSON><PERSON>'s value writing method, the initial object is body, the return type is List<Map<String, String>>, and the Map contains two attributes, key and value. \nNote that the initial script can be copied from the help information in the script edit box.", "httpTab2": "The return value script is a Groovy script, using <PERSON><PERSON><PERSON>'s value writing method, the initial object is body, and the return type is String. \nNote that the initial script can be copied from the help information in the script edit box.", "huo-qu-shu-ju": "retrieve data", "infofilename-shang-chuan-shi-bai": "{0} failed to upload.", "isFormTip": "After completing the configuration of service parameters and service return values, click Refresh to re-render the controls", "jian-zhi-dui-zhuan-huan": "key-value conversion", "jing-tai-xuan-xiang-ming-cheng-zhuan-code": "Static option name to Code", "ke-fou-gou-xuan": "Can you tick", "mei-you-pei-zhi-guan-lian-biao-dan": "No associated form configured", "mei-you-pei-zhi-zi-ding-yi-jiao-ben": "No custom scripts configured", "miao-shu": "describe", "nei-rong": "content", "pai-xu-fang-shi": "sort by", "qian-chu-li-jiao-ben": "preprocessing script", "qian-chu-li-lei": "preprocessing class", "qing-shu-ru-bian-ji-bie-ming": "Please enter edit alias", "qing-shu-ru-biao-ming": "Please enter a table name", "qing-shu-ru-can-shu-zhi": "Please enter parameter value", "qing-shu-ru-gen-jie-dian-zhi": "Please enter root node value", "qing-shu-ru-guo-lv-tiao-jian": "Please enter filter criteria", "qing-shu-ru-hou-chu-li-jiao-ben": "Please enter a postprocessing script", "qing-shu-ru-miao-shu": "Please enter a description", "qing-shu-ru-qian-chu-li-jiao-ben": "Please enter the preprocessing script", "qing-shu-ru-xin-zeng-bie-ming": "Please enter a new alias", "qing-shu-ru-zhu-biao": "Please enter main form", "qing-shu-ru-zi-ding-yi-jiao-ben": "Please enter custom script", "qing-shu-ru-xian-shi-jiao-ben": "Please enter show script", "qing-shu-ru-zi-ding-yi-sql": "Please enter custom SQL", "qing-xian-pei-zhi-xian-shi-zi-duan": "Please configure the display fields first", "qing-xian-pei-zhi-ye-mian-bian-ma": "Please configure page encoding first", "qing-xuan-ze-bian-ji-biao-dan": "Please select edit form", "qing-xuan-ze-er-mo-xing": "Please select an ER model", "qing-xuan-ze-pai-xu-fang-shi": "Please select a sorting method", "qing-xuan-ze-pai-xu-zi-duan": "Please select a sort field", "qing-xuan-ze-shu-ju-zhuan-huan-fang-shi": "Please select a data conversion method", "qing-xuan-ze-wai-jian-zi-duan": "Please select a foreign key field", "qing-xuan-ze-xin-zeng-biao-dan": "Please select Add Form", "qing-xuan-ze-zhu-jian-zi-duan": "Please select a primary key field", "qing-xuan-ze-zhuan-huan-qian-dui-ying-de-zi-duan": "Please select the corresponding field before conversion", "shan-chu-qian-jiao-ben": "delete pre-script", "shi-fou-shan-chu-filename": "Delete {0}?", "shu-ju-ku-biao": "Database Table", "shu-ju-ku-biao-zi-duan-zhuan-huan": "Database table field conversion", "shu-ju-zi-dian-ming-cheng-zhuan-code": "Data dictionary name to Code", "shu-ru-xiang-bu-fu-he-yao-qiu": "The entry does not meet the requirements", "tian-jia-cha-xun-zhi": "Add query value", "tian-jia-cha-xun-zi-duan": "Add query field", "tong-guo-zi-ding-yi-jiao-ben-zhuan-huan": "Convert via custom script", "tong-guo-zi-ding-yi-lei-zhuan-huan": "Convert via custom class", "vxe_error": "Validation error at line {0} {1}: {2}", "xiao-yan-zhong": "Checking", "xin-jie-dian": "new node", "xin-zeng-bie-ming": "Add alias", "xun-huan-tiao-yong": "loop call", "ye-mian-zi-duan": "page field", "yong-hu-ming-cheng-zhuan-code": "Username to Code", "yong-hu-ming-cheng-zhuan-id": "Username to ID", "yuan-zi-duan": "source field", "yun-xu-bian-ji": "allow editing", "yun-xu-shan-chu": "allow deletion", "yun-xu-xin-zeng": "Allow adding", "zheng-xu": "positive sequence", "zhong-xin-xuan-ran-fu-wu-pei-zhi": "Re-render service configuration", "preview_pcode_data": "Preview pCode data", "zhu-biao": "Primary table", "zhu-jian-zi-duan": "primary key field", "zi-dong-zhan-kai-ceng-ji": "Automatically expand hierarchy", "zui-da-shang-chuan-shu-liang-wei-thisrecordoptionslimit": "The maximum number of uploads is {0}", "bao-cun-cheng-gong": "Successfully saved", "bi-tian-xiao-yan": "Required check", "biao-da-shi": "expression", "biao-dan-an-niu-lan": "form button bar", "biao-dan-she-zhi": "Form settings", "cha-xun-zhi": "query value", "chang-du-xiao-yan": "length check", "jing-du-xiao-yan": "digit check", "chang-yong-ying-yong-thiscustomapplistlength": "Common applications ({0})", "chu-can-pei-zhi": "parameter configuration", "dao-ru-gui-ze-pei-zhi": "Import rule configuration", "geng-gai-ye-qian-ming": "Change tab name", "groovy-jiao-ben": "Groovy script", "gu-ding-gao-du": "fixed height", "kai-qi-mo-ren": "on (default)", "ke-bian-ji": "editable", "wei-hu": "maintain", "ke-yong-biao-da-shi": "Available expressions", "ke-yong-ji-suan-fu": "Available calculators", "ke-yong-zi-duan": "Available fields", "lian-dong-qi-ta-mo-xing": "Link other models", "xian-yin-gui-ze": "Show rules", "te-shu-chu-li": "Is it a one-on-one special treatment", "xian-shi-mo-ren": "show (default)", "bao-cun-hou-xian-shi": "show after save or select parent table", "hidden": "hidden", "lian-dong-she-zhi": "Linkage settings", "lie-biao": "list", "lie-index1": "column {0}", "lie-shu-xing": "column properties", "mo-ban-lie-biao": "Template list", "mu-biao-zi-duan": "target field", "parameterTip": "The input parameter information of the selected page model has been updated, please reconfigure and save", "qing-she-zhi-urldelete-shu-xing": "Please set the url.delete property!", "qing-shu-ru-biao-da-shi": "Please enter an expression", "qing-shu-ru-cha-xun-zhi": "Please enter query value", "qing-shu-ru-ying-yong-ming-cheng-cha-xun": "Please enter the application name to query", "qing-shu-ru-zui-da-chang-du": "Please enter a maximum length", "qing-shu-ru-zui-da-zheng-shu": "Please enter a max integer bit", "qing-shu-ru-zui-da-xiao-shu": "Please enter a max decimal bit", "qing-xuan-ze-yao-sheng-cheng-dai-ma-de-ying-yong": "Please select an app to generate code for!", "qing-xuan-ze-yao-zhuan-huan-de-mu-biao-zi-duan": "Please select the target field to convert", "qing-xuan-ze-zheng-ze-zhi": "Please select a regular value", "qu-jian": "interval", "qu-jian-xiao-yan": "interval check", "breadCrumbContent": "bread crumb content", "shu": "Tree", "suo-xuan-ye-mian-mo-xing-wei-she-zhi-ru-can-xin-xi": "The selected page model has no input parameter information set", "ti-shi-xin-xi": "Tips", "wei-yi-xiao-yan": "unique check", "excel-wei-yi-xiao-yan": "Excel unique check", "public-configuration": "Public configuration", "only_combination": "Only combination", "xiang-ying-qi-ta-mo-xing-lian-dong": "Respond linkages", "xiao-yan-gui-ze": "Check rules", "xuan-ze-bao-biao": "select report", "xuan-ze-biao-dan": "select form", "xuan-ze-lie-biao-huo-shu": "select list or tree", "ye-mian-lei-xing": "page type", "ye-qian-ming": "tab name", "yin-cang": "hide", "zan-bu-zhi-chi": "Not currently supported", "zheng-ze-xiao-yan": "Regular check", "shu-ju-zi-dian-jiao-yan": "Data dictionary check", "zheng-ze-zhi": "Regular value", "pcode-xiao-yan": "pCode check", "pcode-ru-ku": "pCode warehouse", "zhi-du": "read only", "zhuan-huan-gui-ze": "Conversion rules", "zi-ding-yi-groovy-jiao-ben": "Custom Groovy Script", "zi-ding-yi-lei": "custom class", "zi-ding-yi-zhuan-huan-jiao-ben": "custom conversion script", "zi-ding-yi-zhuan-huan-lei": "custom conversion class", "zi-dong": "automatic", "zui-da-zhi": "maximum value", "zui-xiao-zhi": "minimum", "bao-biao": "report", "bian-ji-biao-dan": "edit form", "bian-ma-ming-cheng": "code/name", "biao-dan": "form", "biao-dan-ye-mian": "form page", "cha-kan-biao-dan": "View form", "cha-kan-shu-ju": "View data", "checkFks_error": "There is no master table record, please save or select the master table record first", "dao-ru-mo-ban": "import template", "dian-ji-huo-tuo-zhuai-wen-jian-dao-ru": "Click or drag files to import", "dian-ji-xia-zai": "click to download", "dui-hua-kuang": "dialog", "excelMaxTip": "Are you sure you want to export the data? \nTip: If you have selected data, only the selected data will be exported; if you have query conditions, only the data that meets the query conditions will be exported; the maximum export amount of data is {0}!", "gai-gong-neng-bu-zhi-chi-shi-yong-nei-bu-ye-qian-da-kai": "This function does not support opening with a new tab", "import_button_tip": "The button below can download the import template, and the imported data should meet the requirements of the import template", "ju-ti-xiang-qing-qing": "For details, please", "liu-cheng": "process", "liu-zhuan-zhong": "in circulation", "mei-you-pei-zhi-er-mo-xing": "No ER model configured", "mei-you-pei-zhi-guan-lian-ye-mian": "No associated page configured", "mei-you-tong-ji-shu-ju": "no stats", "no_form_no_view": "No associated forms and associated pages are configured", "qing-shu-ru-biao-ming-cheng": "Please enter a table name", "qing-shu-ru-sql-pei-zhi-bian-ma-huo-ming-cheng": "Please enter Sql configuration code or name", "qing-xuan-ze-shu-ju": "Please select data", "qing-xuan-ze-suo-shu-ying-yong": "Please select the application", "qing-zai-dang-qian-ye-xuan-ze-shu-ju": "Please select data on the current page", "ren-wu-zhuang-tai": "task status", "shu-ju-wu-xiao-pcurl-wei-pei-zhi": "Invalid data, pcUrl not configured", "sql-pei-zhi-bian-ma": "Sql Config Code", "sql-pei-zhi-ming-cheng": "Sql Config Name", "wei-qi-dong": "have not started", "wen-jian-xia-zai-shi-bai": "File download failed", "no-data": "no data to export!", "wen-zi-lai-yuan-yu-shu-ju-zi-duan": "The text comes from the data field", "wo-can-yu": "I participate", "wo-fa-qi": "I started", "xia-zai-dao-ru-mo-ban": "Download import template", "xin-zeng-biao-dan": "Add form", "xin-zeng-gen-jie-dian": "Add root node", "chooseDatasetType": "Choose Dataset Type", "xin-zeng-zi-jie-dian": "Add child node", "xuan-ze-biao": "selection table", "xuan-ze-er-mo-xing": "Choose an ER model", "xuan-ze-fu-wu-pei-zhi": "Select service configuration", "xuan-ze-shu-ju": "select data", "xuan-ze-shu-ju-ji": "Choose a dataset", "xuan-ze-shu-ju-zi-dian": "select data dictionary", "xuan-ze-sql-pei-zhi": "Select Sql configuration", "xuan-ze-ye-mian": "select page", "ye-mian": "popup page", "yi-jie-shu": "over", "yi-xuan": "selected", "zan-bu-zhi-chi-zi-biao-qian-tao-dan-chu": "Sub-table nested popup is not supported yet", "zan-shi-bu-zhi-chi-duo-xuan": "Multiple selection is currently not supported", "dragModel": "Drag the page model here", "bao-biao-she-ji": "report design", "bi-tian-xiao-yan-ti-shi-xin-xi": "Required verification prompt information", "biao-ming-cheng-bu-neng-bao-han-han-zi": "Table name cannot contain Chinese characters", "dan-hang-wen-ben": "single line text", "duo-hang-wen-ben": "multiline text", "er-mo-xing-biao-shi-bu-neng-bao-han-han-zi": "ER model logo cannot contain Chinese characters", "er-mo-xing-she-zhi-bu-he-fa": "ER model setting is invalid", "er-mo-xing-wei-she-zhi": "ER model not set", "fen-zu": "grouping", "field_max_length_msg": "Field length must be a number between 1-{0}", "gao-ji-mo-shi-jin-cha-xun": "Advanced mode (query only)", "groups-wei-kong": "groups is empty", "keys-wei-kong": "keys is empty", "kong-jian-wei-kong": "control is empty", "lei-xing-bu-he-fa": "invalid type", "mi-ma": "password", "mo-ren-zi-duan-bu-neng-shan-chu": "Default fields cannot be deleted", "nian-yue-ri": "year month day", "pu-tong-mo-shi": "normal mode", "qing-shu-ru-biao-miao-shu": "Please enter a table description", "qing-shu-ru-shu-ju-yuan": "Please enter a dataSource", "qing-shu-ru-cha-xun-top": "Please enter query top", "qing-shu-ru-er-mo-xing-biao-shi": "Please enter the ER model ID", "qing-shu-ru-er-mo-xing-ming-cheng": "Please enter the ER model name", "qing-xuan-ze-biao-da-shi": "Please select an expression", "qing-xuan-ze-cha-xun-lei-xing": "Please select a query type", "qing-xuan-ze-chi-jiu-hua-fang-shi": "Please select a persistence method", "qing-xuan-ze-guo-lv-tiao-jian": "Please select a filter", "qing-xuan-ze-ji-suan-gong-shi": "Please select a calculation formula", "qing-xuan-ze-pai-xu": "Please select a sort", "qing-xuan-ze-shang-ji-cai-dan": "Please select the parent menu", "qing-xuan-ze-zi-duan": "Please select a field", "shen-fen-zheng-hao": "identity number", "shu-ju": "data", "shu-ju-guo-lv": "Data filtering", "shu-ju-ji-wei-kong": "dataset is empty", "shu-ru-kuang-lei-xing": "input box type", "shu-zi": "number", "values-wei-kong": "values ​​is empty", "wan-zheng-shi-jian": "full time", "xiao-shu-dian-wei-bi-xu-wei-030-zhi-jian-de-shu-zi": "The decimal point must be a number between 0-30", "xiao-shu-dian-wei-bu-neng-da-yu-zi-duan-chang-du": "The decimal point cannot be greater than the field length", "yong-yu-she-zhi-lie-biao-jian-de-lian-dong-guan-xi": "Used to set the linkage relationship between lists", "yu-lan-fei-shi-ji-xiao-guo": "Preview non-real effects", "zheng-shu": "integer", "zhi-biao": "Index", "zi-duan": "field", "zi-duan-lei-xing-bu-neng-wei-kong": "Field type cannot be null", "zi-duan-miao-shu-bu-neng-wei-kong": "Field description cannot be empty", "zi-duan-ming-cheng-bu-neng-wei-kong": "Field name cannot be empty", "zi-duan-ming-cheng-bu-neng-zhong-fu": "Field names cannot be repeated", "zi-duan-ming-wei-bao-liu-zi": "field name reserved word", "cha-xun-kuang": "Query box", "percent-model": "show percent", "hide-xAxis": "Whether to hide the x-axis", "gao-du2": "high:", "guan-lian-bao-biao": "Link report:", "guan-lian-lie-biao": "Link view:", "guan-lian-zi-duan": "Link field:", "ju-shang": "top", "ju-xia": "down", "ju-zhong": "Centered", "dataease": "is Bi system", "kuan-du2": "Width:", "kuan-gao": "Width height", "nei-rong2": "content:", "qing-shu-ru-biao-ti-mo-ren-wei-zu-jian-ming": "Please enter a title, the default is the component name", "qing-shu-ru-gao-du": "Please enter height", "qing-shu-ru-kuan-du": "Please enter width", "qing-xuan-ze-guan-lian-bao-biao": "Please select a related report", "qing-xuan-ze-guan-lian-lie-biao": "Please select an associated list", "qing-xuan-ze-guan-lian-zi-duan": "Please select an associated field", "qing-xuan-ze-shu-chu-zi-duan": "Please select an output field", "qing-xuan-ze-wei-zhi": "Please select a location", "qu-jian2": "Interval:", "qu-zhi-qu-jian": "Value range", "shang-you": "top-right", "shang-zhong": "On the", "shang-zuo": "top-left", "shu-chu-zi-duan": "Output field:", "tu-biao-lian-dong": "Chart linkage", "tu-li": "Legend", "wei-zhi": "Location:", "xia-you": "down-right", "xia-zhong": "lower-middle", "xia-zuo": "down-left", "xian-shi": "show", "you-shang": "right-up", "you-xia": "right-down", "you-zhong": "right-middle", "yu-bao-biao-lian-dong": "Link report", "yu-lie-biao-lian-dong": "Link view", "zi-shi-ying": "adaptive", "zui-da-zhi2": "Max:", "zui-xiao-zhi2": "Minimum:", "zuo-shang": "left-up", "zuo-xia": "left-down", "zuo-zhong": "left-center", "current_app": "Current", "common_app": "Common", "bai-fen-bi": "percentage", "bu-neng-bian-ji": "cannot be edited", "cha-ru-ye-qian": "Insert tab", "dang-qian-ye-qian": "current tab", "jue-dui-zhi": "absolute value", "qing-kong-dang-qian-ye-qian": "Clear current tab", "shan-chu-ye-qian": "Delete tab", "she-ji-qu-yu-hua-fen-wei-24-deng-fen": "The design area is divided into 24 equal parts", "she-zhi-gao-du-de-fang-shi-quan-ju": "How to set height (global)", "ye-qian-cao-zuo": "Tab operations", "ye-qian-ming-cheng": "tab name", "ye-qian-quan-xian-bian-ma": "tab permission code", "zu-jian-shu-xing": "component properties", "fixed-label-width": "fixed label width", "enable-fixed-label-width": "Enable fixed label width", "label-width": "label width (px)", "zhu-biao-dan": "Main form", "zhu-biao-dan-y": "yes", "zhu-biao-dan-n": "no", "align-type": "align type", "stepsTitles": "steps titles", "stepsHeight": "step height", "button-show-type": "button show type", "button-prompt": "button prompt", "button-setting-level": "Button Set Level", "button-setting-authority": "Authority Code", "model-flag": "module identification field", "interface-url": "Interface Url", "button-show-line-one": "line one", "button-show-line-more": "line more", "xia-la-an-niu-xiang": "dropdown button item", "chu-fa-xia-la-xing-wei": "dropdown button trigger", "sider-position": "sider position", "sider-collapsed": "sider collapsed", "sider-width": "sider width", "sider-collapsed-width": "sider collapsed width", "height-type": "height show type", "tabButtons": "tab buttons", "addButtons": "add button", "lrContainer-type": "grid type", "lrContainer-left-cols": "left cols", "lrContainer-right-cols": "right cols", "lrContainer-flex-type": "flex type", "lrContainer-flex-width": "flex width", "tab-position": "tab position", "fei-zhu-zi-biao": "Non primary sub table Tab", "custom-zhu-biao-id": "Custom main table id column name", "ye-qian-xian-yin-jiao-ben": "Tab hiding rule script", "ye-qian-xian-yin": "Tab hiding rule settings", "ye-qian-xian-yin-title": "Tab hiding rule settings(Takes effect when no script is configured!)", "custom-unfold": "Custom unfold(The priority is lower greater than the default value!)", "operation-fields": "Operation and maintenance configuration item fields (customs: gwOperationConfig.xxx, others can be modified as needed)", "mo-ren-guan-bi": "close（default）", "zhu-biao-form-id": "The main form ID maps to this component ID", "arrangeType": "Arrange type", "function": "function", "import": "async import", "default-setting": "default setting", "default-type": "default type", "qing-shu-ru-zheng-zheng-shu-pai-xu-shu-zi": "Please input positive integer", "front-button": "front button", "temporary": "temporary", "temporary_confirm": "Confirm temporary storage?", "edit_table": "edit table setting", "manual": "<PERSON><PERSON> triggered editing", "dblclickCell": "Cell Double Click Edit", "not-show-prompt": "No prompt is displayed", "custom_prompt": "Custom prompts"}, "widgetTypeList": {"Column": "Column", "GroupedColumn": "GroupCol", "StackedColumn": "StackCol", "PercentStackedColumn": "PercentCol", "Radar": "Radar", "Line": "Line", "GroupedLine": "GroupLine", "Area": "Area", "StackedArea": "StackArea", "Bar": "Bar", "StackedBar": "StackBar", "PercentStackedBar": "PercentBar", "GroupedBar": "GroupBar", "Pie": "Pie", "Donut": "Donut", "Funnel": "Funnel", "Gauge": "Gauge", "MeterGauge": "MeterGauge"}, "independent_form": {"scene": "Independent form scene", "form_operation": "Form operation type", "loading_data": "Loading data scene", "add_query": "Add query criteria", "query": "Query criteria", "select_condition_field": "Please select a condition field", "context": "Context", "fixed": "Fixed", "address": "Address", "select_field_value": "Please select field value", "input_field_value": "Please input field value", "input_stores_field_name": "Please input stores field value", "input_field_name": "Please input field name", "scene_query": "With query criteria to match table", "stores": "stores"}}, "localeProvider": {"label": {"randomCodeEvent": "Random code", "select_title": "Multi-language Label Select", "other": "Other", "switchStatus": "Edit/Select", "unchanged": "Data unchanged", "exist": "the company locale is already existed", "default_tag": "Common label", "locale_tag": "Locale label", "locale_tag_ok": "Current language normal", "locale_tag_error": "Current language is abnormal"}}, "eformConstant": {"validRule": {"email": "Email", "email_message": "Please enter a valid email address", "phonenumber": "Phone Number", "phonenumber_message": "Please enter the correct mobile phone number", "number": "Number", "number_message": "You can only enter numbers", "variable": "Letters or underscores", "variable_message": "Only letters and underscores", "fields": "Start With A Letter,The longest 18,Only letters, digits, and underscores", "fields_message": "Start With A Letter,The longest 18,Only letters、digits、underscores", "url": "Url", "url_message": "Please enter a valid url", "chinese": "Chinese", "letter": "Letter", "special": "Special characters", "chinese_message": "Please type Chinese characters", "qq": "QQ", "qq_message": "Please enter the correct QQ number", "varirule": "Start With A Letter", "varirule_message": "Can only start with a letter,Only letters、digits、underscores", "digits": "Integer", "digits_message": "Please enter an integer", "digits2": "Positive Integer", "digits2_message": "Please enter a positive integer", "date": "Date", "date_message": "Please enter a date format", "time": "Time", "time_message": "Please enter a valid time", "zipcode": "Zip Code", "zipcode_message": "Please enter the zip code", "size_255": "The length cannot exceed 255 bytes (2 bytes of a Chinese character)!", "size_20": "The length cannot exceed 20 bytes (2 bytes of a Chinese character)!", "numbersOrLetters": "numbers or letters", "numbersOrLetters_message": "Please enter a valid numbers or letters", "randomCombination": "Any combination of Chinese characters, letters, numbers, and special characters"}, "showType": {"hidden": "hidden", "input": "input", "textarea": "textarea", "number": "number", "select": "select", "checkbox": "checkbox", "radio": "radio", "switch": "switch", "date": "date", "time": "time", "rate": "rate", "slider": "slider", "serialno": "serialno", "editor": "editor", "icon": "icon", "uploadFile": "uploadFile", "uploadFileOutGoing": "uploadFileOutGoing", "uploadImg": "uploadImg", "user": "user", "dept": "dept", "page": "popup", "companyUser": "document maker", "editableTable": "edit table", "excelTable": "excel table", "button": "button", "showColumn": "Set Hidden", "showColumnSetting": "Set Hidden Columns Setting", "fixedField": "Is Fixed Field", "gwSetting": "Maintain", "expandColumn": "Expand Column Setting", "expandViewCode": "Corresponding list page", "gwSettingSyncError": "Synchronization of field configuration information to the customs system failed due to:", "isGwSetting": "Is Maintenance Settings Field", "synchronizeData": "Whether to synchronize data", "synchronizeUrl": "Please enter the synchronization URL", "buttonList": "Button List", "alert": "alert", "text": "text", "divider": "divider", "card": "card", "grid": "grid", "ybz": "YBZ", "calculateTotal": "Unit Price", "unitPrice": "Unit Price", "quantity": "quantity", "total": "total", "table": "table", "tabsWitDragAndDrop": "Tab+", "tabs": "tabs", "verticalTabs": "vertical", "sider": "sider", "collapses": "collapses", "dropdownButton": "dropdown", "lrContainer": "left right container", "hoContainer": "horizontal container", "stepsContainer": "steps", "horizontalContainer": "horizontal", "customComponent": "Custom component", "pleaseEntryStepsTitle": "please entry steps title", "declaration": "declaration", "net_weight": "Net weight", "scale_factor": "Scale factor", "scale_factor_1": "Legal unit1 Scale factor", "scale_factor_2": "Legal unit2 Scale factor", "scale_factor_erp": "ERP Scale factor", "erp_unit": "ERP unit", "batch_text": "Batch text box", "assemble_control": "assemble control", "good_specs": "Good specs", "good_property": "property", "tree_control": "tree", "workflow": "Work flow design", "graph": "business flow"}, "subTableFieldType": {"input": "text", "datetime": "datetime", "normal": "normal", "file": "file", "action": "action"}, "colType": {"varchar": "<PERSON><PERSON><PERSON>", "clob": "clob", "blob": "blob", "number": "number", "date": "date"}, "isSync": {"isSync0": "before create", "isSync1": "created", "isSync2": "before sync", "isSync3": "sync"}, "yesNo_text": {"yes": "yes", "no": "no"}, "paramsRowNum": {"two": "2", "three": "3", "four": "4"}, "radioType_text": {"checkbox": "checkbox", "radio": "radio", "none": "none"}, "persistenceType": {"db": "db", "http": "http"}, "objStructure": {"main": "main", "oneToOne": "oneToOne", "oneToMany": "oneToMany"}, "appType": {"pc": "pc", "mobile": "mobile"}, "groupType": {"group": "group", "app": "app"}, "tempType": {"form": "form", "view": "view", "module": "module"}, "status": {"enable": "enable", "disabled": "disabled"}, "alignType": {"center": "center", "left": "left", "right": "right", "default": "default"}, "queryOp": {"eq": "equals", "eic": "equals ignoring case", "lt": "less", "gt": "greater", "le": "less or equals", "ge": "greater or equals", "ne": "not equals", "lk": "like", "lfk": "left like", "rhk": "right like", "in": "in", "ni": "not in", "bt": "between", "custom": "custom", "inl": "is null", "nnl": "not null"}, "buttonType": {"toolbar": "toolbar", "inline": "inline"}, "flowScope": {"noLimit": "noLimit", "my": "myFlow", "all": "all", "myTask": "myTask"}, "viewTypeAll": {"layout": "layout", "dragLayout": "dragLayout", "table": "table", "tree": "tree", "mobile": "mobile"}, "tuoMin": {"none": "none", "email": "email", "phonenumber": "phonenumber", "idcard": "idcard", "address": "address", "bankcard": "bankcard", "ext": "ext"}, "fixed": {"none": "none", "left": "left", "right": "right"}, "openTypeForSubTable": {"handleClickAdd": "handleClickAdd", "handleAddByPage": "handleAddByPage", "handleAddByForm": "handleAddByForm", "handleExt": "handleExt"}, "dialogSize": {"auto": "auto", "default": "default", "full": "full dialog", "large": "large dialog", "middle": "middle dialog", "small": "small dialog", "mini": "mini dialog", "tab": "new tab", "tabInCurrent": "inside tab", "largeDrawer": "large drawer", "middleDrawer": "middle drawer", "smallDrawer": "small drawer", "withinTab": "show under the tab"}, "heightType": {"full": "full", "auto": "auto", "fixed": "fixed"}, "batchText": {"placeholder": "Support batch query of spaces, carriage return, or English comma or EXCEL copy and paste format directly"}}}, "bpm": {"common": {"flow_history": "Flow History", "operate_log": "Operate Log", "approval_opinion": "Approval Opinion", "business_handler": "Assignee", "bpmStatus": "Process Status", "bpmActivity": "Current Step", "bpmDetail": "Process Detail", "bpmPage": "Process page"}, "entity": {"group": "Process Group", "name": "Template name", "key": "Template Code", "version": "Version", "status": "Template Status", "processDefName": "Process Template", "businessTitle": "Instance Title", "processInstanceState": "Process State", "startTimeString": "Start Time", "endTimeString": "End Time", "durationString": "Duration", "startUserName": "Start User", "taskTitle": "Task Title", "taskName": "Node Name", "taskType": "Task Type", "fromUserName": "Sender", "assigneeName": "Assignee Name", "readerName": "Reader", "sendTimeString": "Send Time", "dueDateString": "Due Time", "myInstanceClassify": "Process State", "varName": "Variable Name", "varValue": "Variable Value", "varType": "Variable Type", "varCreateTime": "Create Time", "operationTypeString": "Processing Type", "message": "Opinion", "passName": "Circulate Name", "passType": "Circulate Type", "passTime": "Circulate Time", "passMessage": "Opinion", "delegateBailee": "Bailee", "delegateResource": "Delegate Resource", "delegateResourceName": "Name"}, "verify": {"confirm_delete_template": "Are you sure to delete the template", "confirm_publish_template": "Are you sure about the publish template", "delete_tip": " Only templates with no instance data can be deleted", "select_show": "Please select the column to display", "select_show_all": "Please complete the configuration of all fences", "confirm_suspend": "Are you sure to suspend the process", "confirm_activate": "Are you sure to activate the process", "tip_batchDeal1": "Please select the task in batches", "tip_batchDeal2": "Cannot select to read, please filter the query conditions and then batch processing", "tip_batchDeal3": "Batch processing Can be performed for multiple tasks under the same approval node in a single process template at a time. You can perform batch processing by filtering query conditions", "tip_batchDeal4": "There are {0} records that meet the conditions of batch processing together, and the other ones that do not meet have been automatically unchecked for you. Please click batch processing again after confirming", "tip_batchPass1": "Please select the batch to be read", "tip_batchPass2": "You can only select to read, please filter the selection through the query conditions before batch read", "tip_deleteMyProcess": "Only processes identified as started by me and not submitted can be deleted", "tip_collectProcess": "Collection process successful", "tip_unCollectProcess": "Cancel collection process successful", "confirm_cancelDelegate": "Do you want to undelegate the selected task? Tip: Delegate can be cancelled only for tasks of type todo", "tip_no_running_task": "No running tasks available", "tip_no_task": "No tasks available", "tip_select_current_task": "Please select the current operation node", "tip_select_target_node": "Please select the target node", "tip_trusteeid_empty": "Trustee ID is empty", "tip_delegation_range_empty": "Delegation range is empty", "tip_switch_tasks": "Switch tasks", "tip_select_transition": "Please select a transition", "tip_select_process_template": "Please select a process template", "tip_assign_user": "Assign user", "tip_start_sucess": "Start successful", "tip_no_transition": "No transition", "tip_no_permission": "No permission", "tip_current_task_delegated": "The current task has been delegated by", "tip_assignor": "assignor", "tip_process_suspended": "Process suspended", "tip_process_template_not_found_for_formcode": "The process template corresponding to formcode [{0}] is not found", "tip_blank_required_or_other_error": "There are blank required items or other errors, please check", "tip_relation_query_fail": "Relation tree query failed", "mei-you-quan-xian": "Permission denied!", "zu-zhi-ji-gou-cha-xun-shi-bai": "Organization query failed:", "qing-tian-xie-yi-jian": "Please fill in comments", "qing-xuan-ren": "please choose", "que-ren-ban-li-ma": "Are you sure to do it?", "user_select_error": "The selected users are all duplicates of existing users in the current process, please remove the duplicate data and continue"}, "analyze": {"processDefAnalyze": "Process Template Analyze", "taskAnalyze": "Task Analyze", "taskDueAnalyze": "DueTime Analyze", "processInstanceAnalyze": "Instances Analyze", "synAnalyzeData": "Manually Synchronize Data", "allProcessIntanceCount": "Start Process Count", "runningProcessInstanceCount": "Running Process Count", "completedProcessInstanceCount": "End Process Count", "processAveragetime": "Average Time", "taskCount": "Task Count", "startProcessTop": "Start Process Instances TOP10", "processInstanceCount": "Number of Process Instances", "taskAverageTimeTop": "The Average Processing Time TOP10", "averageTime": "Average Task Processing time", "uncompletedProcessTop": "Uncompleted Process Instances TOP10", "averageTimeTop": "The Average Processing Time  TOP10", "activityAveragetime": "Activity Average Time", "taskCompletedTop": "Completed Task TOP10", "taskCompletedCount": "Completed Task Count", "todoTaskTop": "Todo Task TOP10", "todoTaskCount": "Todo Task Count", "startTaskTop": "Start Process Instances TOP10", "dueTimeProcessTop": "Process Average Duetime  TOP10", "dueTimeActivityTop": "Activity Average Duetime  TOP10", "dueTimeTaskTop": "Task Average Duetime  TOP10", "dueTime": "Average Duetime", "taskDueCount": "Task Duetime Count", "taskAvgDuetime": "Task Average Duetime", "taskMaxDuetime": "Task Maximum Duetime", "processInstanceMaxDurationTop": "Max Duration Process Instance", "processInstanceOvertimeTop": "Max Duetime Process Instance"}, "design": {"idea_can_be_left_blank": "Can be left blank", "property": "Property", "press_type": "Press Type", "press_type_mail": "Mail", "press_type_sms": "Short Message", "press_type_blank_tips": "At least one urging method must be selected for Press", "passRound": "Circulate", "enable_add_passRound": "Enable Add Circulate", "other": "other", "enable_sendto_backnode": "Enable Send To Back Node", "specifyNode": "Specify Node", "specifyNodePlaceholder": "Select Node;Not selecting means not specify node;", "after_java": "After Processing Class", "after_java_placeholder": "Please enter the springbean name of the event class", "after_script": "After Processing Script", "after_script_button_name": "Custom Groovy Script", "default_name": "Default Name", "custom_name": "Custom Name", "method_name": "Method Name", "idea": "Opinion", "idea_submit_title": "Process Approval Opinion", "idea_pass_round_title": "Process Circulate Opinion", "idea_common": "Common Opinion", "idea_agree": "agree", "idea_disagree": "disagree", "idea_read": "have read", "idea_add_to_common": "Add Common Opinion", "idea_not_blank_warning": "No content in current input box", "idea_required_placeholder": "Please enter opinion", "icon": "Icon", "required": "required", "not_required": "Not Required", "due_date_pre_tips": "The task handling period is", "due_date_after_tips": "That is, the latest completion time is", "process_select_user": "Process Select User", "select_user_tab_name_user": "User", "select_user_tab_name_dept": "Dept", "select_user_tab_name_role": "Role", "select_user_tab_name_position": "Position", "select_user_tab_name_relation": "Relation", "relation_customclass_label": "Custom class", "relation_customclass_placeholder": "Please enter the springbean name of the custom implementation class", "relation_customclass_title": "Select User with Custom Class", "relation_variable_title": "Select User with process variable", "select_user_search_username_placeholder": "Please enter user name to query", "select_user_selected": "Selected", "variable": "Variable", "variable_select": "Select", "variable_source": "Source", "custom_column": "Custom column", "mapping_value": "Mapping value", "variable_source_eform": "Electronic form", "variable_srouce_custom": "Custom", "variable_add": "Add Variable", "variable_expression": "expression", "variable_expression_placeholder": "Please enter an expression", "variable_expression_available": "Available expressions", "variable_expression_userId": "Login ID", "variable_expression_userName": "Login name", "variable_expression_deptId": "Login Department ID", "variable_expression_deptName": "Login Department Name", "variable_expression_deptShortName": "Login Department abbreviation", "variable_expression_orgName": "Login enterprise name", "variable_expression_getUserName": "Transfer from user ID to name", "variable_expression_getDeptName": "Transfer from department ID to name", "variable_expression_getDeptShortName": "Transfer department ID to abbreviation", "variable_expression_getOrgName": "Transfer from enterprise ID to name", "variable_expression_formatDateStr": "Format time", "time_set": "time setting", "process_flow_steps": "Process flow steps", "auto_del_fields_msg": "Automatically exclude non-editable but required fields from configuration list", "bi-tian-quan-xuan-quan-qu-xiao": "Required (select all/cancel all)", "bi-tian-zi-duan": "Required fields:", "ke-bian-ji-quan-xuan-quan-qu-xiao": "Editable (select all/cancel all)", "ke-bian-ji-zi-duan": "Editable fields:", "shu-ju-biao-qian": "data labels", "shu-ju-zi-duan": "data field", "xin-zeng-shu-ju-zi-duan": "Add data fields"}, "other": {"process_design": "Process Design", "start_permission": "Start Permissions", "new_version": "New Version", "start_url": "StartPage URL", "publish_template": "Publish Template", "enable_template": "Enable  Template", "disable_template": "Disable Template", "add_role": "Add Role", "allow_all_role": "Allow All Role", "handleSuspend": "Suspend", "handleActivate": "Activate", "showVariable": "process variable", "showVariable_placeholder": "Please enter process variables", "showVariable_select_placeholder": "Please select process variable type", "bpmTrack": "Track", "showBpmTask": "Task User", "showBpmTaskReader": "Task Reader", "addAssignee": "Add Assignee", "subAssignee": "Sub Assignee", "addPassUser": "Add Circulate User", "subPassUser": "Sub Circulate User", "addReadUser": "Add Read User", "subReadUser": "Sub Read User", "processJump": "Process Jump", "batchDeal": "<PERSON><PERSON>", "batchPass": "Batch Pass", "option_todo": "Todo Tasks", "option_pass": "Pass Tasks", "option_todoed": "Handled Tasks", "option_passed": "Passed Tasks", "placeholder_searchByName": "Please enter a process name", "label_frequentProcess": "Frequent Process", "cancelDelegate": "Cancel Delegate", "delegate_type1": "My Delegate", "delegate_type2": "I To Someone Delegate", "delegate_type3": "Someone To Me Delegate", "delegate_set_title": "Delegate Set", "my_delegated_tasks_title": "My delegated tasks", "process_dialog": "Process Dialog", "custom_button": "Custom Button", "fa-qi": "started", "qing-shu-ru-jiao-se-ming-cheng-cha-xun": "Please enter the role name query", "qing-xuan-ze-mo-ban-fen-lei": "Please select a template category", "xuan-ze-liu-cheng-mo-ban": "Choose a process template", "xia-yi-bu": " Next Step "}, "bpmConstant": {"processDefStatus": {"design": "design", "active": "active", "suspended": "suspended"}, "processInstanceState": {"ACTIVE": "active", "COMPLETED": "completed", "SUSPENDED": "suspended"}, "myInstanceClassify": {"start": "start", "participate": "participate"}}}, "workflow": {"label": {"processEnded": "process ended", "processInitiator": "process initiator", "assignType": "assign type", "checker": "checker", "pleaseSelect": "please select"}, "title": {"approverSettings": "approver settings"}}, "fm": {"components": {"portletPage": {"title": "Portal", "layoutTemp": "Template", "grid": {"grid1": "Template-1", "grid255": "Template-2"}}}, "description": {"containerEmpty": "You can drag and drop the item from the left to add components"}, "actions": {"deletPort": "Delete portal"}, "config": {"widget": {"name": "Name", "title": "Component Attribute", "width": "<PERSON><PERSON><PERSON>", "height": "Height", "titleRequired": "show Title", "widthtooltip": "The design area is divided into 12 equal sections", "pageUrl": "Page url"}}}, "codegen": {"label": {"data_model_selection": "Data model selection", "form_page_config": "Form and page configuration", "infor_config_code_gen": "Infor config and code generation", "attribute_selection": "Attribute Selection", "with_accessories": "With Accessories", "layout_upDown": "Up And Down", "layout_leftRight": "Left And Right ", "templateType_singleTable": "Single table", "templateType_singleTableFlow": "Single strap process", "templateType_mainSubTabTiling": "Primary sub table tab tiling", "templateType_mainSubTabTilingFlow": "Main sub table tab tiling process", "templateType_mainTilingSubTab": "Main table flat shop sub table tab", "templateType_mainTilingSubTabFlow": "Main table flat shop sub table tab with process", "templateType_mainTiling": "Main table tile", "templateType_mainTilingFlow": "Main table tiling process", "form_line_control_num": "Number of controls per line of the form", "module_infor_config": "Module information configuration", "back_module_config": "Backend module configuration", "javaPackage": "Java code package path", "genFile": "Generate file", "genFile_java": "Back end code", "genFile_web": "Front end code", "front_module_config": "Front end module configuration", "front_vue_path": "Front end Vue path"}, "button": {"previous_step": "Previous Step", "next_step": "Next Step", "genCode": "Generate Code", "genSpringbootProject": "Generate Backend Project", "select_er_model": "Select ER Model"}, "verify": {"templateTypeValid": "Please select a template type"}, "entity": {"tableName": "Table Name", "moduleDesc": "Description", "moduleDesc_placeholder": "Please enter the function description", "templateType": "Template Type", "withSubTable": "Associated Sub Table", "mainManangerLayout": "Page Layout", "codeCreationDate": "Create Time", "formColNewRow": "Single Row", "listIsShow": "Show in list", "packagePath": "Base package path", "packagePath_placeholder": "Please enter the base package path", "moduleName": "Component name", "moduleName_placeholder": "Please enter the component name", "authorName": "author", "authorName_placeholder": "Please enter the author", "authorEmail_placeholder": "Please enter email"}}, "portalDesign": {"actions": {"deletPort": "delete this portal"}, "components": {"portletPage": {"grid": {"grid1": "Template one", "grid255": "Template two"}, "layoutTemp": "template page", "title": "portal page"}}, "config": {"widget": {"height": "high", "name": "title", "pageUrl": "small page path", "title": "Control properties", "titleRequired": "show title", "width": "width", "widthtooltip": "The design area is divided into 12 equal parts"}}, "description": {"bao-cun-shi-bai": "Failed to save", "containerEmpty": "drag the page here", "fu-zhi-xiao-ye": "Copy small pages", "jia-zai-shi-bai": "Failed to load", "que-ren-shan-chu-ci-men-hu-ma": "Are you sure you want to delete this portal?", "shan-chu-xiao-ye": "delete small pages", "she-zhi": "set up", "tuo-dong-xiao-ye": "drag page"}}, "warn": {"warnrule": {"entity": {"warningItemId": "Warning item", "ruleCode": "Rule identification", "ruleName": "Rule Name", "dispatchFrequency": "Scheduling frequency", "cronExpr": "cron expression", "createBy": "founder", "createTime": "Creation time", "resultPageLoadScript": "Loading script for warning result page"}, "verify": {"warningItemId": "Warning item cannot be empty", "ruleCode": "Rule identifier cannot be empty", "ruleName": "Rule name cannot be empty", "dispatchFrequency": "The scheduling frequency cannot be empty", "cronExpr": "cron expression cannot be empty"}, "other": {"button-set-warn-condition": "Warning conditions", "busi-design": "business design", "basic_info": "Basic information", "table_design": "table design", "query_config": "query criteria", "busi_config": "service configuration", "warn-item-rule-null": "The corresponding warning rule for the warning item is empty!", "parse_sql_err": "Unable to find the corresponding entity model for the table, parsing failed, please manually configure!", "before_script_help": "Write a stored procedure and replace input parameters with placeholders (replace all with strings, set null if the match is empty)，Such as call p_procedure('xx',null)。", "warn_script_help": "Only supports querying SQL mode, try to avoid using select * to query all fields, and configure the required fields as needed.", "script_placeholder_help": "In the new mode, display placeholders corresponding to enterprise tenants; Add warning condition placeholders in editing mode.", "parse_sql_help": "The fields to be queried should avoid direct parsing using subquery AS aliases or complex function (field) AS aliases. Complex fields' 'as aliases can be replaced after successful parsing.", "forbid_repeat_click": "Please do not click again!", "tab_col_sync": "Tab column synchronization", "condition_placeholder": "Condition placeholder", "before_placeholder": "Prefix placeholder", "warn_placeholder": "Warning placeholder", "after_placeholder": "Post placeholder", "warn_condition": "Warning conditions", "procedure_warn": "Please enter the stored procedure", "warn_item_type": "Warning item type", "disable_condition_set": "Business triggered rules do not involve warning conditions！", "col_code_define_tip": "Attention：The fields(warn_status/warnStatus) are occupied by the system. Please enter another field", "result_page_load_script_help": "Support placeholder format replacement, including enterprise tenant context and alert conditions.Supports two usage scenarios:Warning condition description[setConditionSetDesc(html)],Text prompt in front of the result page table[setTableButtonRightContent(html,width)]."}}, "interfacetablecol": {"entity": {"colCode": "Notification field identification", "colName": "Notification field name"}, "verify": {}, "other": {}}, "sqlconfig": {"entity": {"beforeDsCode": "Pre data source", "warnDsCode": "Warning data source", "afterDsCode": "Post data source", "beforeSql": "Pre script", "warnSql": "Warning script", "afterSql": "Post script"}, "verify": {}, "other": {}}, "setconfig": {"entity": {"warnField": "Warning field", "operateType": "Operation type", "warnFieldValue": "Warning field value", "warnFieldMin": "Minimum value of warning field", "warnFieldMax": "Maximum value of warning field", "fieldPrecision": "accuracy"}, "verify": {"rule-customize-col-uninitialized": "The customized warning field for warning rules has not been initialized!"}, "other": {"warnSet": "Warning settings", "delete-row-confirm": "This operation affects the initialized enterprise configuration information. Are you sure to delete it?", "confirm-yes": "correct", "confirm-no": "deny"}}, "warnresult": {"entity": {"warningItemName": "Warning item name", "warningItemCode": "Warning item identification", "logTotal": "Number of log records", "recentLogTime": "Recent logging time", "recentTaskTime": "Recent warning task time"}, "verify": {"rule-customize-col-uninitialized": "The customized warning field for warning rules has not been initialized!"}, "other": {"warnSet": "Customize warning settings", "recent-warn-result": "Recent warning results", "email-send-logs": "Email sending log", "site-email-send-logs": "Internal message sending log", "warn-result-export": "WarnResultExport", "relieve-warn": "Relieve <PERSON>", "recover-warn": "Recover <PERSON>", "warn-status": "Status"}}, "warntask": {"entity": {"sendStatus": "Send Status", "updateTime": "Sending time", "recipientEmailAddress": "Recipient's email address", "duplicateSenderEmailAddress": "CC recipient's email address", "duration": "Display time (seconds)", "warnType": "Notice type", "userNoList": "notifier", "subject": "subject", "content": "content", "errMsg": "Reason for sending failure"}, "verify": {}, "other": {"emailInfo": "mail message", "siteEmailInfo": "Internal message information", "button-email-resend": "Resend email", "button-site-email-resend": "Resend internal message", "warnResult": "Warning results"}}, "businessconfig": {"entity": {"isSubjectPlaceholder": "Is it a theme placeholder", "isContentPlaceholder": "Is it a placeholder for the main text", "isCustomWarn": "Is there a warning condition", "isBusiNotice": "Whether to configure business notifications", "busiNoticeType": "Business notification type", "busiNoticeConfig": "Business notification configuration", "isEmailCol": "Is it an email attachment column", "isBusiPk": "Warning release/recovery keywords", "isAfterKeywords": "Whether to post process keywords"}, "verify": {}, "other": {}}, "emailtemplate": {"entity": {"warningItemId": "Warning item", "emailTemplateCode": "Email Template Identification", "emailTemplateName": "Email Template Name", "emailSubject": "Email Subject", "emailContent": "Email Content", "resultAttachType": "Send the attachment of the warning result", "createBy": "founder", "createTime": "Creation time"}, "verify": {"warningItemId": "Warning item cannot be empty", "emailTemplateCode": "Email template identifier cannot be empty", "emailTemplateName": "Email template name cannot be empty", "emailSubject": "The email subject cannot be empty", "emailContent": "The email body cannot be empty", "resultAttachType": "The attachment for sending warning results cannot be empty", "placeholderMessage": "The placeholder content cannot be empty"}, "other": {"triggerMode": "Trigger Mode", "subjectBtn": "Theme Placeholder", "contentBtn": "Body placeholder", "selectPlaceholder": "Select placeholders", "placeholderMessage": "Placeholder content", "attachList": "Attachment List", "uploadNullFormId": "There is no main table record ID, please save and upload first", "warnResultAttachUploadTip": "Tip: Check 'Yes' and select the email attachment column based on the alert rule business configuration. Add the alert result as an Excel attachment to the email attachment.", "subjectTip": "Tip: The title bar supports placeholder replacement, and placeholder fields are generally fixed value fields. By default, only the field value in the first dataset is taken.", "contentTip": "Tip: The table in the main text supports both vertical and horizontal tables. The vertical table has a top and bottom structure, with titles placed in the first row and placeholders placed in the second row. The horizontal table has a left-right structure and only supports expansion to the right. The title column is placed in the first column, and placeholders are placed in the second column (the dataset only takes the first item)."}}, "siteemailtemplate": {"entity": {"siteEmailTemplateCode": "Internal message template identification", "siteEmailTemplateName": "Template name for internal message", "siteEmailSubject": "Internal message theme", "siteEmailContent": "Main text of internal message", "duration": "Display time (seconds)", "warnType": "Notice type"}, "verify": {"siteEmailTemplateCode": "The template identifier for internal messages cannot be empty", "siteEmailTemplateName": "The name of the internal message template cannot be empty", "siteEmailSubject": "The subject of the internal message cannot be empty", "siteEmailContent": "The main text of the internal message cannot be empty", "duration": "Display time (seconds) cannot be empty", "warnType": "Notification type cannot be empty"}, "other": {}}, "noticeconfig": {"entity": {"warningItemName": "Warning item name", "warningItemCode": "Warning item identification", "recipient": "recipient", "duplicateSender": "CC to", "businessField": "Business related word type", "businessValue": "Business related words"}, "verify": {"warningItemId": "Warning item cannot be empty", "recipient": "Recipient cannot be empty", "businessField": "Business related word type cannot be empty", "businessValue": "Business related words cannot be empty"}, "other": {"noticeUserManage": "Management of alert notification personnel", "warnNotice": "Warning notification", "busiRelaSet": "Business related word setting", "recipientName": "recipient", "duplicateSenderName": "CC to", "delete-row-confirm": "Are you sure to delete?", "confirm-yes": "correct", "confirm-no": "deny", "triggerMode": "Trigger Mode", "deleteItemConfirm": "Do you want to delete the selected data?"}}, "warnConstant": {"dispatchFrequency": {"midnight": "Midnight", "onepointam": "1 AM", "twopointam": "2 AM", "threepointam": "3 AM", "fourpointam": "4 AM", "fivepointam": "5 AM", "sixpointam": "6 AM", "sevenpointam": "7 AM", "eightpointam": "8 AM", "ninepointam": "9 AM", "tenpointam": "10 AM", "elevenpointam": "11 noon", "twelvepointam": "12 noon", "onepointpm": "1 PM", "twopointpm": "2 PM", "threepointpm": "3 PM", "fourpointpm": "4 PM", "fivepointpm": "5 PM", "sixpointpm": "6 PM", "sevenpointpm": "7 PM", "eightpointpm": "8 PM", "ninepointpm": "9 PM", "tenpointpm": "10 PM", "elevenpointpm": "11 PM"}, "operateType": {"greater_than": "greater than", "less_than": "less than", "equal": "equal to", "like": "be similar", "greater_than_equal": "greater than or equal", "less_than_equal": "less than or equal", "no_equal": "not equal to", "between": "maturity interval", "warn_before": "before expiration", "warn_after": "after expiration"}, "precision": {"day": "day", "hour": "hour"}, "warningItemType": {"common": "Common", "customize": "Customize"}, "taskSendStatus": {"waitSend": "To be sent", "sendSuccess": "Successfully sent", "sendFail": "fail in send"}, "triggerMode": {"platFormWarn": "Platform Warning", "businessTrigger": "Business trigger"}, "tradeInfo": {"currentCompCode": "Enterprise Code", "currentCompName": "Enterprise Name", "currentSocialCreditCode": "Corporate Social Credit Code", "currentTenantId": "Tenant ID", "currentTenantCode": "Tenant code", "currentTenantName": "Tenant Name"}, "warnStatus": {"warn": "<PERSON><PERSON>", "cancel": "Cancel"}}}, "other": {"login_please": "", "last_page": "This is the last page and cannot be closed anymore!"}, "importComponent": {"importIndex": {"select_file": "select file", "file_preview": "file preview", "import_status": "status", "start_row": "start row", "file_upload": "file upload", "import_type": "import type", "end_row": "end row", "download_template": "download template", "import_correct_data": "import correct data", "export_error_data": "export error data", "export_warn_data": "export warn data", "close": "close page", "last_import_time": "last import time: ", "field_setting_comment": "If the template cannot meet the requirements, please use custom field configuration", "enable_custom_field": "Enable custom field configuration", "custom_field": "field configuration", "require": "required", "title": "import", "error_msg": "- the content of the imported file is empty!", "error_msg1": "the uploaded file format is incorrect!", "error_msg2": "please select the file to import", "error_msg3": "the end row must be greater than the import start row", "error_msg4": "save error！", "success_msg": "save success！", "status_enum": {"00": "none", "A10": "ValidPre", "B10": "ValidIng", "B20": "ValidExp", "B30": "templateNotMatch", "C10": "ValidCorrect", "C20": "ValidWrongAll", "C30": "ValidWrong<PERSON>art", "C40": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "D10": "ImportPre", "E10": "ImportIng", "F10": "ImportExp", "G10": "ImportEnd"}, "import_type_enum": {"1": "add", "2": "update", "3": "delete", "4": "saveOrUpdate"}, "confirm": {"title": "Warning", "content": "Are you sure you want to import the correct data?", "okText": "ok", "cancelText": "cancel"}}, "templateTransfer": {"error_msg": "If the following values are invalid, they must be a combination of letters a~z and have a maximum length of 3 digits", "error_msg1": "Edit at least one column！", "title": "field configuration", "comment_title": "Rules for filling in input boxes：", "word": "word", "letter": "letter", "comment_title1": "Cell Content Filling Rules", "comment_content_letter": "Fill in the column name of the corresponding column in Excel in the cell, such as A", "comment_content_letter1": "The cell content must be a combination of English letters a-z, with a maximum length of 3 digits", "comment_content_word": "Fill in the column name of the corresponding column in Excel in the cell, such as the material name", "comment_content_word1": "The cell content can be filled with custom column names, with a maximum length of 50 digits"}, "common": {"export": "export", "back": "back", "preview": "Preview", "save": "save", "close": "close"}}}}