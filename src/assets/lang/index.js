import { createI18n } from 'vue-i18n';

import ViewUiEn from 'view-ui-plus/dist/locale/en-US';  /*view-ui-plus的英文文件*/
import ViewUiZh from 'view-ui-plus/dist/locale/zh-CN';  /*view-ui-plus的中文文件*/
import ViewUiTH from 'view-ui-plus/dist/locale/th-TH';  /*view-ui-plus的泰文文件*/

import zhCN_vxe from 'vxe-table/lib/locale/lang/zh-CN'
import enUS_vxe from 'vxe-table/lib/locale/lang/en-US'

import zhCN from './zh_CN.json'
import enUS from './en_US.json'
import thTH from './th_TH.json'

import dayjsCN from 'dayjs/locale/zh-cn'
import dayjsUS from 'dayjs/locale/es-us'
import dayjsTH from 'dayjs/locale/th'

import {LANGUAGE_CODE} from "@/store/mutation-types";

const messages = {
  zh_CN: {
    ...zhCN,
    ...zhCN_vxe,
    ...ViewUiZh,
    ...dayjsCN,
  },
  en_US: {
    ...enUS,
    ...enUS_vxe,
    ...ViewUiEn,
    ...dayjsUS,
  },
  th_TH: {
    ...thTH,
    ...enUS_vxe,
    ...ViewUiTH,
    ...dayjsTH,
  }
};
export function getLanguage() {
  if(window.$vueApp){
    return window.$vueApp.ls.get("LANGUAGE_CODE")
  }
  return "zh_CN"
}

const i18n = createI18n({
  legacy: false,
  locale: getLanguage(),
  globalInjection: true,
  messages
});

export const znI18n = createI18n({
  legacy: false,
  locale: 'zh_CN',
  globalInjection: true,
  messages
});

export const enI18n = createI18n({
  legacy: false,
  locale: 'en_US',
  globalInjection: true,
  messages
});

export const thI18n = createI18n({
  legacy: false,
  locale: 'th_TH',
  globalInjection: true,
  messages
});

export default i18n
