/* section样式  */
.dc-section {
  height: 100%;
  background: #fff;
  overflow: auto;
}

/* 单表tab页 操作栏需要进行吸附 */
.cs-action-tab {
  overflow: auto;
}
/* 主体区域 */
.cs-action {
  height: 100%;
  display: flex;
  flex-direction: column;

  .remove-table-border-add-bg{
    .surely-table-bordered .surely-table-cell:not(.surely-table-body-cell-range-single-cell):not( .surely-table-body-cell-range-selected) {
      border-right: none !important;
    }

    .blue-table-columns td {
      background-color: #5076f6;
      color: #ffffff;
    }
    .surely-table-row-hover  {
      background-color: var(--surely-table-row-selected-bg) !important;
    }
    .surely-table-row.surely-table-row-selected ,.tr-selected{
      background-color: #69c0ff !important;

    }
    .surely-table-row.surely-table-row-selected > .tr-selected {
      // 这里设置.tr-selected的样式，但基于你的描述，这可能不是你想要的
      // 如果.tr-selected只是另一个独立的类，并且你想要它也有透明背景，你应该直接在它上设置
      background-color: transparent !important; // 但注意，这不会和上面的逻辑冲突，除非它们应用于同一个元素
    }
    .surely-table-row-hover{
      background-color: #e6f7ff !important;
    }
    .surely-table-body .surely-table-body-cell-range-selected:not(.surely-table-body .surely-table-body-cell-inline-edit) {
      background-color: transparent !important;
      border: none;
    }
    .surely-table-cell > .surely-table-cell-edit-wrapper {
      border: none !important; /* 移除边框 */
    }
    .surely-table-body .surely-table-body-cell-range-selected.surely-table-body-cell-range-single-cell {
      border-color: transparent !important; /* 移除选中单元格的边框 */
    }
  }

  //.surely-table-row-selected {
  //  background-color: var(--surely-table-primary-color-3) !important;
  //
  //
  //}
  //
  ///* 当前css 效果下 移除 .surely-table-row-hover css  */
  //.surely-table-row-hover{
  //  background-color: var(--surely-table-primary-color-1)!important;
  //}

  /* 这个 tab吸附会失效 */
  //overflow: auto;
  /* 操作区域 */
  .cs-action-item {
    padding: 4px 0;
    margin: 2px 0;
    background: #fff;
    box-sizing: border-box; /* 确保 padding 不会撑大容器 */
    min-height: calc(100vh - 300px);
    height: auto;
    .surely-table-body{
       min-height: calc(100vh - 300px);
    }
  }
  .cs-action-item-inner {
    padding: 4px 0;
    margin: 2px 0;
    background: #fff;
    box-sizing: border-box; /* 确保 padding 不会撑大容器 */
    min-height: calc(100vh - 330px);
    height: auto;
    .surely-table-body{
      min-height: calc(100vh - 330px);
    }
  }
  .operation-container{
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: -2px;
  }

  /* 分页 */
  .cs-pagination {
    display: flex;
    justify-content:right;
    align-items: center;
    padding-right: 30px;
    background: #FFFFFF;
    height: 40px;
    line-height: 40px;
    width: 100%;
    .ant-pagination .ant-pagination-prev .ant-pagination-item-link, :where(.css-dev-only-do-not-override-zv1cxv).ant-pagination .ant-pagination-next .ant-pagination-item-link {
      display: block;
      width: 100%;
      height: 100%;
      font-size: 13px;
      text-align: center;
      background-color: transparent;
      border: 1px solid transparent;
      border-radius: 6px;
      outline: none;
      transition: all 0.2s;
    }

    .count-number{
      padding-top: 2px;
      align-items: center;
      font-size: 12px;
      display: inline-block;
      height: 32px;
      margin-inline-end: 8px;
      line-height: 30px;
      vertical-align: middle;
      margin-right: 6px;

    }

  }



  /* 设置表格高度 */
  .surely-table-header{
    height: 30px !important;
    .surely-table-center-viewport{
      .surely-table-header-container{
        .surely-table-cell {
          height: 30px!important;
        }
      }
    }
  }


  .surely-table-cell-edit-input:focus {
    border-radius: 4px;
    background: #fff;
    font-size: 12px;
    font-weight: 500;
    padding-left: 5px;
    /* background-color: transparent; */
  }

  /* 设置表格表头样式  */
  .surely-table-header-cell-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex: auto;
    max-width: 100%;
    font-weight: 600;
    font-size:12px;
  }



  /* 设置表格表体内容 */
  .surely-table-cell-content {
    font-size: 12px;
  }

  /* 选择样式 */
  .operation-container{
    .ant-btn-sm{
      font-size: 12px;
    }

  }

  /* ant 表格样式 */
  /* 修改table相关样式，使之可以自适应大小 */
  .surely-table-wrapper {
    position: relative;
    height: 100%;
    .surely-spin-nested-loading {
      height: 100%;
      .surely-spin-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        overflow: hidden;
        .ant-table {
          flex: 1;
          overflow: hidden;
        }
        .surely-table-container {
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          overflow: hidden;
          .surely-table-body {
            flex: 1;
            overflow: auto !important;
          }
        }
      }
    }
  }

  /* 编辑界面基础Tag样式 */
  /* 你可以在这里添加自定义样式 */
  .tag-card {
    position: relative;
    color: #000;
    font-weight: bold;
    padding: 20px 18px;
    border-radius: 4px;
    display: inline-block;
    font-size: 16px;
  }

  .tag-card::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 10px 10px 0 0;
    border-color: #e6f7ff transparent transparent transparent;
  }
}


/*  */
.cs-action-container  {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .remove-table-border-add-bg{
    .surely-table-bordered .surely-table-cell:not(.surely-table-body-cell-range-single-cell):not( .surely-table-body-cell-range-selected) {
      border-right: none !important;
    }

    .blue-table-columns td {
      background-color: #5076f6;
      color: #ffffff;
    }
    .surely-table-row-hover  {
      background-color: var(--surely-table-row-selected-bg) !important;
    }
    .surely-table-row.surely-table-row-selected ,.tr-selected{
      background-color: #69c0ff !important;

    }
    .surely-table-row.surely-table-row-selected > .tr-selected {
      // 这里设置.tr-selected的样式，但基于你的描述，这可能不是你想要的
      // 如果.tr-selected只是另一个独立的类，并且你想要它也有透明背景，你应该直接在它上设置
      background-color: transparent !important; // 但注意，这不会和上面的逻辑冲突，除非它们应用于同一个元素
    }
    .surely-table-row-hover{
      background-color: #e6f7ff !important;
    }
    .surely-table-body .surely-table-body-cell-range-selected:not(.surely-table-body .surely-table-body-cell-inline-edit) {
      background-color: transparent !important;
      border: none;
    }
    .surely-table-cell > .surely-table-cell-edit-wrapper {
      border: none !important; /* 移除边框 */
    }
    .surely-table-body .surely-table-body-cell-range-selected.surely-table-body-cell-range-single-cell {
      border-color: transparent !important; /* 移除选中单元格的边框 */
    }
  }

  //.surely-table-row-selected {
  //  background-color: var(--surely-table-primary-color-3) !important;
  //
  //
  //}
  //
  ///* 当前css 效果下 移除 .surely-table-row-hover css  */
  //.surely-table-row-hover{
  //  background-color: var(--surely-table-primary-color-1)!important;
  //}

  /* 这个 tab吸附会失效 */
  //overflow: auto;
  /* 操作区域 */
  .cs-action-item {
    padding: 4px 0;
    margin: 2px 0;
    background: #fff;
    box-sizing: border-box; /* 确保 padding 不会撑大容器 */
    min-height: calc(100vh - 300px);
    height: auto;
    .surely-table-body{
      min-height: calc(100vh - 300px);
    }
  }
  .table-pagination-container {
    flex: 1 1 0%;
    display: flex;
    flex-direction: column;
    min-height: 0;
    /*  overflow: hidden;*/
    .surely-table{
      height: 100%;
    }
  }
  .cs-action-table-item {
    padding: 4px 0;
    margin: 2px 0;
    background: #fff;
    box-sizing: border-box; /* 确保 padding 不会撑大容器 */
    //min-height: calc(100vh - 300px);
    height: auto;
    .surely-table-body{
      //min-height: calc(100vh - 300px);
    }
  }

  .cs-action-item-inner {
    padding: 4px 0;
    margin: 2px 0;
    background: #fff;
    box-sizing: border-box; /* 确保 padding 不会撑大容器 */
    min-height: calc(100vh - 330px);
    height: auto;
    .surely-table-body{
      min-height: calc(100vh - 330px);
    }
  }
  .operation-container{
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: -2px;
  }

  /* 分页 */
  .cs-pagination {
    display: flex;
    justify-content:right;
    align-items: center;
    padding-right: 30px;
    background: #FFFFFF;
    height: 40px;
    line-height: 40px;
    width: 100%;
    .ant-pagination .ant-pagination-prev .ant-pagination-item-link, :where(.css-dev-only-do-not-override-zv1cxv).ant-pagination .ant-pagination-next .ant-pagination-item-link {
      display: block;
      width: 100%;
      height: 100%;
      font-size: 13px;
      text-align: center;
      background-color: transparent;
      border: 1px solid transparent;
      border-radius: 6px;
      outline: none;
      transition: all 0.2s;
    }

    .count-number{
      padding-top: 2px;
      align-items: center;
      font-size: 12px;
      display: inline-block;
      height: 32px;
      margin-inline-end: 8px;
      line-height: 30px;
      vertical-align: middle;
      margin-right: 6px;

    }

  }



  /* 设置表格高度 */
  .surely-table-header{
    height: 30px !important;
    .surely-table-center-viewport{
      .surely-table-header-container{
        .surely-table-cell {
          height: 30px!important;
        }
      }
    }
  }


  .surely-table-cell-edit-input:focus {
    border-radius: 4px;
    background: #fff;
    font-size: 12px;
    font-weight: 500;
    padding-left: 5px;
    /* background-color: transparent; */
  }

  /* 设置表格表头样式  */
  .surely-table-header-cell-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex: auto;
    max-width: 100%;
    font-weight: 600;
    font-size:12px;
  }



  /* 设置表格表体内容 */
  .surely-table-cell-content {
    font-size: 12px;
  }

  /* 选择样式 */
  .operation-container{
    .ant-btn-sm{
      font-size: 12px;
    }

  }

  /* ant 表格样式 */
  /* 修改table相关样式，使之可以自适应大小 */
  .surely-table-wrapper {
    position: relative;
    height: 100%;
    .surely-spin-nested-loading {
      height: 100%;
      .surely-spin-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        overflow: hidden;
        .ant-table {
          flex: 1;
          overflow: hidden;
        }
        .surely-table-container {
          height: 100%;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          overflow: hidden;
          .surely-table-body {
            flex: 1;
            overflow: auto !important;
          }
        }
      }
    }
  }

  /* 编辑界面基础Tag样式 */
  /* 你可以在这里添加自定义样式 */
  .tag-card {
    position: relative;
    color: #000;
    font-weight: bold;
    padding: 20px 18px;
    border-radius: 4px;
    display: inline-block;
    font-size: 16px;
  }

  .tag-card::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 10px 10px 0 0;
    border-color: #e6f7ff transparent transparent transparent;
  }
}


/* 输入框黄色样式 */
.ant-input-covered-yellow{
  background-color: #faffbd;
}
/* 选择框黄色样式 */
.ant-select-selector-yellow {
  :where(.css-dev-only-do-not-override-nuidd8).ant-select-single.ant-select-sm:not(.ant-select-customize-input) .ant-select-selector {
    background-color: #faffbd !important;
  }
}
.ant-tabs .ant-tabs-content-top{
  height: 100%
}

.ant-tabs .ant-tabs-content{
  height: 100%
}
.ant-tabs {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  color: rgba(0, 0, 0, 0.88);
  font-size: 14px;
  line-height: 1.5714285714285714;
  list-style: none;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  display: flex;
}
/* 表头表体 tab页样式 */
.cs-action-tab {
  .ant-tabs-nav {
    margin: 0;
  }
  .ant-tabs-tab-btn{
    padding: 0  10px;
  }
  .cs-tab-icon{
    font-size: 16px;
    //position: absolute;
    right: 10px;
    top: 0;
    margin-right: 20px;
    z-index: 99;
    cursor: pointer;
  }
  .cs-tab-icon:hover{
    transform: scale(1.1);
  }
  .cs-tab{
    position: relative;
  }
}



/* 操作按钮区域（例如上面的新增 删除 编辑等等） */
.cs-action-btn {
  /*固定*/
  z-index: 98;
  height: 40px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: left;
  padding: 0 10px;
  width: 100%;
  position: relative;
  .cs-action-btn-item {
    display: block;
    pointer-events: auto;
    cursor: pointer;

    .ant-btn-default {
      background-color: #ffffff;
      border-color: #d9d9d9;
      box-shadow: none;
    }

    & > .ant-btn {
      margin-top: 5px;
      padding-left: 4px;
      border: 0;
      font-weight: 800;
      margin-left: 10px;
      font-size: 12px;
    }

    & > .ant-btn:hover {
      color: #000;
      background: #eaeaea;
    }
  }
  .cs-action-btn-settings{
    position: absolute;
    padding-top: 5px;
    right: 0;
    margin-right: 20px;
  }
}


/* 查询条件区域样式 */
.cs-search {
  :where(.css-dev-only-do-not-override-nuidd8).ant-card .ant-card-body {
    padding: 0px;
    border-radius: 0 0 8px 8px;
  }
  padding: 0;
  margin-right: 2px;
  .custom-search-buttons {
    height: auto;
    padding-right: 0;
  }
  // 增加一个额外的父级选择器以提高优先级
  & > .ant-card > .ant-card-body {
    padding: 0 !important;
  }

  .search-btn {
    height: 30px;
    line-height: 30px;
    display: flex;
    justify-content: right;
    align-items: center;
    box-shadow: none;

    .cs-margin-right {
      margin-right: 10px;
      color: #fff;
      background-color: #1890ff;
      box-shadow: 0 2px 0 rgba(5, 175, 255, 0.1);
      border-radius: 3px;
      line-height: 1.4;
      height: 24px;
      padding: 0 7px ;
      font-size: 12px;
      span {
        margin-left: 2px;
      }
    }
    .cs-refresh{

      padding-right: 24px;
    }
    .cs-margin-right:hover  {
      background-color:#69b1ff;
    }

    .cs-warning {
      background: rgb(185, 204, 79);
      padding-right: 24px;
    }

    .cs-warning:hover {
      background: rgb(185, 204, 79);
    }
  }
}


/* 分割线 */
.separateLine {
  height: 2px;
  border-bottom: 1px dashed rgba(206, 200, 200, 0.99);
}



//.ant-select-item-option-content{
//  font-size: 12px !important;
//  display: flex;
//}
//.ant-select-item {
//  position: relative;
//  display: block;
//  min-height: 30px;
//  padding: 5px 12px;
//  color: rgba(0, 0, 0, 0.88);
//  font-weight: normal;
//  font-size: 14px;
//  line-height: 1.5714285714285714;
//  box-sizing: border-box;
//  cursor: pointer;
//  border-radius: 4px;
//  .ant-select-item-option {
//
//  }
//}

/* 表单样式 */
.cs-form {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  grid-column-gap: 2px;


  .ant-input {
    font-size: 12px;
    padding: 0px 7px;
    border-radius: 3px;
    height: 24px;
    //color: #021726;
  }

  .ant-picker .ant-picker-input >input{
    font-size: 14px !important;
  }
  /* 导入禁用样式 */
  .ant-input-disabled {
    color: rgba(0, 0, 0, 0.25) !important;
    background-color: rgba(0, 0, 0, 0.04);
    border-color: #d9d9d9;
    box-shadow: none;
    cursor: not-allowed;
    opacity: 1;
  }
  :where(.css-dev-only-do-not-override-zv1cxv).ant-picker .ant-picker-input >input[disabled] {
    color: rgba(0, 0, 0, 0.25) !important;
    background: transparent;
  }
  :where(.css-dev-only-do-not-override-zv1cxv).ant-input-number-disabled .ant-input-number-input {
    color: rgba(0, 0, 0, 0.25) !important;
  }

  .ant-input-number {
    font-size: 12px;
    padding: 0px 7px;
    border-radius: 3px;
    height: 24px;
    color: #021726;
  }


  /* 选择框样式修改 */

  .ant-select-selector {
    font-size: 12px;
    border-color: #4096ff;
    //box-shadow: 0 0 0 2px rgba(5, 145, 255, 0.1);
    outline: 0;
    span {
      font-size: 12px;
    }
  }
  .ant-row {
    display: flex;
    flex-flow: row wrap;
    min-width: 0;
    align-items: center;
  }
  .ant-form-item .ant-form-item-control-input {
    position: relative;
    display: flex;
    align-items: center;
    min-height: 24px;
  }
  .ant-form-item-label{
    font-size: 12px !important;
    line-height: 1;
    /* 文本换行 */
    white-space: normal;
    word-wrap: break-word;
    word-break: break-all;
    text-align: right;
    /* 高度自动向下 */
    height: 100% !important;
    display: flex;
    align-items: center;
    justify-content: right;
    label{
      height: 24px;
      line-height: 24px;
      font-size: 12px !important;
    }
  }
  .ant-form-item-explain-error {
    position: absolute;
    z-index: 9;
    //width: 100%;
    margin-right: 5px;
    right: 0;
    margin-top: -22px;
    font-size: 12px !important;
  }
  .ant-form-item-explain-error:hover{
    visibility: hidden;
  }

  /* 数字输入框 */
  :where(.css-dev-only-do-not-override-1p3hq3p).ant-input-number {
    width: 100%;
  }

  /* 禁止选择 radio 样式 */
  :where(.css-dev-only-do-not-override-1p3hq3p).ant-radio-button-wrapper-disabled.ant-radio-button-wrapper-checked {
    color: rgba(0, 0, 0, 0.25);
    background-color: skyblue;
    border-color: #d9d9d9;
    box-shadow: none;
  }

}

.grid-container {
  display: grid;
  padding:0;
}

.grid-item {
  margin: 2px; /* 重置外边距 */
  width: 100%;
}

.grid-item-search {
  margin: 2px; /* 重置外边距 */
  width: 100%;
  padding-top: 5px;
}

.grid-container {
  margin: 0;
  grid-template-columns: repeat(3, 1fr);
}

/* 当需要调整为2列时，可以添加 .two-columns 类 */
.two-columns {
  grid-template-columns: repeat(2, 1fr);
}

/* 当需要调整为1列时，可以添加 .one-column 类 */
.one-column {
  grid-template-columns: repeat(1, 1fr);
}

/* 如果需要最后一行撑满 */
.merge-3 {
  grid-column: span 3;
}

.merge-2 {
  grid-column: span 2;
}

.merge-1 {
  grid-column: span 1;
}

/* 常用保存按钮样式 */
.cs-submit-btn {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 30px;
  margin: 20px 0;

  button {
    margin-right: 30px;
  }

  padding-bottom: 20px;
}




.cs-card-form{


  /* 弹框表格样式 去除框线 */
  .remove-table-border-add-bg{
    .surely-table-bordered .surely-table-cell:not(.surely-table-body-cell-range-single-cell):not( .surely-table-body-cell-range-selected) {
      border-right: none !important;
    }

    .blue-table-columns td {
      background-color: #5076f6;
      color: #ffffff;
    }
    .surely-table-row-hover  {
      background-color: var(--surely-table-row-selected-bg) !important;
    }
    .surely-table-row.surely-table-row-selected ,.tr-selected{
      background-color: #69c0ff !important;

    }
    .surely-table-row.surely-table-row-selected > .tr-selected {
      // 这里设置.tr-selected的样式，但基于你的描述，这可能不是你想要的
      // 如果.tr-selected只是另一个独立的类，并且你想要它也有透明背景，你应该直接在它上设置
      background-color: transparent !important; // 但注意，这不会和上面的逻辑冲突，除非它们应用于同一个元素
    }
    .surely-table-row-hover{
      background-color: #e6f7ff !important;
    }
    .surely-table-body .surely-table-body-cell-range-selected:not(.surely-table-body .surely-table-body-cell-inline-edit) {
      background-color: transparent !important;
      border: none;
    }
    .surely-table-cell > .surely-table-cell-edit-wrapper {
      border: none !important; /* 移除边框 */
    }
    .surely-table-body .surely-table-body-cell-range-selected.surely-table-body-cell-range-single-cell {
      border-color: transparent !important; /* 移除选中单元格的边框 */
    }
  }
  .ant-card-head {
    padding: 5px 16px;
    background: #fafafa;
    display: flex;
    justify-content: center;
    flex-direction: column;
    min-height: auto;
    color: rgba(0, 0, 0, 0.88);
    font-weight: 600;
    font-size: 16px;
    border-bottom: 1px solid #f0f0f0;
    border-radius: 2px 2px 0 0;
    .ant-card-head-wrapper {
      width: 100%;
      display: flex;
      align-items: center;
      .ant-card-head-title {
        display: inline-block;
        flex: 1;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
}

.cs-divider {
  height: 1px;
  background: #eeeeee;

}




/* 自定义输入框样式  */
.cs-select-dropdown{
  ::v-deep .ant-select-single .ant-select-selector .ant-select-selection-item {
    position: relative;
    white-space: normal !important;
    user-select: auto;

  }

  .ant-select-selection-item {
    background: red !important;
    color: #021726;
    user-select: auto;
  }


  :where(.css-nuidd8).ant-select-single .ant-select-selector .ant-select-selection-item{
    user-select: auto;
  }

  .ant-select-disabled {
    cursor: default;
  }
  /* 下拉选择框样式修改 */
  .ant-select-dropdown{
    .ant-select-item-option {
      display: flex;
      font-size: 12px;
      height: 24px !important;
      min-height: 24px !important;
    }
    .ant-select-item-option {
      display: flex;
      font-size: 12px;
      min-height: 24px;
      height: 24px;
      align-items: center;
      border-radius: 0;
    }

  }
  .ant-select-item {
    height: 24px !important;
    min-height: 24px !important;
    font-size: 12px !important;
    padding: 3px 20px !important;
  }

  .ant-select-item-option-active{
    background: #5897fb !important;
    color: #fff !important;
    font-weight: 500 !important;
    border-radius: 0 !important;

  }
  .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
    color:#fff !important;
    font-weight: 500 !important;
    background-color: #f5222d !important;
    border-radius: 0 !important;
  }
}


.cs-margin-right{
  margin-right: 10px;
}
.cs-margin-left{
  margin-left: 10px;
}
.cs-list-total-data{
  font-size: 12px;
  color: black;
  font-weight: normal;
  line-height: 24px;
  margin-right: 10px;
  padding-top: 2px;
}










