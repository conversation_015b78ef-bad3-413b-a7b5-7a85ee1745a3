<template>
  <section class="dc-section">
    <div class="cs-action" v-show="show">
      <!-- 查询列表区域 -->
      <div class="cs-search">
        <a-card :bordered="false">
          <bread-crumb>
            <div ref="area_head">
              <div class="search-btn">
                <a-button size="small" type="primary" class="cs-margin-right cs-refresh" @click="handlerRefresh" v-show="showSearch">
                  <template #icon>
                    <GlobalIcon type="redo" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" class="cs-margin-right" @click="handlerSearch">
                  {{localeContent('m.common.button.query')}}
                  <template #icon>
                    <GlobalIcon type="search" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" danger class="cs-margin-right cs-warning" @click="handleShowSearch">
                  <template #icon>
                    <GlobalIcon v-show="!showSearch" type="down" style="color:#fff"/>
                    <GlobalIcon v-show="showSearch" type="up" style="color:#fff"/>
                  </template>
                </a-button>
              </div>
            </div>
          </bread-crumb>
          <div class="separateLine"></div>
          <div ref="area_search">
            <div v-show="showSearch">
              <contract-search ref="headSearch" />
            </div>
          </div>
        </a-card>
      </div>
      <!-- 操作按钮区域 -->
      <div class="cs-action-btn">
        <div class="cs-action-btn-item" v-has="['yc-cs:auxiliaryMaterials-forContract:add']">
          <a-button size="small" @click="handlerAdd">
            <template #icon>
              <GlobalIcon type="plus" style="color:green"/>
            </template>
            {{localeContent('m.common.button.add')}}
          </a-button>
        </div>
<!--        <div class="cs-action-btn-item" v-has="['yc-cs:auxiliaryMaterials-forContract:update']">-->
<!--          <a-button size="small" @click="handlerEdit">-->
<!--            <template #icon>-->
<!--              <GlobalIcon type="form" style="color:orange"/>-->
<!--            </template>-->
<!--            {{localeContent('m.common.button.update')}}-->
<!--          </a-button>-->
<!--        </div>-->
        <div class="cs-action-btn-item" v-has="['yc-cs:auxiliaryMaterials-forContract:delete']">
          <a-button size="small" :loading="deleteLoading" @click="handlerDelete">
            <template #icon>
              <GlobalIcon type="delete" style="color:red"/>
            </template>
            {{localeContent('m.common.button.delete')}}
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:auxiliaryMaterials-forContract:export']">
          <a-button size="small" :loading="exportLoading" @click="handlerExport">
            <template #icon>
              <GlobalIcon type="folder-open" style="color:orange"/>
            </template>
            {{localeContent('m.common.button.export')}}
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:auxiliaryMaterials-forContract:confirm']">
          <a-button size="small" :loading="confirmLoading" @click="handlerConfirm">
            <template #icon>
              <GlobalIcon type="check" style="color:green"/>
            </template>
            确认
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:auxiliaryMaterials-forContract:copy']">
          <a-button size="small" :loading="copyLoading" @click="handlerCopy">
            <template #icon>
              <GlobalIcon type="snippets" style="color:deepskyblue"/>
            </template>
            版本复制
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:auxiliaryMaterials-forContract:audit']">
          <a-button size="small" :loading="auditLoading" @click="handlerAudit">
            <template #icon>
              <GlobalIcon type="cloud" style="color:deepskyblue"/>
            </template>
            发送审核
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:auxiliaryMaterials-forContract:invalid']">
          <a-button size="small" :loading="invalidLoading" @click="handlerInvalid">
            <template #icon>
              <GlobalIcon type="close-square" style="color:red"/>
            </template>
            作废
          </a-button>
        </div>

        <div class="cs-action-btn-settings">
          <!-- 自定义显示组件 -->
          <CsTableColSettings
            :resId="tableKey"
            :tableKey="tableKey+'-contract'"
            :initSettingColumns="originalColumns"
            :showColumnSettings="true"
            @customColumnChange="customColumnChange"
          >
          </CsTableColSettings>
        </div>
      </div>

      <!-- 表格区域 -->
      <div v-if="showColumns && showColumns.length > 0">
        <s-table
          :animate-rows="false"
          ref="tableRef"
          class="cs-action-item remove-table-border-add-bg"
          size="small"
          :scroll="{ y: tableHeight,x:400 }"
          column-drag
          bordered
          :pagination="false"
          :columns="showColumns.length > 0 ?showColumns:totalColumns"
          :data-source="dataSourceList"
          :row-selection="{ selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          row-key="id"
          :custom-row="customRow"
          :row-height="30"
          :range-selection="false"
        >
          <!-- 操作 -->
          <template #bodyCell="{ column,record }">
            <template v-if="column.key === 'operation'">
              <div class="operation-container">
                <a-button
                  size="small"
                  type="link"
                  @click="handleEditByRow(record)"
                  :style="operationEdit('edit')"
                >
                  <template #icon>
                    <GlobalIcon type="form" style="color:#e93f41"/>
                  </template>
                </a-button>
                <a-button
                  size="small"
                  type="link"
                  @click="handleViewByRow(record)"
                  :style="operationEdit('view')"
                >
                  <template #icon>
                    <GlobalIcon type="search" style="color:#1677ff"/>
                  </template>
                </a-button>
              </div>
            </template>
          </template>
        </s-table>
      </div>
      <!-- 分页 -->
      <div class=cs-pagination v-if="showColumns && showColumns.length > 0">
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer :page-size="page.pageSize" :total="page.total" @change="onPageChange">
          <template #buildOptionText="props">
            <span>{{ props.value }}条/页</span>
          </template>
        </a-pagination>
      </div>
    </div>

    <!-- 新增 编辑数据 -->
    <div v-if="!show">
      <!--      <contract-edit :editConfig="editConfig" @on-back="handlerOnBack" />-->
      <contract-tab :editConfig="editConfig" @onEditBack="handlerOnBack"></contract-tab>
    </div>

    <!-- 导入数据 -->
    <!--    <ImportIndex :importShow="importShow" :importConfig="importConfig" @onImportSuccess="importSuccess"></ImportIndex>-->

    <!-- 使用计划选择组件 -->
    <plan-select-modal
      v-model:visible="planSelectVisible"
      @select="handlePlanSelect"
    />

  </section>
</template>

<script setup>
/* 使用自定义 Hook 函数 */
import {useCommon} from '@/view/common/useCommon'
import {createVNode, onMounted, ref, watch} from "vue";
import {getColumns} from "@/view/auxiliaryMaterials/forContract/ContractColumns";
import {message, Modal} from "ant-design-vue";
import BreadCrumb from "@/components/breadcrumb/BreadCrumb.vue";
import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
const { totalColumns } = getColumns()
import {localeContent} from "../../utils/commonUtil";
import ycCsApi from "@/api/ycCsApi";
import CsTableColSettings from "@/components/settings/CsTableColSettings.vue";
import {useRoute, useRouter} from "vue-router";
import {editStatus} from "@/view/common/constant";
import {deepClone} from "@/view/utils/common";
import ContractSearch from "@/view/auxiliaryMaterials/forContract/ContractSearch.vue";
import ContractTab from "@/view/auxiliaryMaterials/forContract/ContractTab.vue";
import {
  checkContract,
  confirmContract, copyContract,
  deleteContract,
  invalidContract,
  sendAudit
} from "@/api/auxiliaryMaterials/forContract/contractApi";
import PlanSelectModal from './components/PlanSelectModal.vue';


/* 引入通用方法 */
const {
  editConfig,
  show,
  page,
  showSearch,
  headSearch,
  handleViewByRow,
  operationEdit,
  onPageChange,
  handleShowSearch,
  handlerSearch,
  dataSourceList,
  tableLoading,
  getTableScroll,
  exportLoading,
  getList,
  ajaxUrl,
  doExport,
  handlerRefresh,
  gridData,
  onSelectChange

} = useCommon()



defineOptions({
  name: 'ContractList',
});

const router = useRouter();
const planSelectVisible = ref(false);


const importShow = ref(false)

onMounted(fn => {

  ajaxUrl.selectAllPage = ycCsApi.auxiliaryMaterials.bizIAuxMatForContractHead.list
  ajaxUrl.exportUrl = ycCsApi.auxiliaryMaterials.bizIAuxMatForContractHead.export
  tableHeight.value = getTableScroll(100, '');
  getList()
  initCustomColumn()

})

const tableHeight = ref('')

/* 按钮loading */
const deleteLoading = ref(false)
const confirmLoading = ref(false)
const copyLoading = ref(false)
const auditLoading = ref(false)
const invalidLoading = ref(false)

/* 返回事件 */
const handlerOnBack = (flag) => {
  console.log('返回', flag)
  show.value = !show.value;
  // 返回清空选择数据
  if (flag) {
    getList()
  }
}

/* 新增数据 */
const handlerAdd = () => {
  planSelectVisible.value = true;
}

const handleEditByRow = (row) => {
  // 在这里添加处理编辑行的逻辑
  if (row.docStatus !== '0') {
    message.warning('仅0编制的数据可以操作编辑')
    return
  }
  show.value = !show.value
  editConfig.value.editStatus = editStatus.EDIT
  editConfig.value.editData = row
}
/* 编辑数据 */
const handlerEdit = () => {
  if (gridData.selectedRowKeys.length <= 0) {
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1) {
    message.warning('只能选择一条数据')
    return
  }
  if (gridData.selectedData[0].docStatus !== '0') {
    message.warning('仅0编制的数据可以操作编辑')
    return
  }
  editConfig.value.editStatus = editStatus.EDIT
  editConfig.value.editData = gridData.selectedData[0]

  console.log('editConfig.value.editData', gridData)
  show.value = !show.value;
}

/* 删除数据 */
const handlerDelete = () => {
  if (gridData.selectedRowKeys.length <= 0) {
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1) {
    message.warning('请选择一条数据')
    return
  }
  // 弹出确认框
  Modal.confirm({
    title: '提醒?',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '删除',
    cancelText: '取消',
    content: '确认删除所选项吗？',
    onOk() {
      deleteLoading.value = true
      deleteContract(gridData.selectedRowKeys).then(res => {
        if (res.code === 200) {
          message.success("删除成功！")
          getList()
        } else {
          message.error(res.message)
        }
      }).finally(() => {
        deleteLoading.value = false
      })
    },
    onCancel() {

    },
  });
}

/* 导出事件 */
const handlerExport = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  const timestamp = `${year}${month}${day}${hours}${minutes}${seconds}`
  doExport(`国营贸易进口辅料-外商合同${timestamp}.xlsx`, totalColumns)
}

/* 自定义设置 */
const showColumns = ref([])

/* 唯一键 */
const tableKey = ref('')
console.log('window.majesty.router', window.majesty.router.patch)
tableKey.value = window.$vueApp ? window.majesty.router.currentRoute.value.path : useRoute().path
const originalColumns = ref()

/* 自定义显示列初始化操作 */
const initCustomColumn = () => {
  // 这里是拷贝是属于
  let tempColumns = deepClone(totalColumns.value)
  let dealColumns = []
  // 使用map遍历会丢失customRender方法，所以使用forEach
  tempColumns.map((item) => {
    let newObj = Object.assign({}, item);
    newObj["visible"] = true;
    // 需要将customRender 方法追加到新对象中
    if (item.customRender) {
      newObj["customRender"] = item.customRender;
    }
    dealColumns.push(newObj);
  });
  //原始列信息
  originalColumns.value = dealColumns;
}

/* 选中visible为true的数据进行显示 */
const customColumnChange = (settingColumns) => {
  totalColumns.value = settingColumns.filter((item) => item.visible === true);
  showColumns.value = [...totalColumns.value]
}

/* 双击行进入编辑页面 */
const handleRowDblclick = (record) => {
  handleEditByRow(record)
};
// 自定义行属性
const customRow = (record) => {
  return {
    onDblclick: () => {
      handleRowDblclick(record);
    }, style: {cursor: 'pointer'}
  };
};


/* 监控 dataSourceList */
// watch(dataSourceList, (newValue, oldValue) => {
//   showColumns.value = [...totalColumns.value];
//   // 将showColumns的数据属性 和 初始属性进行比对，如果初始属性存在customRender 方法，追加到showColumns中
// },{deep:true})
watch(totalColumns.value, (newValue, oldValue) => {
  if (!window.$vueApp) {
    showColumns.value = [...totalColumns.value];
  } else {
    if (newValue.length === 0) {
      showColumns.value = [...totalColumns.value];
    } else {
      showColumns.value = newValue.map((item) => {
        item.visible = true;
        return item;
      })
      totalColumns.value = newValue.map((item) => {
        item.visible = true;
        return item;
      })
    }
  }
}, {immediate: true, deep: true})


/* 处理计划选择 */
const handlePlanSelect = (plan) => {
  editConfig.value.editStatus = editStatus.ADD;
  editConfig.value.editData = plan;
  //标识新增
  // editConfig.value.flag = editStatus.ADD;
  show.value = !show.value;
};

/* 确认事件 */
const handlerConfirm = () => {
  if (gridData.selectedRowKeys.length <= 0) {
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1) {
    message.warning('只能选择一条数据')
    return
  }
  // 弹出确认框
  Modal.confirm({
    title: '提醒',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: '确认执行此操作吗？',
    onOk() {
      confirmLoading.value = true
      // 这里需要调用确认API
      const params = {
        id: gridData.selectedRowKeys[0]
      }
      confirmContract(params).then(res => {
        if (res.code === 200) {
          message.success("确认成功！")
          getList()
        } else {
          message.error(res.message)
        }
      }).finally(() => {
        confirmLoading.value = false
      })
    },
    onCancel() {
      // 取消操作
    },
  });
}

/* 版本复制事件 */
const handlerCopy = () => {
  if (gridData.selectedRowKeys.length <= 0) {
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1) {
    message.warning('只能选择一条数据')
    return
  }
  // 弹出确认框
  Modal.confirm({
    title: '提醒',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '复制',
    cancelText: '取消',
    content: '确认复制所选项吗？',
    onOk() {
      copyLoading.value = true
      //数据校验
      const params = {
        id: gridData.selectedRowKeys[0]
      }
      checkContract(params).then(res => {
        if (res.code === 200 && res.data > 0) {
          // 弹出确认框
          Modal.confirm({
            title: '提醒',
            icon: createVNode(ExclamationCircleOutlined),
            okText: '确认',
            cancelText: '取消',
            content: '当前单据存在有效数据，是否将其作废并重新生成一份新数据？',
            onOk() {
              copyContract(params).then(res => {
                if (res.code === 200) {
                  message.success("复制成功！")
                  getList()
                } else {
                  message.error(res.message)
                }
              }).finally(() => {
                copyLoading.value = false
              })
            },
            onCancel() {
              // 取消操作
              copyLoading.value = false
            },
          })
        } else if (res.code === 200 && res.data === 0) {
          // 这里需要调用复制API
          copyContract(params).then(res => {
            if (res.code === 200) {
              message.success("复制成功！")
              getList()
            } else {
              message.error(res.message)
            }
          }).finally(() => {
            copyLoading.value = false
          })
        }
      })
    },
    onCancel() {
      // 取消操作
    },
  });
}

/* 发送审核事件 */
const handlerAudit = () => {
  if (gridData.selectedRowKeys.length <= 0) {
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1) {
    message.warning('只能选择一条数据')
    return
  }
  // 弹出确认框
  Modal.confirm({
    title: '提醒',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '发送',
    cancelText: '取消',
    content: '确认发送审核吗？',
    onOk() {
      auditLoading.value = true
      const params = {
        id: gridData.selectedRowKeys[0]
      }
      sendAudit(params).then(res => {
        if (res.code === 200) {
          message.success("发送成功！")
          getList()
        } else {
          message.error(res.message)
        }
      }).finally(() => {
        auditLoading.value = false
      })

    },
    onCancel() {
      // 取消操作
    },
  });
}

/* 作废事件 */
const handlerInvalid = () => {
  if (gridData.selectedRowKeys.length <= 0) {
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1) {
    message.warning('只能选择一条数据')
    return
  }
  // 弹出确认框
  Modal.confirm({
    title: '提醒',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '作废',
    cancelText: '取消',
    content: '确认作废所选项吗？',
    onOk() {
      invalidLoading.value = true
      // 这里需要调用作废API
      const params = {
        id: gridData.selectedRowKeys[0]
      }
      invalidContract(params).then(res => {
        if (res.code === 200) {
          message.success("作废成功！")
          getList()
        } else {
          message.error(res.message)
        }
      }).finally(() => {
        invalidLoading.value = false
      })

    },
    onCancel() {
      // 取消操作
    },
  });
}

</script>

<style lang="less" scoped>


</style>
