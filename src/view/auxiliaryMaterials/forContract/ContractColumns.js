import { h, reactive, ref } from 'vue'
import { Tag } from 'ant-design-vue'
import { baseColumns, createSorter, createDateSorter, createNumberSorter } from "@/view/common/baseColumns";
import { productClassify } from '@/view/common/constant'
import { useColumnsRender } from "../../common/useColumnsRender";
import { useMerchant } from "@/view/common/useMerchant";

// 格式化数字为千分位分隔的工具函数
const formatNumber = (value) => {
  if (value === undefined || value === null || value === '') {
    return '';
  }
  // 将数字转换为字符串并添加千分位分隔符
  // 检查是否有小数部分
  const numValue = Number(value);
  if (Number.isNaN(numValue)) {
    return '';
  }

  const hasDecimal = numValue % 1 !== 0;
  if (hasDecimal) {
    // 如果有小数，保留原有小数位数
    return new Intl.NumberFormat('zh-CN').format(numValue);
  } else {
    // 如果没有小数，补充.00
    return new Intl.NumberFormat('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(numValue);
  }
};

const { baseColumnsExport, baseColumnsShow } = baseColumns()
const { cmbShowRender } = useColumnsRender()
const { merchantOptions, getMerchantOptions } = useMerchant()

// 初始化时获取供应商数据
await getMerchantOptions()

function getColumns() {
  const commColumns = reactive([
    'id',
    'businessType',
    'contractNo',
    'customerName',
    'supplierName',
    'signDate',
    'contractStartDate',
    'contractEndDate',
    'signPlace',
    'signPlaceEn',
    'portOfShipment',
    'portOfDestination',
    'customsPort',
    'paymentMethod',
    'currency',
    'remark',
    'docStatus',
    'confirmDate',
    'auditStatus',
    'dataState',
    'versionNo',
    'tradeCode',
    'sysOrgCode',
    'parentId',
    'createBy',
    'createTime',
    'updateBy',
    'updateTime',
    'insertUserName',
    'updateUserName',
    'extend1',
    'extend2',
    'extend3',
    'extend4',
    'extend5',
    'extend6',
    'extend7',
    'extend8',
    'extend9',
    'extend10',
    'totalAmount'
  ])

  // 导出字段设置
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])

  // table表格字段设置
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  // table表格字段设置
  const totalColumns = ref([
    {
      title: '操作',
      maxWidth: 80,
      width: 80,
      dataIndex: 'operation',
      key: 'operation',
      align: 'center',
      fixed: 'left',
      resizable: true,
    },
    {
      title: '合同号',
      width: 160,
      minWidth: 160,
      align: 'center',
      dataIndex: 'contractNo',
      resizable: true,
      key: 'contractNo',
      ...createSorter('contractNo')
    },
    {
      title: '买方',
      width: 250,
      minWidth: 250,
      align: 'center',
      dataIndex: 'customerName',
      resizable: true,
      key: 'customerName',
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text, merchantOptions.value))
      }
    },
    {
      title: '卖方',
      width: 250,
      minWidth: 250,
      align: 'center',
      dataIndex: 'supplierName',
      resizable: true,
      key: 'supplierName',
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text, merchantOptions.value))
      }
    },
    {
      title: '金额',
      width: 150,
      minWidth: 150,
      align: 'center',
      dataIndex: 'totalAmount',
      resizable: true,
      key: 'totalAmount',
      ...createNumberSorter('totalAmount'),
      customRender: ({ text }) => {
        return formatNumber(text);
      }
    },
    {
      title: '签约日期',
      width: 150,
      minWidth: 150,
      align: 'center',
      dataIndex: 'signDate',
      resizable: true,
      key: 'signDate',
      customRender: ({ text }) => {
        return h('span', text ? text.slice(0, 10) : text)
      }
    },
    {
      title: '生效日期',
      width: 150,
      minWidth: 150,
      align: 'center',
      dataIndex: 'contractStartDate',
      resizable: true,
      key: 'contractStartDate',
      customRender: ({ text }) => {
        return h('span', text ? text.slice(0, 10) : text)
      },
      ...createDateSorter('contractStartDate')
    },
    {
      title: '制单人',
      width: 150,
      minWidth: 150,
      align: 'center',
      dataIndex: 'insertUserName',
      resizable: true,
      key: 'insertUserName'
    },
    {
      title: '制单时间',
      width: 150,
      minWidth: 150,
      align: 'center',
      dataIndex: 'createTime',
      resizable: true,
      key: 'createTime'
    },
    {
      title: '单据状态',
      width: 120,
      minWidth: 120,
      align: 'center',
      dataIndex: 'docStatus',
      resizable: true,
      key: 'docStatus',
      customRender: ({ text }) => {
        const tagColor = text === '2' ? 'error' : 'success';
        return h(Tag, { color: tagColor }, cmbShowRender(text, productClassify.data_status))
      }
    },
    {
      title: '审核状态',
      width: 120,
      minWidth: 120,
      align: 'center',
      dataIndex: 'auditStatus',
      resizable: true,
      key: 'auditStatus',
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,productClassify.audit_status))
      }
    },
    {
      title: '版本号',
      width: 150,
      minWidth: 150,
      align: 'center',
      resizable: true,
      dataIndex: 'versionNo',
      key: 'versionNo'
    },
    {
      title: '业务类型',
      width: 150,
      minWidth: 150,
      align: 'center',
      dataIndex: 'businessType',
      key: 'businessType',
      resizable: true,
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,productClassify.businessType))
      }

    },
  ])

  return {
    columnsConfig,
    excelColumnsConfig,
    totalColumns
  }
}

export { getColumns }
