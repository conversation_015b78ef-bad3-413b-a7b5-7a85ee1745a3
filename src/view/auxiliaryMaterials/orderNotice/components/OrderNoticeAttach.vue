<template>
  <a-table
    style="width:800px"
    size="small"
    :columns="columns"
    :data-source="dataSource"
    bordered
    :pagination="false"
    row-key="id"
  >
    <template #bodyCell="{ column, record }">
      <!-- 文件列的自定义渲染 -->
      <template v-if="column.key === 'action'">
        <cs-upload :head-id="edit.config.value.headId" :b-type="record.businessType"
                   :is-show="!edit.commonShowFlag.value"/>
      </template>
    </template>
  </a-table>
</template>

<script setup>
import CsUpload from '@/components/upload/CsUpload.vue'
import { inject, ref } from 'vue'
import { docType, editStatus } from '@/view/common/constant'

const edit = inject('edit')

// 数据源
const dataSource = ref([
  {
    id: 1,
    type: '签约环节文件',
    businessType: docType.signingAttachType
  }
])

// 显示列
const columns = [
  {
    title: '文件类型',
    dataIndex: 'type',
    align: 'center',
    width: 200
  },
  {
    title: '文件',
    key: 'action',
    align: 'center',
    width: 600,
    flex: 1
  }
]

defineOptions({
  name: 'OrderNoticeAttach'
})
</script>
