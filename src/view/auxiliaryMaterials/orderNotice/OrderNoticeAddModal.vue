<template>
  <a-modal v-model:open="open" title="选择购销合同" :keyboard="false" :width="800"
           :mask-closable="false" :destroy-on-close="false">
    <div class="cs-action">
      <!-- 搜索区域 -->
      <a-form
        ref="formRef"
        layout="inline"
        :model="searchParam"
        class="search-form"
      >
        <!-- 购销合同号 -->
        <a-form-item name="psContractNo" :label="'购销合同号'">
          <a-input v-model:value="searchParam.psContractNo" :allow-clear="true"/>
        </a-form-item>

        <!-- 购销年份 -->
        <a-form-item name="psYear" :label="'购销年份'">
          <a-date-picker v-model:value="searchParam.psYear" picker="year" placeholder="" format="YYYY"
                         value-format="YYYY"/>
        </a-form-item>

        <!-- 重置 -->
        <a-form-item>
          <a-button type="default" :icon="h(UndoOutlined)" @click="handleReset">重置</a-button>
        </a-form-item>
      </a-form>

      <!-- 表格区域 -->
      <a-table
        ref="tableRef"
        :loading="tableLoading"
        :columns="showColumns"
        :custom-row="customRow"
        :data-source="datasourceList"
        :row-selection="{ selectedRowKeys: selectedKeys , onChange: onSelectChange }"
        row-key="id"
        :scroll="{ y: 320, x: 320}"
        :pagination="false"
        size="small"
        style="font-size: 12px"
      >
      </a-table>
      <div class="select-summary">
        <div class="info">
          <span>
            已选中 <a-tag color="error" style="margin: 0">{{ selectedKeys.length }}</a-tag> 个购销合同
          </span>
        </div>
      </div>
    </div>

    <template #footer>
      <a-button type="primary" @click="handleSelect" :loading="selectLoading">选择</a-button>
      <a-button @click="closeModal">关闭</a-button>
    </template>
  </a-modal>
</template>

<script setup>
import { ref, h, shallowRef, toRaw, watchEffect, watch, onBeforeUnmount } from 'vue'
import { UndoOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { getSelectContractData } from '@/api/auxiliaryMaterials/orderNotice'

// 防抖timer
let timer = null

// 防抖
function debounced(func, delay = 1000) {
  return (...args) => {
    clearTimeout(timer)
    timer = setTimeout(() => {
      func(...args)
    }, delay)
  }
}

// 选择购销合同事件
const emit = defineEmits(['selectContractNo'])

// 表格loading
const tableLoading = ref(false)

// 打开状态
const open = ref(false)

/**
 * 打开模态框
 */
function openModal() {
  open.value = true
}

/**
 * 关闭模态框
 */
function closeModal() {
  open.value = false
}

// 表单ref
const formRef = ref(null)

// 搜索参数
const searchParam = ref({
  psContractNo: '',
  psYear: ''
})

/**
 * 重置
 */
function handleReset() {
  selectedKeys.value = []
  if (formRef.value) {
    formRef.value.resetFields()
  }
  getList()
}

// 选中键列表
const selectedKeys = ref([])

/**
 * 选中行变化回调函数
 * @param selectedRowKeys 行keys
 */
function onSelectChange(selectedRowKeys) {
  const currKeys = toRaw(selectedKeys.value)
  const datasourceListKeys = datasourceList.value.map(item => item.id)
  const notAppearKeys = currKeys.filter(key => !datasourceListKeys.includes(key))
  selectedKeys.value = [...selectedRowKeys, ...notAppearKeys]
}

/**
 * 自定义行
 * @param record 数据记录
 * @returns {{onDblclick: *, style: {cursor: string}}} 行配置
 */
function customRow(record) {
  return {
    onDblclick: () => {
      const currentKeys = selectedKeys.value
      if (currentKeys['includes'](record.id)) {
        selectedKeys.value = currentKeys.filter(key => key !== record.id)
      } else {
        currentKeys.push(record.id)
      }
    },
    style: {cursor: 'pointer'}
  }
}

// 显示列
const showColumns = [
  {
    title: '购销合同号',
    minWidth: 220,
    align: 'center',
    dataIndex: 'psContractNo',
    resizable: true,
    key: 'psContractNo'
  },
  {
    title: '购销年份',
    minWidth: 120,
    align: 'center',
    dataIndex: 'psYear',
    resizable: true,
    key: 'psYear'
  }
]

// 原始数据源列表
let originDatasourceList = []

// 数据源列表
const datasourceList = shallowRef([])

// 选择购销合同副作用函数
const stopWatchSelectContract = watchEffect(() => {
  const {psContractNo, psYear} = searchParam.value
  debounced(() => {
    let handleDatasourceList = originDatasourceList
    if (psContractNo) {
      handleDatasourceList = handleDatasourceList.filter(item => item.psContractNo.includes(psContractNo))
    }
    if (psYear) {
      handleDatasourceList = handleDatasourceList.filter(item => item.psYear === psYear)
    }
    datasourceList.value = handleDatasourceList
  }, 500)()
})

// 选择按钮loading
const selectLoading = ref(false)

// open侦听器
const stopWatchOpen = watch(open, (openStatus) => {
  if (openStatus) {
    selectLoading.value = false
    handleReset()
  }
})

/**
 * 列表获取
 */
async function getList() {
  tableLoading.value = true
  try {
    const res = await getSelectContractData()
    if (res.success) {
      originDatasourceList = res.data
      datasourceList.value = originDatasourceList
    } else {
      message.error(res.message)
    }
  } finally {
    tableLoading.value = false
  }
}

/**
 * 选择购销合同
 */
function handleSelect() {
  if (selectedKeys.value.length < 1) {
    message.warn('请选择购销合同')
    return
  }
  selectLoading.value = true
  emit('selectContractNo', toRaw(selectedKeys.value))
}

onBeforeUnmount(() => {
  stopWatchSelectContract()
  stopWatchOpen()
  if (timer) {
    clearTimeout(timer)
  }
})

defineExpose({openModal, closeModal, selectLoading})

defineOptions({
  name: 'OrderNoticeAddModal',
})
</script>

<style>
.search-form {
  margin-top: 15px;
  margin-bottom: 10px;
}

.select-summary {
  position: relative;
  padding: 15px 0;
  font-size: 12px;
}

.select-summary .info {
  position: absolute;
  right: 0;
  bottom: 0;
}
</style>
