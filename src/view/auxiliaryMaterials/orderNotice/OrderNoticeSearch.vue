<template>
  <a-form ref="formRef" layout="inline" label-align="right" :label-col="{ style: { width: '100px' } }"
          :model="searchParam"
          class="cs-form  grid-container">
    <!-- 单据状态 -->
    <a-form-item name="dataStatus" :label="'单据状态'" class="grid-item" :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key" allow-clear show-search multiple
                 v-model:value="searchParam.dataStatus">
        <a-select-option class="cs-select-dropdown" v-for="item in productClassify.data_status"
                         :key="`${item.value} ${item.label}`" :value="item.value" :label="`${item.value}${item.label}`">
          {{ item.value }} {{ item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>

    <!-- 订货编号 -->
    <a-form-item name="orderNo" :label="'订货编号'" class="grid-item" :colon="false">
      <a-input size="small" v-model:value="searchParam.orderNo" allow-clear/>
    </a-form-item>

    <!-- 客户 -->
    <a-form-item name="customer" :label="'客户'" class="grid-item" :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key" allow-clear show-search
                 v-model:value="searchParam.customer">
        <a-select-option class="cs-select-dropdown" v-for="item in customerPageOptions"
                         :key="`${item.value} ${item.label}`" :value="item.value" :label="`${item.value}${item.label}`">
          {{ item.value }} {{ item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>

    <!-- 制单日期 -->
    <a-form-item name="createDate" label="制单日期" class="grid-item" :colon="false">
      <a-form-item-rest>
        <a-row>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.createDateFrom"
              :valueFormat="DATE_FORMAT.DATE"
              :format="DATE_FORMAT.DATE"
              size="small"
              style="width: 100%"
              placeholder="制单日期起"
              :locale="locale"
              allow-clear
            />
          </a-col>
          <a-col :span="2" style="text-align: center">
            -
          </a-col>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.createDateTo"
              :valueFormat="DATE_FORMAT.DATE"
              :format="DATE_FORMAT.DATE"
              size="small"
              style="width: 100%"
              placeholder="制单日期止"
              :locale="locale"
              allow-clear
            />
          </a-col>
        </a-row>
      </a-form-item-rest>
    </a-form-item>
  </a-form>
</template>

<script setup>
import CsSelect from '@/components/select/CsSelect.vue'
import locale from 'ant-design-vue/es/date-picker/locale/zh_CN'
import { inject, ref } from 'vue'
import { productClassify, DATE_FORMAT } from '@/view/common/constant'

const {page: customerPageOptions} = inject('customerOptions')

const searchParam = ref({
  dataStatus: '',
  orderNo: '',
  customer: '',
  createDateFrom: '',
  createDateTo: ''
})

const formRef = ref(null)

const resetSearch = () => {
  formRef.value.resetFields()
}

defineExpose({searchParam, resetSearch})

defineOptions({
  name: 'OrderNoticeSearch'
})
</script>
