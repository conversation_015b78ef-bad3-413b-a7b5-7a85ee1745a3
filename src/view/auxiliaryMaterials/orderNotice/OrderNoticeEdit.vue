<template>
  <section>
    <a-card size="small" title="表头" class="cs-card-form head">
      <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="formRules"
              :model="formData" class="grid-container cs-form">
        <!-- 业务类型 -->
        <a-form-item name="businessType" :label="'业务类型'" class="grid-item" :colon="false">
          <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search
                     v-model:value="formData.businessType" id="businessType">
            <a-select-option v-for="item in productClassify.commonBusinessType" :key="`${item.value} ${item.label}`"
                             :value="item.value" :label="`${item.value} ${item.label}`">
              {{ item.value }} {{ item.label }}
            </a-select-option>
          </cs-select>
        </a-form-item>

        <!-- 订货编号 -->
        <a-form-item name="orderNo" :label="'订货编号'" class="grid-item" :colon="false">
          <a-input :disabled="!commonAddFlag" size="small" v-model:value="formData.orderNo"/>
        </a-form-item>

        <!-- 订货日期 -->
        <a-form-item name="orderDate" :label="'订货日期'" class="grid-item" :colon="false">
          <a-date-picker
            :disabled="commonShowFlag"
            v-model:value="formData.orderDate"
            :valueFormat="DATE_FORMAT.DATE"
            :format="DATE_FORMAT.DATE"
            :locale="zhCN"
            :placeholder="''"
            size="small"
            style="width: 100%"
          />
        </a-form-item>

        <!-- 客户 -->
        <a-form-item name="customer" :label="'客户'" class="grid-item" :colon="false">
          <cs-select :disabled="commonShowFlag" optionFilterProp="label" option-label-prop="key" allow-clear
                     show-search v-model:value="formData.customer">
            <a-select-option v-for="item in customerAllOptions" :key="`${item.value} ${item.label}`"
                             :value="item.value" :label="`${item.value} ${item.label}`">
              {{ item.value }} {{ item.label }}
            </a-select-option>
          </cs-select>
        </a-form-item>

        <!-- 购销合同号 -->
        <a-form-item name="purchaseSalesContractNo" :label="'购销合同号'" class="grid-item" :colon="false">
          <a-input disabled size="small" v-model:value="formData.purchaseSalesContractNo"/>
        </a-form-item>

        <!-- 版本号 -->
        <a-form-item name="versionNo" :label="'版本号'" class="grid-item" :colon="false">
          <a-input disabled size="small" v-model:value="formData.versionNo"/>
        </a-form-item>

        <!-- 备注 -->
        <a-form-item name="note" :label="'备注'" class="grid-item" :colon="false">
          <a-input :disabled="commonShowFlag" size="small" v-model:value="formData.note"/>
        </a-form-item>

        <!-- 制单人 -->
        <a-form-item name="createByName" :label="'制单人'" class="grid-item" :colon="false">
          <a-input disabled size="small" v-model:value="formData.createByName"/>
        </a-form-item>

        <!-- 制单时间 -->
        <a-form-item name="updateTime" :label="'制单时间'" class="grid-item" :colon="false">
          <a-date-picker
            disabled
            v-model:value="formData.updateTime"
            :valueFormat="DATE_FORMAT.DATE_TIME"
            :format="DATE_FORMAT.DATE_TIME"
            :locale="zhCN"
            placeholder=""
            size="small"
            style="width: 100%"
          />
        </a-form-item>

        <!-- 单据状态 -->
        <a-form-item name="dataStatus" :label="'单据状态'" class="grid-item" :colon="false">
          <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search
                     v-model:value="formData.dataStatus">
            <a-select-option v-for="item in productClassify.data_status" :key="`${item.value} ${item.label}`"
                             :value="item.value" :label="`${item.value} ${item.label}`">
              {{ item.value }} {{ item.label }}
            </a-select-option>
          </cs-select>
        </a-form-item>

        <!-- 确认时间 -->
        <a-form-item name="confirmTime" :label="'确认时间'" class="grid-item" :colon="false">
          <a-date-picker
            disabled
            v-model:value="formData.confirmTime"
            :valueFormat="DATE_FORMAT.DATE_TIME"
            :format="DATE_FORMAT.DATE_TIME"
            :locale="zhCN"
            placeholder=""
            size="small"
            style="width: 100%"
          />
        </a-form-item>

        <!-- 审批状态 -->
        <a-form-item name="apprStatus" :label="'审批状态'" class="grid-item" :colon="false">
          <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search
                     v-model:value="formData.apprStatus">
            <a-select-option v-for="item in productClassify.approval_status" :key="`${item.value} ${item.label}`"
                             :value="item.value" :label="`${item.value} ${item.label}`">
              {{ item.value }} {{ item.label }}
            </a-select-option>
          </cs-select>
        </a-form-item>

        <div class="cs-submit-btn merge-3">
          <a-button v-if="!commonShowFlag" size="small" type="primary" @click="handleSave" class="cs-margin-right"
                    :loading="saveLoading">
            保存
          </a-button>
          <a-button size="small" @click="edit.back(true)">
            返回
          </a-button>
          <a-button v-if="!commonShowFlag && !commonAddFlag && formData.dataStatus === DATA_STATUS.DRAFT" type="text"
                    size="small" :icon="h(CheckOutlined)" @click="handleConfirm">
            确认
          </a-button>
        </div>
      </a-form>
    </a-card>
    <a-card title="表体" size="small" class="cs-card-form body">
      <order-notice-body-list ref="bodyList"/>
    </a-card>
  </section>
</template>

<script setup>
import CsSelect from '@/components/select/CsSelect.vue'
import OrderNoticeBodyList from '@/view/auxiliaryMaterials/orderNotice/body/OrderNoticeBodyList.vue'
import { ref, inject, onMounted, h, createVNode } from 'vue'
import CheckOutlined from '@ant-design/icons-vue/lib/icons/CheckOutlined'
import ExclamationCircleOutlined from '@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined'
import zhCN from 'ant-design-vue/es/date-picker/locale/zh_CN'
import { message, Modal } from 'ant-design-vue'
import { productClassify, DATE_FORMAT, DATA_STATUS } from '@/view/common/constant'
import { insertHead, updateHead, confirmHead } from '@/api/auxiliaryMaterials/orderNotice'

const edit = inject('edit')
const {all: customerAllOptions} = inject('customerOptions')

// 通用显示标识
const commonShowFlag = edit.commonShowFlag
// 通用新增标识
const commonAddFlag = edit.commonAddFlag

// 表头form ref
const formRef = ref(null)

// 表体ref
const bodyList = ref(null)

// 表头form数据
const formData = ref({
  businessType: '2',
  orderNo: '',
  orderDate: '',
  customer: '',
  purchaseSalesContractNo: '',
  versionNo: '',
  note: '',
  createByName: '',
  updateTime: '',
  dataStatus: '',
  confirmTime: '',
  apprStatus: ''
})

// 表头form校验规则
const formRules = {
  orderNo: [
    {required: true, message: '请输入订货编号', trigger: 'blur'}
  ],
  orderDate: [
    {required: true, message: '请选择订货日期', trigger: 'blur'}
  ],
  customer: [
    {required: true, message: '请选择客户', trigger: 'change'}
  ]
}

const saveLoading = ref(false)

/**
 * 保存
 */
async function handleSave() {
  saveLoading.value = true
  try {
    await formRef.value.validate()
    const contractIds = edit.config.value['contractIds']
    const params = commonAddFlag.value ? {...formData.value, bodyList: bodyList.value.getAddList(), contractIds}
      : formData.value
    const res = await (commonAddFlag.value ? insertHead(params) : updateHead(edit.config.value['headId'], params))
    if (res.success) {
      message.success(commonAddFlag.value ? '新增成功' : "修改成功")
      // 新增状态更改为修改状态
      if (commonAddFlag.value) {
        edit.toEdit(res.data)
        bodyList.value.getList()
      }
      formData.value = res.data
    } else {
      // 重新选择购销合同
      if (commonAddFlag.value && res.code === 510) {
        edit.back(true, true)
      }
      message.error(res.message)
    }
  } catch (error) {
    console.log('save error', error)
  } finally {
    saveLoading.value = false
  }
}

/**
 * 确认
 */
async function handleConfirm() {
  Modal.confirm({
    title: '提醒',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: '是否确认所选项？',
    onOk: async () => {
      const headId = edit.config.value['headId']
      const updateRes = await updateHead(headId, formData.value)
      if (!updateRes || !updateRes.success) {
        message.error('保存失败')
        return
      }
      formData.value = updateRes.data
      const res = await confirmHead(headId)
      if (res.success) {
        const {confirmTime, dataStatus} = res.data
        formData.value.confirmTime = confirmTime
        formData.value.dataStatus = dataStatus
        bodyList.value.toReadonly()
        message.success('确认成功')
      } else {
        message.error(res.message)
      }
    }
  })
}

onMounted(() => {
  const editConfig = edit.config.value
  // 表单数据初始化
  formData.value = Object.assign(formData.value, editConfig['editData'])
})

defineOptions({
  name: 'OrderNoticeEdit'
})
</script>

<style scoped>
:deep(.cs-submit-btn) {
  padding-bottom: 0;
  margin: 10px 0
}

.head :deep(.ant-card-body) {
  padding-bottom: 0;
}

.body :deep(.ant-card-body) {
  padding-top: 0;
}
</style>
