import { defineAsyncComponent } from "vue"

export default  [
  {
    path: '/tobacco/auxiliaryMaterials/buyContract',
    name: 'AuxiliaryBuyContractList',
    meta: {
      title: '购销合同信息'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "./buyContract/BuyContractList.vue"))
  },
  {
    path: '/tobacco/auxiliaryMaterials/forContract',
    name: 'AuxiliaryForContractList',
    meta: {
      title: '外商合同信息'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "./forContract/ContractList.vue"))
  },
  {
    path: '/tobacco/auxiliaryMaterials/orderNotice',
    name: 'OrderNoticeList',
    meta: {
      title: '订单通知'
    },
    component: defineAsyncComponent(() => import(/* webpackChunkName: "my-chunk-name" */ "./orderNotice/OrderNoticeList.vue"))
  }
]
