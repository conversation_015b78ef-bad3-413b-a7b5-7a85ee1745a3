import {baseColumns} from "@/view/common/baseColumns";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {h, reactive, ref} from "vue";
import {productClassify} from "@/view/common/constant";
import {Tag} from "ant-design-vue";
import {useMerchant} from "@/view/common/useMerchant";
const {baseColumnsExport, baseColumnsShow} = baseColumns()
const { cmbShowRender,formatNumber } = useColumnsRender()
const { merchantOptions,getMerchantOptions } = useMerchant()

await getMerchantOptions()
export function getColumns() {

  const commColumns = reactive([
    'businessType'
    , 'gName'
    , 'productModel'
    , 'specifications'
    , 'importUnitPrice'
    , 'merchantCode'
    , 'status'
    , 'createrBy'
    , 'createrTime'
  ])

// 导出字段设置`
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns,
  ])

  // table表格字段设置
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  // table表格字段设置
  const totalColumns = ref([
    {
      width: 80,
      minWidth:80,
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      align: 'center',
      fixed: 'left',
      resizable:"true",
    },
    {
      title: '业务类型',
      minWidth: 120,
      align: 'center',
      key: 'businessType',
      dataIndex: 'businessType',
      resizable:"true",
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,productClassify.businessType2))
      }
    },
    {
      title: '商品名称',
      minWidth: 120,
      align: 'center',
      key: 'gName',
      dataIndex: 'gName',
      resizable:"true",
    },
    {
      title: '产品型号',
      minWidth: 120,
      align: 'center',
      key: 'productModel',
      dataIndex: 'productModel',
      resizable:"true",
    },
    {
      title: '规格',
      minWidth: 120,
      align: 'center',
      key: 'specifications',
      dataIndex: 'specifications',
      resizable:"true",
    },
    {
      title: '价格',
      minWidth: 120,
      align: 'center',
      key: 'importUnitPrice',
      dataIndex: 'importUnitPrice',
      resizable:"true",
      customRender({text}){
        return formatNumber(text);
      }
    },

    {
      title: '供应商',
      minWidth: 120,
      align: 'center',
      key: 'merchantCode',
      dataIndex: 'merchantCode',
      resizable:"true",
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text, merchantOptions.value))
      }
    },
    {
      title: '单据状态',
      minWidth: 120,
      align: 'center',
      key: 'status',
      dataIndex: 'status',
      resizable:"true",
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,productClassify.dataStatus))
      }
    },
    {
      title: '制单人',
      minWidth: 120,
      align: 'center',
      key: 'createrUserName',
      dataIndex: 'createrUserName',
      resizable:"true",
    },
    {
      title: '制单时间',
      minWidth: 120,
      align: 'center',
      key: 'createrTime',
      dataIndex: 'createrTime',
      resizable:"true",
    },
  ])

  return{
    columnsConfig,
    excelColumnsConfig,
    totalColumns,
    commColumns
  }
}


