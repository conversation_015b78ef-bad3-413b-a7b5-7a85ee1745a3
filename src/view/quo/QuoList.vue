<template>
  <section  class="dc-section">
    <div class="cs-action"  v-show="show">
      <!-- 查询列表区域 -->
      <div class="cs-search">
        <a-card :bordered="false">
          <bread-crumb>
            <div ref="area_head">
              <div class="search-btn">
                <a-button size="small" type="primary" class="cs-margin-right cs-refresh" @click="handlerRefresh" v-show="showSearch">
                  <template #icon>
                    <GlobalIcon type="redo" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" class="cs-margin-right" @click="handlerSearch">
                  {{localeContent('m.common.button.query')}}
                  <template #icon>
                    <GlobalIcon type="search" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" danger class="cs-margin-right cs-warning" @click="handleShowSearch">
                  <template #icon>
                    <GlobalIcon v-show="!showSearch" type="down" style="color:#fff"/>
                    <GlobalIcon v-show="showSearch" type="up" style="color:#fff"/>
                  </template>
                </a-button>
              </div>
            </div>
          </bread-crumb>
          <div class="separateLine"></div>
          <div ref="area_search">
            <div v-show="showSearch">
              <QuoSearch ref="headSearch" />
            </div>
          </div>
        </a-card>
      </div>
      <!-- 操作按钮区域 -->
      <div class="cs-action-btn">
          <div class="cs-action-btn-item" v-has="['yc-cs:qua:add']">
            <a-button size="small" @click="handlerAdd2" >
              <template #icon>
                <GlobalIcon type="plus" style="color:green"/>
              </template>
              {{localeContent('m.common.button.add')}}
            </a-button>
          </div>
          <div class="cs-action-btn-item" v-has="['yc-cs:qua:update']">
            <a-button  size="small"  @click="handlerEdit2">
              <template #icon>
                <GlobalIcon type="form" style="color:orange"/>
              </template>
              {{localeContent('m.common.button.update')}}
            </a-button>
          </div>
          <div class="cs-action-btn-item" v-has="['yc-cs:qua:delete']">
            <a-button  size="small" :loading="deleteLoading" @click="handlerDelete">
              <template #icon>
                <GlobalIcon type="delete" style="color:red"/>
              </template>
              {{localeContent('m.common.button.delete')}}
            </a-button>
          </div>
          <div class="cs-action-btn-item" v-has="['yc-cs:qua:export']">
            <a-button  size="small" :loading="exportLoading" @click="handlerExport">
              <template #icon>
                <GlobalIcon type="folder-open" style="color:orange"/>
              </template>
              {{localeContent('m.common.button.export')}}
            </a-button>
          </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:qua:enable']">
          <a-button size="small" @click="handlerEnable" >
            <template #icon>
              <GlobalIcon type="check" style="color:green"/>
            </template>
            重新启用
          </a-button>
        </div>
          <div class="cs-action-btn-item"  v-has="['yc-cs:qua:valid']">
            <a-button  size="small"  @click="handlerCancellation">
              <template #icon>
                <GlobalIcon type="close-square" style="color:deeppink"/>
              </template>
              作废
            </a-button>
          </div>

        <div class="cs-action-btn-item"  v-has="['yc-cs:qua:paper']">
          <a-button  size="small" :loading="printTippingLoading"  @click="handlerPrintTippingPaper">
            <template #icon>
              <GlobalIcon type="file-excel" style="color:deeppink"/>
            </template>
            接装纸格式报价表
          </a-button>
        </div>
        <div class="cs-action-btn-item"  v-has="['yc-cs:qua:cigarettePaper']">
          <a-button  size="small" :loading="printLoading"  @click="handlerPrintCigarettePaper">
            <template #icon>
              <GlobalIcon type="file-excel" style="color:deeppink"/>
            </template>
            卷烟纸格式报价表
          </a-button>
        </div>



        <div class="cs-action-btn-settings">
          <!-- 自定义显示组件 -->
          <CsTableColSettings
            :resId="tableKey"
            :tableKey="tableKey+'-client_code'"
            :initSettingColumns="originalColumns"
            :showColumnSettings="true"
            @customColumnChange="customColumnChange"
          >
          </CsTableColSettings>
        </div>
      </div>

      <!-- 表格区域 -->
      <div v-if="showColumns && showColumns.length > 0">
        <s-table
          :animate-rows="false"
          ref="tableRef"
          class="cs-action-item"
          size="small"
          :scroll="{ y: tableHeight,x:400 }"
          bordered
          column-drag
          :custom-row="customRow"
          :pagination="false"
          :columns="showColumns.length > 0 ?showColumns:totalColumns"
          :data-source="dataSourceList"
          :row-selection="{ selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          row-key="sid"
        >
          <!-- 操作 -->
          <template #bodyCell="{ column,record }">
            <template v-if="column.key === 'operation'">
              <div class="operation-container">
                <a-button
                  size="small"
                  type="link"
                  @click="handleEditByRow(record)"
                  :style="operationEdit('edit')"
                >
                  <template #icon>
                    <GlobalIcon type="form" style="color:#e93f41"/>
                  </template>
                </a-button>
                <a-button
                  size="small"
                  type="link"
                  @click="handleViewByRow(record)"
                  :style="operationEdit('view')"
                >
                  <template #icon>
                    <GlobalIcon type="search" style="color:#1677ff"/>
                  </template>
                </a-button>
              </div>
            </template>
          </template>
        </s-table>
      </div>
      <!-- 分页 -->
      <div class=cs-pagination           v-if="showColumns && showColumns.length > 0">
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer  :page-size="page.pageSize" :total="page.total"  @change="onPageChange">
          <template #buildOptionText="props">
            <span >{{ props.value }}条/页</span>
          </template>
        </a-pagination>

      </div>
    </div>

    <!-- 新增 编辑数据 -->
    <div v-if="!show">
      <QuoEdit :editConfig="editConfig" @on-back="handlerOnBack" />
    </div>

  </section>


</template>

<script setup>
/* 使用自定义 Hook 函数 */
import {useCommon} from '@/view/common/useCommon'
import {createVNode, onMounted, provide, reactive, ref, watch} from "vue";
import QuoSearch from "./QuoSearch";
import {getColumns} from "./QuoColumns";
import QuoEdit from "./QuoEdit";
import {message, Modal} from "ant-design-vue";
import BreadCrumb from "@/components/breadcrumb/BreadCrumb.vue";
import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
const { totalColumns,commColumns } = getColumns()
import {localeContent} from "@/view/utils/commonUtil";
import ycCsApi from "@/api/ycCsApi";
import CsTableColSettings from "@/components/settings/CsTableColSettings.vue";
import {useRoute} from "vue-router";
import {editStatus} from "@/view/common/constant";
import {deepClone} from "@/view/utils/common";


/* 导入 */
import {ImportIndex} from 'yao-import'
import { useImport } from "@/view/common/useImport"
import {cancelQuoList, deleteQuoList, enableQuoList, insertQuoList, updateQuoList} from "@/api/cs_api_constant";
import {useMerchant} from "@/view/common/useMerchant";
let { importConfig } = useImport()
/* 引入通用方法 */
const {
  editConfig,
  show,
  page,
  showSearch,
  headSearch,
  handleEditByRow,
  handleViewByRow,
  operationEdit,
  onPageChange,
  handleShowSearch,
  handlerSearch,
  dataSourceList,
  tableLoading,
  getTableScroll,
  exportLoading,
  getList,
  ajaxUrl,
  doExport,
  handlerRefresh,
  gridData

} = useCommon()



defineOptions({
  name: 'MerchantHeadList',
});



const importShow = ref(false)

const { merchantOptions, getMerchantOptions } = useMerchant()

onMounted(()=> {


  ajaxUrl.selectAllPage = ycCsApi.quoUrl.bizQuotation.list
  ajaxUrl.exportUrl = ycCsApi.quoUrl.bizQuotation.export

  tableHeight.value = getTableScroll(100,'');

  getList()

  initCustomColumn()



  if (window.fuyun){
    window.fuyun.majesty.util.handleAsyncImport({id:'1913108305329262593',arrangeId:'1917466458095407105'}).then(res=>{
      importConfig = res
      importConfig.businessParam = {
        headId: props.headId
      }
    })
  }


})

const tableHeight = ref('')




/* 选中事件(这里的事件，顺序不能乱，必须key,后面点跟实体，不然回显出问题。) */
const onSelectChange = (selectedRowKeys, rowSelectData) => {
  gridData.selectedData = rowSelectData;
  gridData.selectedRowKeys = selectedRowKeys;
};


/* 按钮loading */
const deleteLoading = ref(false)
const printLoading = ref(false)
const printTippingLoading = ref(false)



/* 返回事件 */
const handlerOnBack = (flag) => {
  show.value = !show.value;
  if (flag){
    getList()
  }
}

/* 新增数据 */
/*const handlerAdd = ()=>{
  editConfig.value.editStatus = editStatus.ADD
  show.value = !show.value;
}*/


/* 编辑数据 */
/*const handlerEdit = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1){
    message.warning('只能选择一条数据')
    return
  }
  editConfig.value.editStatus = editStatus.EDIT
  editConfig.value.editData =  gridData.selectedData[0]

  console.log('editConfig.value.editData', gridData)
  show.value =!show.value;
}*/


/* 删除数据 */
const handlerDelete = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  // 弹出确认框
  Modal.confirm({
    title: '提醒?',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '删除',
    cancelText: '取消',
    content: '确认删除所选项吗？',
    onOk() {
      deleteLoading.value = true
      deleteQuoList(gridData.selectedRowKeys).then(res => {
        if (res.code === 200) {
          message.success("删除成功！")
          getList()
        }
      }).finally(() => {
        deleteLoading.value = false
      })
    },
    onCancel() {

    },
  });

}


/* 打开导入 */
const handlerImport = ()=>{
  importShow.value = !importShow.value
  // 参数外部重置 可以选择在onMounted里面重置 或者 打开时重置
  // importConfig.taskCode = 'base_client_import'
}


/* 导入成功后事件 */
const importSuccess = ()=>{
  importShow.value =!importShow.value
  getList()
}

// 定义格式化封装函数
function formaData(timer) {
  const year = timer.getFullYear()
  const month = timer.getMonth() + 1 // 由于月份从0开始，因此需加1
  const day = timer.getDate()
  const hour = timer.getHours()
  const minute = timer.getMinutes()
  const second = timer.getSeconds()
  return `${pad(year, 4)}${pad(month)}${pad(day)}${pad(hour)}${pad(minute)}${pad(second)}`
}
// 定义具体处理标准
// timeEl 传递过来具体的数值：年月日时分秒
// total 字符串总长度 默认值为2
// str 补充元素 默认值为"0"
function pad(timeEl, total = 2, str = '0') {
  return timeEl.toString().padStart(total, str)
}

const handlerEnable = (value) => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  let filter = gridData.selectedData.filter(item => item.status === '0');
  if (filter.length > 0) {
    message.warning('仅作废数据可操作重新启用')
    return
  }
  // 弹出确认框
  Modal.confirm({
    title: '提醒?',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '重新启用',
    cancelText: '取消',
    content: '确认重新启用所选项吗？',
    onOk() {
      enableQuoList(gridData.selectedRowKeys).then(res => {
        if (res.code === 200) {
          message.success("重新启用成功！")
          getList()
        }
      })
    },
  });
};

const handlerCancellation = (value) => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  let filter = gridData.selectedData.filter(item => item.status === '1');
  if (filter.length > 0) {
    message.warning('仅启用数据可操作作废')
    return
  }
  // 弹出确认框
  Modal.confirm({
    title: '提醒?',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '作废',
    cancelText: '取消',
    content: '确认作废所选项吗？',
    onOk() {
      cancelQuoList(gridData.selectedRowKeys).then(res => {
        if (res.code === 200) {
          message.success("作废成功！")
          getList()
        }
      })
    },
  });
};

/**
 * 打印接装纸格式报价表
 */
const handlerPrintTippingPaper = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  printTippingLoading.value = true
  const params = {
    sids: gridData.selectedRowKeys
  }
  window.majesty.httpUtil.downloadFile(
    ycCsApi.quoUrl.bizQuotation.printTippingPaper, null,params,'post',null
  ).then(res => {

  }).catch(() => {
  }).finally(() => {
    printTippingLoading.value = false
  })
}
/**
 * 打印卷烟纸格式报价表
 */
const handlerPrintCigarettePaper = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  printLoading.value = true
  const params = {
    sids: gridData.selectedRowKeys
  }
  window.majesty.httpUtil.downloadFile(
    ycCsApi.quoUrl.bizQuotation.printCigarettePaper, null,params,'post',null
  ).then(res => {

  }).catch(() => {
  }).finally(() => {
    printLoading.value = false
  })
}

/* 导出事件 */
const handlerExport = () =>{
  // let dateW = new Date()
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  const timestamp = `${year}${month}${day}${hours}${minutes}${seconds}`
  doExport(`报价单信息${timestamp}.xlsx`, totalColumns)
}



/* 自定义设置 */
/* 显示列数据 */
const showColumns =  ref([])

/* 唯一键 */
const tableKey = ref('')
console.log('window.majesty.router',window.majesty.router.patch)
tableKey.value = window.$vueApp ? window.majesty.router.currentRoute.value.path : useRoute().path
const originalColumns = ref()




/* 自定义显示列初始化操作 */
const initCustomColumn = () => {
  // 这里是拷贝是属于
  let tempColumns = deepClone(totalColumns.value)
  let dealColumns = []
  // 使用map遍历会丢失customRender方法，所以使用forEach
  tempColumns.map((item) => {
    let newObj = Object.assign({}, item);
    newObj["visible"] = true;
    // 需要将customRender 方法追加到新对象中
    if (item.customRender) {
      newObj["customRender"] = item.customRender;
    }
    dealColumns.push(newObj);
  });
  //原始列信息
  originalColumns.value = dealColumns;
}



/* 选中visible为true的数据进行显示 */
const customColumnChange = (settingColumns)  => {
  totalColumns.value = settingColumns.filter((item) => item.visible === true);
  showColumns.value = [...totalColumns.value]
  //  深拷贝之前实现 丢失了方法，所以修改了utils的deepClone方法，这里不要了
  // showColumns.value.map((item) => {
  //   let temp = totalColumns.value.find((tempItem) => tempItem.key === item.key);
  //   if (temp && temp.customRender) {
  //     item.customRender = temp.customRender;
  //   }
  // });
}





/* 监控 dataSourceList */
// watch(dataSourceList, (newValue, oldValue) => {
//   showColumns.value = [...totalColumns.value];
//   // 将showColumns的数据属性 和 初始属性进行比对，如果初始属性存在customRender 方法，追加到showColumns中
// },{deep:true})
watch(totalColumns.value, (newValue, oldValue) => {
  if(!window.$vueApp){
    showColumns.value = [...totalColumns.value];
  }else {
    if (newValue.length === 0) {
      showColumns.value = [...totalColumns.value];
    }else {
      showColumns.value = newValue.map((item) => {
        item.visible = true;
        return item;
      })
      totalColumns.value = newValue.map((item) => {
        item.visible = true;
        return item;
      })
    }
  }
},{immediate:true,deep:true})

const editableData = reactive({});
const edit = (sid) => {
  editableData[sid] = { ...dataSourceList.value.find(item => item.sid === sid) };
};
const handlerEdit = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1){
    message.warning('只能选择一条数据')
    return
  }
  editConfig.value.editStatus = editStatus.EDIT
  editConfig.value.editData =  gridData.selectedData[0]
  editableData[gridData.selectedData[0].sid] =  gridData.selectedData[0]
}

const handlerEdit2 = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1){
    message.warning('只能选择一条数据')
    return
  }
  editConfig.value.editStatus = editStatus.EDIT
  editConfig.value.editData =  gridData.selectedData[0]
  show.value =!show.value;
}

// 保存操作
const save = (sid) => {
  // 1. 找到数据源中与当前 sid 匹配的项
  const targetItem = dataSourceList.value.find(item => item.sid === sid);

  // 2. 将临时编辑的数据（editableData[sid]）合并到目标项中
  Object.assign(targetItem, editableData[sid]);

  // 3. 删除临时编辑的数据，清理内存
  delete editableData[sid];
  if (sid.includes("save")){
    //执行保存请求
    insertQuoList(targetItem).then((res)=>{
      if (res.code === 200){
        message.success('新增成功!')
        getList()
      } else {
        message.error(res.message)
        edit(sid)
      }
    })
  }else {
    //执行保存请求
    updateQuoList(sid,targetItem).then((res)=>{
      if (res.code === 200){
        message.success('修改成功!')
        getList()
      }else {
        message.error(res.message)
        edit(sid)
      }
    })
  }


};
// 取消操作
const cancel = (sid) => {
  delete editableData[sid];
  getList()
};
/* 新增数据 */
// const handlerAdd = () => {
//   let sid = new Date().getTime()+"save";
//   const newData = {
//     sid: `${sid}`,
//     paramsType:"CURR",
//     paramsCode:"",
//     paramsName:"",
//     customParamCode:"",
//     customParamName:""
//   };
//   dataSourceList.value.unshift(newData);
//   dataSourceList.value = [].concat(dataSourceList.value);
//   editableData[sid] = { ...dataSourceList.value.find(item => item.sid === sid) };
//   // MerchantGetMerchantCodeClient({}).then((res)=>{
//   //   if (res.code === 200){
//   //     editableData[sid].merchantCode = res.data
//   //   }
//   // })
// };
/* 新增数据 */

const handlerAdd2 = ()=>{
  editConfig.value.editStatus = editStatus.ADD
  show.value = !show.value;
}
const handleChange = (value) => {
  console.log(`selected ${value}`);
};


</script>

<style lang="less" scoped>


</style>
