// import { inject, provide } from 'vue';

const eventBus = {};

function useEventBus() {
  const emitEvent = (eventName, payload) => {
    if (eventBus[eventName]) {
      eventBus[eventName].forEach(callback => callback(payload));
    }
  };

  const onEvent = (eventName, callback) => {
    if (!eventBus[eventName]) {
      eventBus[eventName] = [];
    }
    eventBus[eventName].push(callback);
  };

  return { emitEvent, onEvent };
}

export default useEventBus;
