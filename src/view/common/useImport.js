import { ref } from "vue";
import {axios} from "@/api/request";
import {isNullOrEmpty} from "@/view/utils/common";

export function useImport() {

  const importConfig = ref({
    taskCode: 'base_client_import', // 任务编码
    businessParam: {},    // 自定义业务参数
    systemCode: 'gw3',  // 系统标识
    tradeCode: '',  // 企业编码
    extendKey: '', // 扩展主键
    baseUri: '/api/fyImportBackend',
    httpApi: isNullOrEmpty(window.$vueApp) ? axios : window.$vueApp.axios,  // http_api接口对象
    selfParam: '', // 自定义业务参数
    startRow: '5', // 导入起始行
    endRow: '', // 导入截止行
    importType: '1', // 导入方式
    directImport: '', // 是否直接导入
    sid: '',
    enableTemplateTransfer: undefined, // 启用自定义字段勾选框控制
    businessArrange: {}, // 业务编排id
  })


  return{
    importConfig
  }

}
