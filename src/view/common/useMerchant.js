import { ref } from 'vue'
import ycCs<PERSON>pi from "@/api/ycCsApi"
import { message } from 'ant-design-vue'

export function useMerchant() {
  const merchantOptions = ref([])

  const getMerchantOptions = async () => {
    try {
      const params = {}
      const res = await window.majesty.httpUtil.postAction(
        `${ycCsApi.bizMerchant.list}`,
        params
      );
      if (res.code === 200) {
        merchantOptions.value = res.data.map(item => ({
          value: item.merchantCode,
          label: item.merchantNameCn
        }));
      } else {
        message.error(res.message || '获取客商数据失败');
      }
    } catch (error) {
      message.error('获取客商数据失败');
    }
  }

  const productTypeOptions = ref([])

  const getProductTypeOptions = async () => {
    try {
      const params = {}
      const res = await window.majesty.httpUtil.postAction(
        `${ycCsApi.params.productType.list}`,
        params
      );
      if (res.code === 200) {
        productTypeOptions.value = res.data.map(item => ({
          value: item.categoryCode,
          label: item.categoryName
        }));
      } else {
        message.error(res.message || '获取商品类别失败');
      }
    } catch (error) {
      message.error('获取商品类别失败');
    }
  }

  return {
    merchantOptions,
    getMerchantOptions,
    productTypeOptions,
    getProductTypeOptions
  }
}
