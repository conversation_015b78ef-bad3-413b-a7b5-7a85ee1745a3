import {reactive, ref} from "vue";

export function baseColumns() {

  const baseColumnsExport = reactive(['sid'])
  const baseColumnsShow = reactive(['selection', 'operation', 'sid'])


  // 返回状态 和 方法
  return{
    baseColumnsExport,
    baseColumnsShow,
  }

}

// 通用排序函数
export const createSorter = (field) => ({
  sorter: {
    compare: (a, b) => {
      if (!a[field] && !b[field]) return 0;
      if (!a[field]) return -1;
      if (!b[field]) return 1;
      return a[field] > b[field] ? 1 : -1;
    }
  }
});

// 日期类型的排序
export const createDateSorter = (field) => ({
  sorter: {
    compare: (a, b) => {
      if (!a[field] && !b[field]) return 0;
      if (!a[field]) return -1;
      if (!b[field]) return 1;
      return new Date(a[field]) - new Date(b[field]);
    }
  }
});

// 数字类型的排序
export const createNumberSorter = (field) => ({
  sorter: {
    compare: (a, b) => {
      if (!a[field] && !b[field]) return 0;
      if (!a[field]) return -1;
      if (!b[field]) return 1;
      return Number(a[field]) - Number(b[field]);
    }
  }
});
