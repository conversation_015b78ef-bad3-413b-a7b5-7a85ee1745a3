import {baseColumns} from "@/view/common/baseColumns";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {h, reactive, ref} from "vue";
import {productClassify} from "@/view/common/constant";
import {Tag} from "ant-design-vue";
const {baseColumnsExport, baseColumnsShow} = baseColumns()
const { cmbShowRender } = useColumnsRender()


export function getColumns() {

  const commColumns = reactive([
    'customerType',
    'customerCode',
    'companyName',
    'customsCreditRating',
    'inspectionCode',
    'creditCode',
    'declareCode',
    'companyNameShort',
    'telephoneNo',
    'linkmanName',
    'linkmanDuty',
    'mobilePhone',
    'eMail',
    'address',
    'companyNameEn',
    'countryEn',
    'areaEn',
    'cityEn',
    'addressEn',
    'telephoneNoEn',
    'linkmanNameEn',
    'mobilePhoneEn',
    'eMailEn',
    'note',
    'insertUser',
    'insertTime',
    'updateUser',
    'updateTime',
    'tradeCode',
    'sid',
    'linkmanDutyEn',
    'aeoCode',
    'masterCustoms',
    'fax',
    'postal',
    'country',
    'area',
    'city',
    'invoiceAddress',
    'invoiceAddressEn',
    'deliverAddress',
    'deliverAddressEn',
    'status',
    'insertUserName',
    'updateUserName',
    'authorize',
    'authorizeDeadline',
    'freeAddress',
    'freeProperties',
    'costCenter',
    'decPersonnel',
    'decPersonnelTel'
  ])

// 导出字段设置`
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])

  // table表格字段设置
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  // table表格字段设置
  const totalColumns = ref([
    {
      title: '操作',
      maxWidth:80,
      width:80,
      dataIndex: 'operation',
      key: 'operation',
      align: 'center',
      fixed: 'left',
    },
    {
      title: '状态',
      width: 150,
      minWidth: 150,
      align: 'center',
      dataIndex: 'status',
      key: 'status',
      customRender: ({ text }) => {
        const tagColor = text === '1' ? 'error' : 'success';
        return h(Tag,{ color:tagColor }, cmbShowRender(text,productClassify.status))
      }
    },
    {
      title: '供应商代码',
      width: 220,
      minWidth: 220,
      align: 'center',
      dataIndex: 'customerCode',
      key: 'customerCode',
    },
    {
      title: '供应商名称',
      width: 220,
      minWidth: 220,
      align: 'center',
      dataIndex: 'companyName',
      key: 'companyName',
    },
    {
      title: '英文名称',
      width: 150,
      minWidth: 150,
      align: 'center',
      dataIndex: 'companyNameEn',
      key: 'companyNameEn',
    },
    {
      title: '联系人',
      width: 250,
      minWidth: 250,
      align: 'center',
      dataIndex: 'linkmanName',
      key: 'linkmanName',
    },
    {
      title: '联系人电话',
      width: 150,
      minWidth: 150,
      align: 'center',
      dataIndex: 'mobilePhone',
      key: 'mobilePhone',
    },
    {
      title: '中文地址',
      width: 350,
      minWidth: 350,
      align: 'center',
      dataIndex: 'address',
      key: 'address',
    },
    {
      title: '英文地址',
      width: 350,
      minWidth: 350,
      align: 'center',
      dataIndex: 'addressEn',
      key: 'addressEn',
    },
    {
      title: '创建人',
      width: 150,
      minWidth: 150,
      align: 'center',
      dataIndex: 'insertUser',
      key: 'insertUser',
    },
    {
      title: '创建日期',
      width: 150,
      minWidth: 150,
      align: 'center',
      dataIndex: 'insertTime',
      key: 'insertTime',
    }
  ])

  return{
    columnsConfig,
    excelColumnsConfig,
    totalColumns
  }
}


