<template>
  <a-form  layout="inline"  label-align="right"  :label-col="{ style: { width: '100px' } }" :model="searchParam"   class="cs-form  grid-container" >
    <!-- 客户代码 -->
    <a-form-item name="customerCode"   :label="'客户代码'" class="grid-item-search "  :colon="false">
      <a-input  size="small" v-model:value="searchParam.customerCode" />
    </a-form-item>

    <!-- 客户中文名称 -->
    <a-form-item name="companyName"   :label="'客户中文名称'" class="grid-item-search"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.companyName" />
    </a-form-item>

    <!-- 商检代码 -->
    <a-form-item name="inspectionCode"   :label="'商检代码'" class="grid-item-search"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.inspectionCode" />
    </a-form-item>

    <!-- 企业名称缩写 -->
    <a-form-item name="companyNameShort"   :label="'企业名称缩写'" class="grid-item-search"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.companyNameShort" :colon="false" />
    </a-form-item>

    <!-- 更新时间 -->
    <a-form-item name="insertTime" label="更新时间" class="grid-item-search" :colon="false">
      <!-- Warning: [ant-design-vue: Form.Item] FormItem can only collect one field item,
            you haved set ASelect, ASelect, AInputNumber, AInputNumber, AInput 5 field items. You can set not need to be collected fields into a-form-item-rest
      -->
      <a-form-item-rest>
        <a-row>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.insertTimeFrom"
              id="insertTimeFrom"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
            />
          </a-col>
          <a-col :span="2" style="text-align: center">
            -
          </a-col>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.insertTimeTo"
              size="small"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD"
              :locale="locale"
              style="width: 100%"
              placeholder=""
            />
          </a-col>
        </a-row>
      </a-form-item-rest>

    </a-form-item>


    <!-- 数据状态 -->
    <a-form-item name="status"   :label="'数据状态'" class="grid-item-search"  :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="searchParam.status" id="authorize">
        <a-select-option   class="cs-select-dropdown"  v-for="item in productClassify.status"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
          {{item.value}} {{item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>
  </a-form>
</template>

<script setup>
import {inject, onMounted, reactive} from 'vue'
import {productClassify} from "@/view/common/constant";
import CsSelect from "@/components/select/CsSelect.vue";

defineOptions({
  name: 'SupplierInfoSearch'
})
const searchParam = reactive({
  customerType:'',
  customerCode:'',
  companyName:'',
  customsCreditRating:'',
  inspectionCode:'',
  creditCode:'',
  declareCode:'',
  companyNameShort:'',
  telephoneNo:'',
  linkmanName:'',
  linkmanDuty:'',
  mobilePhone:'',
  eMail:'',
  address:'',
  companyNameEn:'',
  countryEn:'',
  areaEn:'',
  cityEn:'',
  addressEn:'',
  telephoneNoEn:'',
  linkmanNameEn:'',
  mobilePhoneEn:'',
  eMailEn:'',
  note:'',
  insertUser:'',
  insertTime:'',
  updateUser:'',
  updateTime:'',
  tradeCode:'',
  sid:'',
  linkmanDutyEn:'',
  aeoCode:'',
  masterCustoms:'',
  fax:'',
  postal:'',
  country:'',
  area:'',
  city:'',
  invoiceAddress:'',
  invoiceAddressEn:'',
  deliverAddress:'',
  deliverAddressEn:'',
  status:'',
  insertUserName:'',
  updateUserName:'',
  authorize:'',
  authorizeDeadline:'',
  freeAddress:'',
  freeProperties:'',
  costCenter:'',
  decPersonnel:'',
  decPersonnelTel:'',
  insertTimeFrom:'',
  insertTimeTo:'',
})
/* 定义重置方法(注意前后顺序) */
const resetSearch = () => {
  Object.keys(searchParam).forEach(key => {
    searchParam[key] = '';
  });
}
defineExpose({searchParam,resetSearch});
onMounted(() => {

});





</script>
