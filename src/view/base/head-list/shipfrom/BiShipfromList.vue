 <template>
  <section  class="dc-section">
    <div class="cs-action"  v-show="show">
      <!-- 查询列表区域 -->
      <div class="cs-search">
        <a-card :bordered="false">
          <bread-crumb>
            <div ref="area_head">
              <div class="search-btn">
                <a-button size="small" type="primary" class="cs-margin-right cs-refresh" @click="handlerRefresh" v-show="showSearch">
                  <template #icon>
                    <GlobalIcon type="redo" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" class="cs-margin-right" @click="handlerSearch">
                  {{localeContent('m.common.button.query')}}
                  <template #icon>
                    <GlobalIcon type="search" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" danger class="cs-margin-right cs-warning" @click="handleShowSearch">
                  <template #icon>
                    <GlobalIcon v-show="!showSearch" type="down" style="color:#fff"/>
                    <GlobalIcon v-show="showSearch" type="up" style="color:#fff"/>
                  </template>
                </a-button>
              </div>
            </div>
          </bread-crumb>
          <div class="separateLine"></div>
          <!-- 查询按钮组件 -->
          <div ref="area_search">
            <div v-show="showSearch">
              <bi-shipfrom-search :head-id="headId" ref="headSearch" />
            </div>
          </div>
        </a-card>
      </div>
      <!-- 操作按钮区域 -->
      <div class="cs-action-btn">
          <div class="cs-action-btn-item" v-has="['yc-cs:shipfrom:add']"  v-if="props.operationStatus !== editStatus.SHOW">
            <a-button size="small" @click="handlerAdd" >
              <template #icon>
                <GlobalIcon type="plus" style="color:green"/>
              </template>
              {{localeContent('m.common.button.add')}}
            </a-button>
          </div>
          <div class="cs-action-btn-item" v-has="['yc-cs:shipfrom:edit']" v-if="props.operationStatus !== editStatus.SHOW">
            <a-button  size="small"  @click="handlerEdit">
              <template #icon>
                <GlobalIcon type="form" style="color:orange"/>
              </template>
              {{localeContent('m.common.button.update')}}
            </a-button>
          </div>
          <div class="cs-action-btn-item" v-has="['yc-cs:shipfrom:delete']" v-if="props.operationStatus !== editStatus.SHOW">
            <a-button  size="small" :loading="deleteLoading" @click="handlerDelete">
              <template #icon>
                <GlobalIcon type="delete" style="color:red"/>
              </template>
              {{localeContent('m.common.button.delete')}}
            </a-button>
          </div>
          <div class="cs-action-btn-item" v-has="['yc-cs:shipfrom:export']">
            <a-button  size="small" :loading="exportLoading" @click="handlerExport">
              <template #icon>
                <GlobalIcon type="folder-open" style="color:orange"/>
              </template>
              {{localeContent('m.common.button.export')}}
            </a-button>
          </div>
          <div class="cs-action-btn-item"  v-has="['yc-cs:shipfrom:import']" v-if="props.operationStatus !== editStatus.SHOW">
            <a-button  size="small"  @click="handlerImport">
              <template #icon>
                <GlobalIcon type="file-excel" style="color:deepskyblue"/>
              </template>
              {{localeContent('m.common.button.import')}}
            </a-button>
          </div>

        <div class="cs-action-btn-settings">
          <!-- 自定义显示组件 -->
          <CsTableColSettings
            :resId="tableKey"
            :tableKey="tableKey+'-bi_shipfrom'"
            :initSettingColumns="originalColumns"
            :showColumnSettings="true"
            @customColumnChange="customColumnChange"
          >
          </CsTableColSettings>
        </div>


      </div>

      <!-- 表格区域 -->
      <div v-if="showColumns && showColumns.length > 0">
        <s-table
          ref="tableRef"
          class="cs-action-item-inner"
          size="small"
          :scroll="{ y: tableHeight,x:400 }"
          bordered
          :pagination="false"
          :columns="showColumns.length > 0 ?showColumns:totalColumns"
          :data-source="dataSourceList"
          :row-selection="{  selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          row-key="sid"
        >
          <!-- 操作 -->
          <template #bodyCell="{ column,record }">
            <template v-if="column.key === 'operation'">
              <div class="operation-container">
                <a-button
                  v-if="props.operationStatus !== editStatus.SHOW"
                  size="small"
                  type="link"
                  @click="handleEditByRow(record)"
                  :style="operationEdit('edit')"
                >
                  <template #icon>
                    <GlobalIcon type="form" style="color:#e93f41"/>
                  </template>
                </a-button>
                <a-button
                  size="small"
                  type="link"
                  @click="handleViewByRow(record)"
                  :style="operationEdit('view')"
                >
                  <template #icon>
                    <GlobalIcon type="search" style="color:#1677ff"/>
                  </template>
                </a-button>

              </div>
            </template>
          </template>
        </s-table>
      </div>

      <!-- 分页 -->
      <div class="cs-pagination" v-if="showColumns && showColumns.length > 0">
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer  :page-size="page.pageSize" :total="page.total"  @change="onPageChange">
          <template #buildOptionText="props">
            <span >{{ props.value }}条/页</span>
          </template>
        </a-pagination>

      </div>
    </div>

    <!-- 新增 编辑数据 -->
    <div v-if="!show">
      <bi-shipfrom-edit :head-id="headId" :editConfig="editConfig" @on-back="handlerOnBack" />
    </div>


    <!-- 导入数据 -->
    <ImportIndex :importShow="importShow" :importConfig="importConfig"   @onImportSuccess="importSuccess"></ImportIndex>


  </section>


</template>

<script setup>
/* 使用自定义 Hook 函数 */
import {useCommon} from '@/view/common/useCommon'
import {createVNode, nextTick, onMounted, provide, reactive, ref, watch} from "vue";
import {getColumns} from "@/view/base/head-list/shipfrom/BiShipfromColumns";
import {message, Modal} from "ant-design-vue";
import BreadCrumb from "@/components/breadcrumb/BreadCrumb.vue";
import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
import {deleteShipfrom} from "@/api/bi/bi_client_info";
const { totalColumns } = getColumns()
import {ImportIndex} from 'yao-import'
import {localeContent} from "@/view/utils/commonUtil";
import { useImport } from "@/view/common/useImport";
import ycCsApi from "@/api/ycCsApi";
import CsTableColSettings from "@/components/settings/CsTableColSettings.vue";
import {useRoute} from "vue-router";
import {editStatus} from "@/view/common/constant";
import BiShipfromEdit from "@/view/base/head-list/shipfrom/BiShipfromEdit.vue";
import BiShipfromSearch from "@/view/base/head-list/shipfrom/BiShipfromSearch.vue";
import {deepClone} from "@/view/utils/common";
const { importConfig } = useImport()


/* 引入通用方法 */
const {
  editConfig,
  show,
  page,
  showSearch,
  headSearch,
  handleEditByRow,
  handleViewByRow,
  operationEdit,
  onPageChange,
  handleShowSearch,
  handlerSearch,
  dataSourceList,
  tableLoading,
  getTableScroll,
  exportLoading,
  getList,
  ajaxUrl,
  doExport,
  handlerRefresh,
  gridData,
  onSelectChange

} = useCommon()



defineOptions({
  name: 'BiShipfromList',
});

const props = defineProps({

  /* 表头headId */
  headId: {
    type: String,
    default: ''
  },
  /* 表头传入状态 查看/编辑 */
  operationStatus: {
    type: String,
    default: ''
  }
})



const importShow = ref(false)



onMounted( () => {


  ajaxUrl.selectAllPage = ycCsApi.biShipfrom.list
  ajaxUrl.exportUrl = ycCsApi.biShipfrom.export
  tableHeight.value = getTableScroll(190,'');
  getList()

  // 重新加载 totalColumns
  console.log('aaaaaaaaaaaaaaa',totalColumns.value)
  initCustomColumn()



})

const tableHeight = ref('')




// /* 选中事件(这里的事件，顺序不能乱，必须key,后面点跟实体，不然回显出问题。) */
// const onSelectChange = (selectedRowKeys, rowSelectData) => {
//   gridData.selectedData = rowSelectData;
//   gridData.selectedRowKeys = selectedRowKeys;
// };


/* 按钮loading */
const deleteLoading = ref(false)




/* 返回事件 */
const handlerOnBack = (flag) => {
  show.value = !show.value;
  if (flag){
    getList()
  }
}



/* 新增数据 */
const handlerAdd = ()=>{
  editConfig.value.editStatus = editStatus.ADD
  show.value = !show.value;
}


/* 编辑数据 */
const handlerEdit = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1){
    message.warning('只能选择一条数据')
    return
  }
  editConfig.value.editStatus = editStatus.EDIT
  editConfig.value.editData =  gridData.selectedData[0]

  show.value =!show.value;
}


/* 删除数据 */
const handlerDelete = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  // 弹出确认框
  Modal.confirm({
    title: '提醒?',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '删除',
    cancelText: '取消',
    content: '确认删除所选项吗？',
    onOk() {
      deleteLoading.value = true
      deleteShipfrom(gridData.selectedRowKeys).then(res => {
        if (res.code === 200) {
          message.success("删除成功！")
          getList()
        }
      }).finally(() => {
        deleteLoading.value = false
      })
    },
    onCancel() {

    },
  });

}


/* 打开导入 */
const handlerImport = ()=>{
  importShow.value = !importShow.value
  // 参数外部重置 可以选择在onMounted里面重置 或者 打开时重置
  importConfig.taskCode = 'base_client_import'
}


/* 导入成功后事件 */
const importSuccess = ()=>{
  importShow.value =!importShow.value
  getList()
}


/* 导出事件 */
const handlerExport = () =>{
  doExport('测试文件导出.xlsx',totalColumns)
}



/* 自定义设置 */
/* 显示列数据 */
const showColumns =  ref([])

/* 唯一键 */
const tableKey = ref('');
/* 注意 这里一定要和父子组件的tableKey一直，不然会导致组件加载有问题，甚至拿不到 columns的数据 */
// tableKey.value = window.$vueApp ? window.majesty.router.patch : useRoute().path+'-shipfrom' 错误写法
tableKey.value = window.$vueApp ? window.majesty.router.patch : useRoute().path
const originalColumns = ref()

/* 自定义显示列初始化操作 */
const initCustomColumn = () => {
  console.log('originalColumns-totalColumns',totalColumns.value)
  // 这里是拷贝是属于
  let tempColumns = deepClone(totalColumns.value)
  let dealColumns = []
  // 使用map遍历会丢失customRender方法，所以使用forEach
  tempColumns.map((item) => {
    let newObj = Object.assign({}, item);
    newObj["visible"] = true;
    // 需要将customRender 方法追加到新对象中
    if (item.customRender) {
      newObj["customRender"] = item.customRender;
    }
    dealColumns.push(newObj);
  });
  //原始列信息
  originalColumns.value = dealColumns;

  console.log('originalColumns',originalColumns.value)
}


/* 选中visible为true的数据进行显示 */
const customColumnChange = (settingColumns)  => {
  totalColumns.value = settingColumns.filter((item) => item.visible === true);
  showColumns.value = [...totalColumns.value]
  console.log('BIFROM',showColumns.value)
}


/* 监控 dataSourceList */
watch(dataSourceList, (newValue, oldValue) => {
  showColumns.value = [...totalColumns.value];
  // 将showColumns的数据属性 和 初始属性进行比对，如果初始属性存在customRender 方法，追加到showColumns中
  console.log('BIFROM',showColumns.value)
},{deep:true})



</script>

<style lang="less" scoped>


</style>
