<template>
  <a-form layout="inline"  label-align="right"  :label-col="{ style: { width: '120px' } }" :model="searchParam"   class="pw-form grid-container" >

    <!--  shipFrom代码  -->
    <a-form-item name="shipFromCode"  label="ship from 代码" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.shipFromCode" />
    </a-form-item>

    <!--  shipFrom代码  -->
    <a-form-item name="shipFromName"  label="ship from 名称" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.shipFromName" />
    </a-form-item>
    <!--  创建日期   -->
    <a-form-item name="insertTime" label="创建日期"  class="grid-item" :colon="false">
      <!-- Warning: [ant-design-vue: Form.Item] FormItem can only collect one field item,
            you haved set ASelect, ASelect, AInputNumber, AInputNumber, AInput 5 field items. You can set not need to be collected fields into a-form-item-rest
      -->
      <a-form-item-rest>
        <a-row>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.insertTimeFrom"
              id="insertTimeForm"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
            />
          </a-col>
          <a-col :span="2" style="text-align: center">
            -
          </a-col>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.insertTimeTo"
              id="insertTimeTo"
              size="small"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD"
              :locale="locale"
              style="width: 100%"
              placeholder=""
            />
          </a-col>
        </a-row>
      </a-form-item-rest>
    </a-form-item>


  </a-form>
</template>

<script setup>
import {inject, onMounted, reactive} from 'vue'

defineOptions({
  name: 'BiShipfromSearch'
})

const props = defineProps({
  headId:{
    type: String,
    default: ''
  }
})

/* 定义重置方法(注意前后顺序) */
const resetSearch = () => {
  Object.keys(searchParam).forEach(key => {
    searchParam[key] = '';
  });
  searchParam.headId = props.headId
}

/* 定义搜索参数 */
const searchParam = reactive({
  sid:'',
  shipFromCode:'',
  shipFromName:'',
  shipFromAddress:'',
  remark:'',
  insertUser:'',
  insertTime:'',
  updateUser:'',
  updateTime:'',
  tradeCode:'',
  headId:props.headId,
  insertUserName:'',
  updateUserName:'',
  supplierCode:'',
  insertTimeFrom:'',
  insertTimeTo:''
})
defineExpose({searchParam,resetSearch});

onMounted(() => {

  // 1.自定义显示列当全部取消选择时，对应的table列并没有清空
  // 2.自定义显示列当全部取消选择时，会保留一个selection列，这里的列也是需要清空的。
  // 3.当点击重置时，界面的table列放生了变化，但是此时的自定义设置中的选择框 应该是全选
  // 4.当界面加载时，对应的table显示，但是对应的自定义选项的字段属性，是一个未选的状态
});


</script>

<style lang='less' scoped>

</style>
