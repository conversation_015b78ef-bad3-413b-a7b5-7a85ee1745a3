import {baseColumns} from "@/view/common/baseColumns";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {h, reactive, ref} from "vue";
import {productClassify} from "@/view/common/constant";
import {Tag} from "ant-design-vue";
const {baseColumnsExport, baseColumnsShow} = baseColumns()
const { cmbShowRender } = useColumnsRender()




export function getColumns() {

  const commColumns = reactive([
    'sid',
    'shipFromCode',
    'shipFromName',
    'shipFromAddress',
    'remark',
    'insertUser',
    'insertTime',
    'updateUser',
    'updateTime',
    'tradeCode',
    'headId',
    'insertUserName',
    'updateUserName',
    'supplierCode'
  ])

  /* 导出字段设置 */
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])

  /* table表格字段设置 */
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  /* table表格字段属性设置 */
  const totalColumns = ref([
    {
      maxWidth:80,
      width:80,
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      align: 'center',
      fixed: 'left',
    },

    {
      title: 'ship from 代码',
      width: 200,
      minWidth: 200,
      align: 'center',
      dataIndex: 'shipFromCode',
      key: 'shipFromCode',
    },
    {
      title: 'ship from 名称',
      width: 200,
      minWidth: 200,
      align: 'center',
      dataIndex: 'shipFromName',
      key: 'shipFromName',
    },
    {
      title: 'ship from 详细信息',
      minWidth: 200,
      align: 'center',
      dataIndex: 'shipFromAddress',
      key: 'shipFromAddress',
    },
    {
      title: '创建人',
      width: 200,
      minWidth: 200,
      align: 'center',
      dataIndex: 'insertUser',
      key: 'insertUser',
    },
    {
      title: '创建日期',
      width: 200,
      minWidth: 200,
      align: 'center',
      dataIndex: 'insertTime',
      key: 'insertTime'
    }

  ])

  return{
    columnsConfig,
    excelColumnsConfig,
    totalColumns
  }
}
