<template>
  <section>
    <a-card size="small" title="供应商基础信息" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData"   class=" grid-container">
            <!-- shipFrom代码 -->
            <a-form-item name="shipFromCode"  :label="'ship from 代码'" class="grid-item"  :colon="false">
                <a-input :disabled="showDisable" class="ant-input-covered-yellow"  size="small" v-model:value="formData.shipFromCode" />
            </a-form-item>
            <!-- shipFrom名称 -->
            <a-form-item name="shipFromName"   :label="'ship from 名称'" class="grid-item"  :colon="false">
                <a-input :disabled="showDisable" class="ant-input-covered-yellow"  size="small" v-model:value="formData.shipFromName" />
            </a-form-item>
            <!-- shipFrom地址 -->
            <a-form-item name="shipFromAddress"   :label="'ship from 地址'" class="grid-item merge-3"  :colon="false">
                <a-textarea :rows="4" :disabled="showDisable"  size="small" v-model:value="formData.shipFromAddress"  :autosize="{ minRows: 2, maxRows: 6 }"/>
            </a-form-item>
            <!-- 备注 -->
            <a-form-item name="remark"   :label="'备注'" class="grid-item merge-3"  :colon="false">
                <a-textarea :rows="4" :disabled="showDisable"  size="small" v-model:value="formData.remark" :autosize="{ minRows: 2, maxRows: 6 }" />
            </a-form-item>
          <div class="cs-submit-btn merge-3">
            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right"
                      v-show="props.editConfig.editStatus !== editStatus.SHOW ">保存
            </a-button>
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(false)">返回</a-button>
          </div>
        </a-form>
      </div>
    </a-card>


  </section>
</template>

<script setup>
import {editStatus, productClassify} from '@/view/common/constant'
import {message} from "ant-design-vue";
import {onMounted, reactive, ref} from "vue";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
import {insertShipfrom, updateShipfrom} from "@/api/bi/bi_client_info";
const { getPCode } = usePCode()



const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  },
  headId: {
    type: String,
    default: ''
  }
});

// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onBack']);

const onBack = (val) => {
  emit('onBack', val);
};

// 是否禁用
const showDisable = ref(false)

// 表单数据
const formData = reactive({
    // 主键
    sid:'',
    // shipFrom代码
    shipFromCode:'',
    // shipFrom名称
    shipFromName:'',
    // shipFrom地址
    shipFromAddress:'',
    // 备注
    remark:'',
    // 创建人
    insertUser:'',
    // 创建日期
    insertTime:'',
    // 修改人
    updateUser:'',
    // 修改时间
    updateTime:'',
    // 所属企业编码
    tradeCode:'',
    // 供应商SID
    headId:props.headId,
    // 制单人姓名
    insertUserName:'',
    // 修改人姓名
    updateUserName:'',
    // 供应商CODE
    supplierCode:''
})
// 校验规则
const rules = {
    sid:[
        {max: 40, message: '主键长度不能超过 40位字节', trigger: 'blur'}
    ],
    shipFromCode:[
        {required: true, message: 'shipFrom代码不能为空', trigger: 'blur'},
        {max: 50, message: 'shipFrom代码长度不能超过 50位字节', trigger: 'blur'}
    ],
    shipFromName:[
        {required: true, message: 'shipFrom名称不能为空', trigger: 'blur'},
        {max: 255, message: 'shipFrom名称长度不能超过 255位字节', trigger: 'blur'}
    ],
    shipFromAddress:[
        {max: 512, message: 'shipFrom地址长度不能超过 512位字节', trigger: 'blur'}
    ],
    remark:[
        {max: 255, message: '备注长度不能超过 255位字节', trigger: 'blur'}
    ],
    insertUser:[
        {max: 50, message: '创建人长度不能超过 50位字节', trigger: 'blur'}
    ],
    insertTime:[
    ],
    updateUser:[
        {max: 50, message: '修改人长度不能超过 50位字节', trigger: 'blur'}
    ],
    updateTime:[
    ],
    tradeCode:[
        {max: 20, message: '所属企业编码长度不能超过 20位字节', trigger: 'blur'}
    ],
    headId:[
        {max: 50, message: '供应商SID长度不能超过 50位字节', trigger: 'blur'}
    ],
    insertUserName:[
        {max: 50, message: '制单人姓名长度不能超过 50位字节', trigger: 'blur'}
    ],
    updateUserName:[
        {max: 50, message: '修改人姓名长度不能超过 50位字节', trigger: 'blur'}
    ],
    supplierCode:[
        {max: 50, message: '供应商CODE长度不能超过 50位字节', trigger: 'blur'}
    ]
}

const pCode = ref('')
// 初始化操作
onMounted(() => {
  getPCode().then(res=>{
    console.log('res',res)
    pCode.value = res;
  })
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    showDisable.value = false
    Object.assign(formData, {});
  }
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = true
  }
});



// vue3中使用ref,需要先定义，然后在模板中使用。因为在模板渲染之前，DOM 元素还不存在。
const formRef = ref(null);
// 保存
const handlerSave = () => {
  formRef.value
    .validate()
    .then(() => {
      if (props.editConfig && props.editConfig.editStatus === editStatus.ADD){
        insertShipfrom(formData).then((res)=>{
          if (res.code === 200){
            message.success('新增成功!')
            onBack(true)
          }
        })
      }else if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT){
        updateShipfrom(formData.sid,formData).then((res)=>{
          if (res.code === 200){
            message.success('修改成功!')
            onBack(true)
          }
        })
      }
    })
    .catch(error => {
      console.log('validate failed', error);
    })
};

</script>

<style lang="less" scoped>


</style>



