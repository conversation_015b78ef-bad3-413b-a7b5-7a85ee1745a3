import { defineAsyncComponent } from "vue"

export default  [
  {
    path: '/tobacco/baseHead',
    name: 'BaseHeadList',
    meta: {
      title: '客户基础信息'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "./head/BaseHeadList.vue"))
  },



  {
    path: '/tobacco/supplierInfo',
    name: 'SupplierInfoList',
    meta: {
      title: '供应商基础信息'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "./head-list/SupplierInfoList.vue"))
  },
  {
    path: '/tobacco/merchant',
    name: 'MerchantHeadList',
    meta: {
      title: '客商信息'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "./bi-merchant/MerchantHeadList.vue"))
  },
  {
    path: '/tobacco/materialInformation',
    name: 'MaterialInformationHeadList',
    meta: {
      title: '物料信息'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "./bi-biMaterialInformation/MaterialInformationHeadList.vue"))
  },
]
