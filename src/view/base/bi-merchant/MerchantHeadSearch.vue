<template>
  <a-form  layout="inline"  label-align="right"  :label-col="{ style: { width: '100px' } }" :model="searchParam"   class="cs-form  grid-container" >
    <!-- 客商编码 -->
    <a-form-item name="merchantCode"   :label="'客商编码'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.merchantCode" />
    </a-form-item>

    <!-- 客商中文名称 -->
    <a-form-item name="merchantNameCn"   :label="'客商中文名称'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.merchantNameCn" />
    </a-form-item>

    <!-- 客商英文名称 -->
    <a-form-item name="merchantNameEn"   :label="'客商英文名称'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.merchantNameEn" />
    </a-form-item>

<!--    &lt;!&ndash; 客商简称 &ndash;&gt;-->
<!--    <a-form-item name="merchantShort"   :label="'客商简称'" class="grid-item"  :colon="false">-->
<!--      <a-input  size="small" v-model:value="searchParam.merchantShort" :colon="false" />-->
<!--    </a-form-item>-->


  </a-form>
</template>

<script setup>
import {inject, onMounted, reactive} from 'vue'
import {productClassify} from "@/view/common/constant";
import CsSelect from "@/components/select/CsSelect.vue";

defineOptions({
  name: 'BaseHeadSearch'
})
const searchParam = reactive({

  merchantCode:'',
  merchantNameCn:'',
  merchantNameEn:'',
})
/* 定义重置方法(注意前后顺序) */
const resetSearch = () => {
  Object.keys(searchParam).forEach(key => {
    searchParam[key] = '';
  });
}
defineExpose({searchParam,resetSearch});
onMounted(() => {

});





</script>
