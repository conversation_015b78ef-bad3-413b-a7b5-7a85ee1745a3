<template>

  <div v-if="dataSource.length > 0">
    <s-table
      rowKey="sid"
      :bordered="true"
      size="small"
      :columns="columns"
      :scroll="{ y: tableHeight ,x:100}"
      :pagination="false"
      :data-source="dataSource"
      :sticky="true"
    ></s-table>
  </div>

</template>


<script setup>
import {onMounted, reactive, ref} from "vue";

const columns = ref([
  {
    title: 'Full Name',
    dataIndex: 'name',
    key: 'name',
    width: 150,
    align: "center"
  },
  {
    title: 'Age',
    dataIndex: 'age',
    key: 'age',
    width: 150,
    align: "center"
  },
  {
    title: 'Column 1',
    dataIndex: 'address',
    key: 'address',
    width: 150,
    align: "center"
  }
])

/* 数据 */
const dataSource =  reactive([]);

/* 表格高度 */
const tableHeight = ref('')

onMounted(() => {
 for (let i = 0; i < 210; i++) {
   dataSource.push({
     sid:`id-${i}`,
     address: 'London, Park Lane no. 1',
     age: 42,
     name: '<PERSON>',
   })
 }
  console.log('dataSource', dataSource)

  // 初始化表格高度
  tableHeight.value = getTableScroll(73)
})
// const setTableHeight  = ()=>{
//   this.scroll = {}
//   if (this.$refs["area_vr_wrapper"]) {
//     let refAreaVrWwrapper = this.$refs["area_vr_wrapper"].getBoundingClientRect()
//
//     // 计算面包屑高度
//     let breadCrumbDivHeight = 0
//     if (this.$refs["breadCrumbDiv"]) {
//       breadCrumbDivHeight = 32
//     }
//     // 计算查询条件高度
//     let queryConditionDivHeight = 0
//     if(this.showQueryCondition){
//       if (this.$refs['queryConditionDiv']) {
//         queryConditionDivHeight = this.$refs['queryConditionDiv'].getBoundingClientRect().height+2
//       }
//     }
//     // 计算表格统计高度
//     let tableFooterHeight = 0
//     if (this.statisticsSource && this.statisticsSource.length > 0) {
//       let a = this.$refs["tableDiv"].getElementsByClassName('ant-table-footer')
//       if(a.length > 0){
//         tableFooterHeight = 32
//       }
//     }
//
//     if (this.tableScrollEnable) {
//       this.scroll.x = this.tableScrollWidth
//       //如果开启了滚动条，取计算后的宽度
//       if(this.allWidth > 0){
//         this.scroll.x = this.allWidth
//       }
//     }
//     if (fixedHeight && fixedHeight != 'auto') {
//       this.scroll.y = fixedHeight
//     } else {
//       let totalHeight = refAreaVrWwrapper.height === 0 ? (this.gridLayoutHeight * 10 - 5) : refAreaVrWwrapper.height
//       let h = totalHeight  -  queryConditionDivHeight - breadCrumbDivHeight
//         - this.tableHeaderHeight - 40
//         - (this.showTableSuffixValue ? this.$refs["refTableSuffix"].getBoundingClientRect().height : 0)
//         - tableFooterHeight
//       this.scroll.y = h
//       console.log( this.scroll.y)
//       console.log(h)
//       if(this.$refs.table && this.$refs.table.$el){
//         let ele = this.$refs.table.$el.getElementsByClassName("ant-table-container")
//         if (ele.length > 0) {
//           ele = ele[0].getElementsByClassName("ant-table-body")
//           if (ele.length > 0) {
//             ele = ele[0]
//           } else {
//             ele = null
//           }
//         } else {
//           ele = null
//         }
//         if (this.dataSource && this.dataSource.length > 0) {
//           if (ele) {
//             ele.style.height = h + "px"
//           }
//         } else {
//           if (ele)
//             ele.style.height = null
//         }
//       }
//     }
//   }


  const getTableScroll = ({extraHeight, id}) => {
    if (typeof extraHeight == "undefined") {
      //  默认底部分页64 + 边距10
      extraHeight = 74
    }
    let tHeader = null
    if (id) {
      tHeader = document.getElementById(id) ? document.getElementById(id).getElementsByClassName("ant-table-thead")[0] : null
    } else {
      tHeader = document.getElementsByClassName("ant-table-thead")[0]
    }
    //表格内容距离顶部的距离
    let tHeaderBottom = 0
    if (tHeader) {
      tHeaderBottom = tHeader.getBoundingClientRect().bottom
    }
    //窗体高度-表格内容顶部的高度-表格内容底部的高度
    // let height = document.body.clientHeight - tHeaderBottom - extraHeight
    let height = `calc(100vh - ${tHeaderBottom + extraHeight}px)`
    console.log('height', height)
    return height
  }

</script>

<style lang="less" scoped>
</style>

