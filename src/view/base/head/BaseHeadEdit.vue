<template>
  <section  >
<!--    <div class="tag-card">
      客户基础信息
    </div>
    <div class="cs-divider"></div>-->
    <a-card size="small" title="客户基础信息" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData"   class=" grid-container">
          <!-- 客户类型(供应商 PRD、客户CLI、货代FOD、报关行CUT、企业COM) -->
          <a-form-item name="customerType" :label="'客户类型'" class="grid-item"  :colon="false">
            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.customerType" id="customsCreditRating">
              <a-select-option v-for="item in productClassify.customerType"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 客户代码 -->
          <a-form-item name="customerCode" :label="'客户代码'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.customerCode"/>
          </a-form-item>
          <!-- 客户中文名称 -->
          <a-form-item name="companyName" :label="'客户中文名称'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.companyName"/>
          </a-form-item>
          <!-- 海关注册编码 -->
          <a-form-item name="declareCode" :label="'海关注册编码'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.declareCode"/>
          </a-form-item>
          <!-- 海关信用等级 -->
          <a-form-item name="customsCreditRating" :label="'海关信用等级'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.customsCreditRating" id="customsCreditRating">
              <a-select-option v-for="item in productClassify.creditLevel"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 社会信用代码 -->
          <a-form-item name="creditCode" :label="'社会信用代码'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.creditCode"/>
          </a-form-item>
          <!-- 企业名称缩写 -->
          <a-form-item name="companyNameShort" :label="'企业名称缩写'" class="grid-item merge-2" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.companyNameShort"/>
          </a-form-item>
          <!-- 商检代码 -->
          <a-form-item name="inspectionCode" :label="'商检代码'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.inspectionCode"/>
          </a-form-item>

          <!-- 客户电话 -->
          <a-form-item name="telephoneNo" :label="'客户电话'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.telephoneNo"/>
          </a-form-item>
          <!-- 客户联系人 -->
          <a-form-item name="linkmanName" :label="'客户联系人'" class="grid-item merge-2" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.linkmanName"/>
          </a-form-item>
          <!-- 联系人职务 -->
          <a-form-item name="linkmanDuty" :label="'联系人职务'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.linkmanDuty"/>
          </a-form-item>
          <!-- 联系人电话 -->
          <a-form-item name="mobilePhone" :label="'联系人电话'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.mobilePhone"/>
          </a-form-item>
          <!-- 联系人邮箱 -->
          <a-form-item name="eMail" :label="'联系人邮箱'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.eMail"/>
          </a-form-item>
          <!-- 企业中文地址 -->
          <a-form-item name="address" :label="'企业中文地址'" class="grid-item merge-3" :colon="false">
            <a-textarea :disabled="showDisable" size="small" v-model:value="formData.address"/>
          </a-form-item>
          <!-- 企业英文名称 -->
          <a-form-item name="companyNameEn" :label="'企业英文名称'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.companyNameEn"/>
          </a-form-item>
          <!-- 英文国家 -->
          <a-form-item name="countryEn" :label="'英文国家'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.countryEn"/>
          </a-form-item>
          <!-- 英文地区 -->
          <a-form-item name="areaEn" :label="'英文地区'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.areaEn"/>
          </a-form-item>
          <!-- 英文城市 -->
          <a-form-item name="cityEn" :label="'英文城市'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.cityEn"/>
          </a-form-item>
          <!-- 英文地址 -->
          <a-form-item name="addressEn" :label="'英文地址'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.addressEn"/>
          </a-form-item>
          <!-- 客户电话(英文) -->
          <a-form-item name="telephoneNoEn" :label="'客户电话(英文)'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.telephoneNoEn"/>
          </a-form-item>
          <!-- 客户联系人(英文) -->
          <a-form-item name="linkmanNameEn" :label="'客户联系人(英文)'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.linkmanNameEn"/>
          </a-form-item>
          <!-- 联系人电话(英文) -->
          <a-form-item name="mobilePhoneEn" :label="'联系人电话(英文)'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.mobilePhoneEn"/>
          </a-form-item>
          <!-- 联系人邮箱(英文) -->
          <a-form-item name="eMailEn" :label="'联系人邮箱(英文)'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.eMailEn"/>
          </a-form-item>
          <!-- 联系人职务(英文) -->
          <a-form-item name="linkmanDutyEn" :label="'联系人职务(英文)'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.linkmanDutyEn"/>
          </a-form-item>
          <!-- AEO代码 -->
          <a-form-item name="aeoCode" :label="'AEO代码'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.aeoCode"/>
          </a-form-item>
          <!-- 申报地海关 -->
          <a-form-item name="masterCustoms" :label="'申报地海关'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.masterCustoms" id="masterCustoms">
              <a-select-option v-for="(key,value) in pCode.CUSTOMS_REL"  :key="value +' ' + key" :value="value" :label="key + value">
                {{value }} {{key}}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 传真 -->
          <a-form-item name="fax" :label="'传真'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.fax"/>
          </a-form-item>
          <!-- 邮编 -->
          <a-form-item name="postal" :label="'邮编'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.postal"/>
          </a-form-item>
          <!-- 中文国家 -->
          <a-form-item name="country" :label="'中文国家'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.country"/>
          </a-form-item>
          <!-- 中文地区 -->
          <a-form-item name="area" :label="'中文地区'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.area"/>
          </a-form-item>
          <!-- 中文城市 -->
          <a-form-item name="city" :label="'中文城市'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.city"/>
          </a-form-item>
          <!-- 发票中文地址 -->
          <a-form-item name="invoiceAddress" :label="'发票中文地址'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.invoiceAddress"/>
          </a-form-item>
          <!-- 发票英文地址 -->
          <a-form-item name="invoiceAddressEn" :label="'发票英文地址'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.invoiceAddressEn"/>
          </a-form-item>
          <!-- 送货中文地址 -->
          <a-form-item name="deliverAddress" :label="'送货中文地址'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.deliverAddress"/>
          </a-form-item>
          <!-- 送货英文地址 -->
          <a-form-item name="deliverAddressEn" :label="'送货英文地址'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.deliverAddressEn"/>
          </a-form-item>
          <!-- 数据状态 -->
          <a-form-item name="status" :label="'数据状态'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.status"/>
          </a-form-item>
          <!-- 是否授权(0: 未授权; 1:已授权) -->
          <a-form-item name="authorize" :label="'是否授权'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.authorize" id="authorize">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.isAuthorizeList"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 授权截止日期 -->
          <a-form-item name="authorizeDeadline" :label="'授权截止日期'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="showDisable"
              v-model:value="formData.authorizeDeadline"
              id="authorizeDeadline"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
            />
          </a-form-item>
          <!-- 减免税货物使用地点 -->
          <a-form-item name="freeAddress" :label="'减免税货物使用地点'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.freeAddress"/>
          </a-form-item>
          <!-- 减免税物资属性 -->
          <a-form-item name="freeProperties" :label="'减免税物资属性'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.freeProperties"/>
          </a-form-item>
          <!-- 成本中心 -->
          <a-form-item name="costCenter" :label="'成本中心'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.costCenter"/>
          </a-form-item>
          <!-- 报关人员 -->
          <a-form-item name="decPersonnel" :label="'报关人员'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.decPersonnel"/>
          </a-form-item>
          <!-- 报关人员电话 -->
          <a-form-item name="decPersonnelTel" :label="'报关人员电话'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.decPersonnelTel"/>
          </a-form-item>


          <!-- 备注 -->
          <a-form-item name="note" :label="'备注'" class="grid-item merge-3" :colon="false">
            <a-textarea :disabled="showDisable" size="small" v-model:value="formData.note"  :autosize="{ minRows: 5, maxRows: 10 }"/>
          </a-form-item>

          <div class="cs-submit-btn merge-3">
            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right"
                      v-show="props.editConfig.editStatus !== 'SHOW' ">保存
            </a-button>
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(false)">返回</a-button>
          </div>
        </a-form>
      </div>
    </a-card>


  </section>
</template>

<script setup>
import {editStatus, productClassify} from '@/view/common/constant'
import {message} from "ant-design-vue";
import {onMounted, reactive, ref} from "vue";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
import {insertClient, updateClient} from "@/api/bi/bi_client_info";
const { getPCode } = usePCode()



const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});

// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onBack']);

const onBack = (val) => {
  emit('onBack', val);
};

// 是否禁用
const showDisable = ref(false)

// 表单数据
const formData = reactive({
  // 客户类型(供应商 PRD、客户CLI、货代FOD、报关行CUT、企业COM)       字符类型(3)
  customerType: '',
  // 客户代码       字符类型(50)
  customerCode: '',
  // 客户中文名称       字符类型(200)
  companyName: '',
  // 海关信用等级       字符类型(3)
  customsCreditRating: '',
  // 商检代码       字符类型(20)
  inspectionCode: '',
  // 社会信用代码       字符类型(20)
  creditCode: '',
  // 海关注册编码       字符类型(10)
  declareCode: '',
  // 企业名称缩写       字符类型(50)
  companyNameShort: '',
  // 客户电话       字符类型(50)
  telephoneNo: '',
  // 客户联系人       字符类型(100)
  linkmanName: '',
  // 联系人职务       字符类型(20)
  linkmanDuty: '',
  // 联系人电话       字符类型(20)
  mobilePhone: '',
  // 联系人邮箱       字符类型(100)
  eMail: '',
  // 企业中文地址       字符类型(250)
  address: '',
  // 企业英文名称       字符类型(250)
  companyNameEn: '',
  // 英文国家       字符类型(100)
  countryEn: '',
  // 英文地区       字符类型(100)
  areaEn: '',
  // 英文城市       字符类型(100)
  cityEn: '',
  // 英文地址       字符类型(250)
  addressEn: '',
  // 客户电话(英文)       字符类型(20)
  telephoneNoEn: '',
  // 客户联系人(英文)       字符类型(50)
  linkmanNameEn: '',
  // 联系人电话(英文)       字符类型(20)
  mobilePhoneEn: '',
  // 联系人邮箱(英文)       字符类型(50)
  eMailEn: '',
  // 备注       字符类型(250)
  note: '',
  // 创建人       字符类型(50)
  insertUser: '',
  // 创建日期       timestamp
  insertTime: '',
  // 修改人       字符类型(50)
  updateUser: '',
  // 修改时间       timestamp
  updateTime: '',
  // 所属企业编码       字符类型(20)
  tradeCode: '',
  // 主键       字符类型(40)
  sid: '',
  // 联系人职务(英文)       字符类型(20)
  linkmanDutyEn: '',
  // AEO代码       字符类型(50)
  aeoCode: '',
  // 申报地海关       字符类型(4)
  masterCustoms: '',
  // 传真       字符类型(50)
  fax: '',
  // 邮编       字符类型(50)
  postal: '',
  // 中文国家       字符类型(100)
  country: '',
  // 中文地区       字符类型(100)
  area: '',
  // 中文城市       字符类型(100)
  city: '',
  // 发票中文地址       字符类型(255)
  invoiceAddress: '',
  // 发票英文地址       字符类型(255)
  invoiceAddressEn: '',
  // 送货中文地址       字符类型(255)
  deliverAddress: '',
  // 送货英文地址       字符类型(255)
  deliverAddressEn: '',
  // 数据状态       字符类型(1)
  status: '',
  // 制单人姓名       字符类型(50)
  insertUserName: '',
  // 修改人姓名       字符类型(50)
  updateUserName: '',
  // 是否授权(0: 未授权; 1:已授权)       字符类型(1)
  authorize: '',
  // 授权截止日期       日期类型(6)
  authorizeDeadline: '',
  // 减免税货物使用地点       字符类型(250)
  freeAddress: '',
  // 减免税物资属性       字符类型(50)
  freeProperties: '',
  // 成本中心       字符类型(150)
  costCenter: '',
  // 报关人员       字符类型(50)
  decPersonnel: '',
  // 报关人员电话       字符类型(50)
  decPersonnelTel: ''
})
// 校验规则
const rules = {
  customerType: [
    {required: true, message: '客户类型不能为空', trigger: 'blur'},
    {max: 3, message: '客户类型长度不能超过3位字节', trigger: 'blur'}
  ],
  customerCode: [
    {required: true, message: '客户代码不能为空', trigger: 'blur'},
    {max: 50, message: '客户代码长度不能超过50位字节', trigger: 'blur'}
  ],
  companyName: [
    {required: true, message: '客户中文名称不能为空', trigger: 'blur'},
    {max: 200, message: '客户中文名称长度不能超过200位字节', trigger: 'blur'}
  ],
  customsCreditRating: [
    {max: 3, message: '海关信用等级长度不能超过3位字节', trigger: 'blur'}
  ],
  inspectionCode: [
    {max: 20, message: '商检代码长度不能超过20位字节', trigger: 'blur'}
  ],
  creditCode: [
    {max: 20, message: '社会信用代码长度不能超过20位字节', trigger: 'blur'}
  ],
  declareCode: [
    {max: 10, message: '海关注册编码长度不能超过10位字节', trigger: 'blur'}
  ],
  companyNameShort: [
    {max: 50, message: '企业名称缩写长度不能超过50位字节', trigger: 'blur'}
  ],
  telephoneNo: [
    {max: 50, message: '客户电话长度不能超过50位字节', trigger: 'blur'}
  ],
  linkmanName: [
    {max: 100, message: '客户联系人长度不能超过100位字节', trigger: 'blur'}
  ],
  linkmanDuty: [
    {max: 20, message: '联系人职务长度不能超过20位字节', trigger: 'blur'}
  ],
  mobilePhone: [
    {max: 20, message: '联系人电话长度不能超过20位字节', trigger: 'blur'}
  ],
  eMail: [
    {max: 100, message: '联系人邮箱长度不能超过100位字节', trigger: 'blur'}
  ],
  address: [
    {max: 250, message: '企业中文地址长度不能超过250位字节', trigger: 'blur'}
  ],
  companyNameEn: [
    {max: 250, message: '企业英文名称长度不能超过250位字节', trigger: 'blur'}
  ],
  countryEn: [
    {max: 100, message: '英文国家长度不能超过100位字节', trigger: 'blur'}
  ],
  areaEn: [
    {max: 100, message: '英文地区长度不能超过100位字节', trigger: 'blur'}
  ],
  cityEn: [
    {max: 100, message: '英文城市长度不能超过100位字节', trigger: 'blur'}
  ],
  addressEn: [
    {max: 250, message: '英文地址长度不能超过250位字节', trigger: 'blur'}
  ],
  telephoneNoEn: [
    {max: 20, message: '客户电话(英文)长度不能超过20位字节', trigger: 'blur'}
  ],
  linkmanNameEn: [
    {max: 50, message: '客户联系人(英文)长度不能超过50位字节', trigger: 'blur'}
  ],
  mobilePhoneEn: [
    {max: 20, message: '联系人电话(英文)长度不能超过20位字节', trigger: 'blur'}
  ],
  eMailEn: [
    {max: 50, message: '联系人邮箱(英文)长度不能超过50位字节', trigger: 'blur'}
  ],
  note: [
    {max: 250, message: '备注长度不能超过250位字节', trigger: 'blur'}
  ],
  insertUser: [
    {max: 50, message: '创建人长度不能超过50位字节', trigger: 'blur'}
  ],
  insertTime: [],
  updateUser: [
    {max: 50, message: '修改人长度不能超过50位字节', trigger: 'blur'}
  ],
  updateTime: [],
  tradeCode: [
    {max: 20, message: '所属企业编码长度不能超过20位字节', trigger: 'blur'}
  ],
  sid: [
    {max: 40, message: '主键长度不能超过40位字节', trigger: 'blur'}
  ],
  linkmanDutyEn: [
    {max: 20, message: '联系人职务(英文)长度不能超过20位字节', trigger: 'blur'}
  ],
  aeoCode: [
    {max: 50, message: 'AEO代码长度不能超过50位字节', trigger: 'blur'}
  ],
  masterCustoms: [
    {max: 4, message: '申报地海关长度不能超过4位字节', trigger: 'blur'}
  ],
  fax: [
    {max: 50, message: '传真长度不能超过50位字节', trigger: 'blur'}
  ],
  postal: [
    {max: 50, message: '邮编长度不能超过50位字节', trigger: 'blur'}
  ],
  country: [
    {max: 100, message: '中文国家长度不能超过100位字节', trigger: 'blur'}
  ],
  area: [
    {max: 100, message: '中文地区长度不能超过100位字节', trigger: 'blur'}
  ],
  city: [
    {max: 100, message: '中文城市长度不能超过100位字节', trigger: 'blur'}
  ],
  invoiceAddress: [
    {max: 255, message: '发票中文地址长度不能超过255位字节', trigger: 'blur'}
  ],
  invoiceAddressEn: [
    {max: 255, message: '发票英文地址长度不能超过255位字节', trigger: 'blur'}
  ],
  deliverAddress: [
    {max: 255, message: '送货中文地址长度不能超过255位字节', trigger: 'blur'}
  ],
  deliverAddressEn: [
    {max: 255, message: '送货英文地址长度不能超过255位字节', trigger: 'blur'}
  ],
  status: [
    {max: 1, message: '数据状态长度不能超过1位字节', trigger: 'blur'}
  ],
  insertUserName: [
    {max: 50, message: '制单人姓名长度不能超过50位字节', trigger: 'blur'}
  ],
  updateUserName: [
    {max: 50, message: '修改人姓名长度不能超过50位字节', trigger: 'blur'}
  ],
  authorize: [
    {max: 1, message: '是否授权(0: 未授权; 1:已授权)长度不能超过1位字节', trigger: 'blur'}
  ],
  authorizeDeadline: [],
  freeAddress: [
    {max: 250, message: '减免税货物使用地点长度不能超过250位字节', trigger: 'blur'}
  ],
  freeProperties: [
    {max: 50, message: '减免税物资属性长度不能超过50位字节', trigger: 'blur'}
  ],
  costCenter: [
    {max: 150, message: '成本中心长度不能超过150位字节', trigger: 'blur'}
  ],
  decPersonnel: [
    {max: 50, message: '报关人员长度不能超过50位字节', trigger: 'blur'}
  ],
  decPersonnelTel: [
    {max: 50, message: '报关人员电话长度不能超过50位字节', trigger: 'blur'}
  ]
}

const pCode = ref('')
// 初始化操作
onMounted(() => {
  getPCode().then(res=>{
    console.log('res',res)
    pCode.value = res;
  })
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    showDisable.value = false
    Object.assign(formData, {});
  }
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = true
  }
});



// vue3中使用ref,需要先定义，然后在模板中使用。因为在模板渲染之前，DOM 元素还不存在。
const formRef = ref(null);
// 保存
const handlerSave = () => {
  formRef.value
    .validate()
    .then(() => {
      if (props.editConfig && props.editConfig.editStatus === editStatus.ADD){
        insertClient(formData).then((res)=>{
          if (res.code === 200){
            message.success('新增成功!')
            onBack(true)
          }
        })
      }else if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT){
        console.log('value',formData)
        updateClient(formData.sid,formData).then((res)=>{
          if (res.code === 200){
            message.success('修改成功!')
            onBack(true)
          }
        })
      }
    })
    .catch(error => {
      console.log('validate failed', error);
    })
};

</script>

<style lang="less" scoped>


</style>



