import {baseColumns} from "@/view/common/baseColumns";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {h, reactive, ref} from "vue";
import {productClassify} from "@/view/common/constant";
import {Tag} from "ant-design-vue";
const {baseColumnsExport, baseColumnsShow} = baseColumns()
const { cmbShowRender } = useColumnsRender()


export function getColumns() {

  const commColumns = reactive([
    'customerType',
    'customerCode',
    'companyName',
    'customsCreditRating',
    'inspectionCode',
    'creditCode',
    'declareCode',
    'companyNameShort',
    'telephoneNo',
    'linkmanName',
    'linkmanDuty',
    'mobilePhone',
    'eMail',
    'address',
    'companyNameEn',
    'countryEn',
    'areaEn',
    'cityEn',
    'addressEn',
    'telephoneNoEn',
    'linkmanNameEn',
    'mobilePhoneEn',
    'eMailEn',
    'note',
    'insertUser',
    'insertTime',
    'updateUser',
    'updateTime',
    'tradeCode',
    'sid',
    'linkmanDutyEn',
    'aeoCode',
    'masterCustoms',
    'fax',
    'postal',
    'country',
    'area',
    'city',
    'invoiceAddress',
    'invoiceAddressEn',
    'deliverAddress',
    'deliverAddressEn',
    'status',
    'insertUserName',
    'updateUserName',
    'authorize',
    'authorizeDeadline',
    'freeAddress',
    'freeProperties',
    'costCenter',
    'decPersonnel',
    'decPersonnelTel'
  ])

// 导出字段设置`
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])

  // table表格字段设置
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  // table表格字段设置
  const totalColumns = ref([
    {
      width: 80,
      minWidth:80,
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      align: 'center',
      fixed: 'left',
    },
    {
      title: '客户类型',
      width: 150,
      align: 'center',
      dataIndex: 'customerType',
      key: 'customerType',
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,productClassify.customerType))
      }
    },
    {
      title: '客户代码',
      width: 220,
      align: 'center',
      dataIndex: 'customerCode',
      key: 'customerCode',
    },
    {
      title: '客户中文名称',
      width: 220,
      align: 'center',
      dataIndex: 'companyName',
      key: 'companyName',
    },
    {
      title: '海关信用等级',
      width: 220,
      align: 'center',
      dataIndex: 'customsCreditRating',
      key: 'customsCreditRating',
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,productClassify.creditLevel))
      }
    },
    {
      title: '商检代码',
      width: 150,
      align: 'center',
      dataIndex: 'inspectionCode',
      key: 'inspectionCode',
    },
    {
      title: '社会信用代码',
      width: 150,
      align: 'center',
      dataIndex: 'creditCode',
      key: 'creditCode',
    },
    {
      title: '海关注册编码',
      width: 150,
      align: 'center',
      dataIndex: 'declareCode',
      key: 'declareCode',
    },
    {
      title: '企业名称缩写',
      width: 150,
      align: 'center',
      dataIndex: 'companyNameShort',
      key: 'companyNameShort',
    },
    {
      title: '客户电话',
      width: 150,
      align: 'center',
      dataIndex: 'telephoneNo',
      key: 'telephoneNo',
    },
    {
      title: '客户联系人',
      width: 150,
      align: 'center',
      dataIndex: 'linkmanName',
      key: 'linkmanName',
    },
    {
      title: '联系人职务',
      width: 150,
      align: 'center',
      dataIndex: 'linkmanDuty',
      key: 'linkmanDuty',
    },
    {
      title: '联系人电话',
      width: 150,
      align: 'center',
      dataIndex: 'mobilePhone',
      key: 'mobilePhone',
    },
    {
      title: '联系人邮箱',
      width: 150,
      align: 'center',
      dataIndex: 'eMail',
      key: 'eMail',
    },
    {
      title: '企业中文地址',
      width: 150,
      align: 'center',
      dataIndex: 'address',
      key: 'address',
    },
    {
      title: '企业英文名称',
      width: 150,
      align: 'center',
      dataIndex: 'companyNameEn',
      key: 'companyNameEn',
    },
    {
      title: '英文国家',
      width: 150,
      align: 'center',
      dataIndex: 'countryEn',
      key: 'countryEn',
    },
    {
      title: '英文地区',
      width: 150,
      align: 'center',
      dataIndex: 'areaEn',
      key: 'areaEn',
    },
    {
      title: '英文城市',
      width: 150,
      align: 'center',
      dataIndex: 'cityEn',
      key: 'cityEn',
    },
    {
      title: '英文地址',
      width: 150,
      align: 'center',
      dataIndex: 'addressEn',
      key: 'addressEn',
    },
    {
      title: '客户电话(英文)',
      width: 150,
      align: 'center',
      dataIndex: 'telephoneNoEn',
      key: 'telephoneNoEn',
    },
    {
      title: '客户联系人(英文)',
      width: 150,
      align: 'center',
      dataIndex: 'linkmanNameEn',
      key: 'linkmanNameEn',
    },
    {
      title: '联系人电话(英文)',
      width: 150,
      align: 'center',
      dataIndex: 'mobilePhoneEn',
      key: 'mobilePhoneEn',
    },
    {
      title: '联系人邮箱(英文)',
      width: 150,
      align: 'center',
      dataIndex: 'eMailEn',
      key: 'eMailEn',
    },
    {
      title: '备注',
      width: 150,
      align: 'center',
      dataIndex: 'note',
      key: 'note',
    },
    {
      title: '创建人',
      width: 150,
      align: 'center',
      dataIndex: 'insertUser',
      key: 'insertUser',
    },
    {
      title: '创建日期',
      width: 150,
      align: 'center',
      dataIndex: 'insertTime',
      key: 'insertTime',
    },
    {
      title: '修改人',
      width: 150,
      align: 'center',
      dataIndex: 'updateUser',
      key: 'updateUser',
    },
    {
      title: '修改时间',
      width: 150,
      align: 'center',
      dataIndex: 'updateTime',
      key: 'updateTime',
    },
    {
      title: '所属企业编码',
      width: 150,
      align: 'center',
      dataIndex: 'tradeCode',
      key: 'tradeCode',
    },
    {
      title: '主键',
      width: 350,
      align: 'center',
      dataIndex: 'sid',
      key: 'sid',
    },
    {
      title: '联系人职务(英文)',
      width: 150,
      align: 'center',
      dataIndex: 'linkmanDutyEn',
      key: 'linkmanDutyEn',
    },
    {
      title: 'AEO代码',
      width: 150,
      align: 'center',
      dataIndex: 'aeoCode',
      key: 'aeoCode',
    },
    {
      title: '申报地海关',
      width: 150,
      align: 'center',
      dataIndex: 'masterCustoms',
      key: 'masterCustoms',
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,[],'CUSTOMS_REL'))
      }

    },
    {
      title: '传真',
      width: 150,
      align: 'center',
      dataIndex: 'fax',
      key: 'fax',
    },
    {
      title: '邮编',
      width: 150,
      align: 'center',
      dataIndex: 'postal',
      key: 'postal',
    },
    {
      title: '中文国家',
      width: 150,
      align: 'center',
      dataIndex: 'country',
      key: 'country',
    },
    {
      title: '中文地区',
      width: 150,
      align: 'center',
      dataIndex: 'area',
      key: 'area',
    },
    {
      title: '中文城市',
      width: 150,
      align: 'center',
      dataIndex: 'city',
      key: 'city',
    },
    {
      title: '发票中文地址',
      width: 150,
      align: 'center',
      dataIndex: 'invoiceAddress',
      key: 'invoiceAddress',
    },
    {
      title: '发票英文地址',
      width: 150,
      align: 'center',
      dataIndex: 'invoiceAddressEn',
      key: 'invoiceAddressEn',
    },
    {
      title: '送货中文地址',
      width: 150,
      align: 'center',
      dataIndex: 'deliverAddress',
      key: 'deliverAddress',
    },
    {
      title: '送货英文地址',
      width: 150,
      align: 'center',
      dataIndex: 'deliverAddressEn',
      key: 'deliverAddressEn',
    },
    {
      title: '数据状态',
      width: 150,
      align: 'center',
      dataIndex: 'status',
      key: 'status',
      customRender: ({ text }) => {
        const tagColor = text === '1' ? 'success' : 'error';
        return h(Tag,{ color:tagColor }, cmbShowRender(text,productClassify.status))
      }
    },
    {
      title: '制单人姓名',
      width: 150,
      align: 'center',
      dataIndex: 'insertUserName',
      key: 'insertUserName',
    },
    {
      title: '修改人姓名',
      width: 150,
      align: 'center',
      dataIndex: 'updateUserName',
      key: 'updateUserName',
    },
    {
      title: '是否授权',
      width: 150,
      align: 'center',
      dataIndex: 'authorize',
      key: 'authorize',
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,productClassify.isAuthorizeList))
      }
    },
    {
      title: '授权截止日期',
      width: 150,
      align: 'center',
      dataIndex: 'authorizeDeadline',
      key: 'authorizeDeadline',
      customRender: ({ text }) => {
        return text ? text.slice(0, 10) : text
      }
    },
    {
      title: '减免税货物使用地点',
      width: 150,
      align: 'center',
      dataIndex: 'freeAddress',
      key: 'freeAddress',
    },
    {
      title: '减免税物资属性',
      width: 150,
      align: 'center',
      dataIndex: 'freeProperties',
      key: 'freeProperties',
    },
    {
      title: '成本中心',
      width: 150,
      align: 'center',
      dataIndex: 'costCenter',
      key: 'costCenter',
    },
    {
      title: '报关人员',
      width: 150,
      align: 'center',
      dataIndex: 'decPersonnel',
      key: 'decPersonnel',
    },
    {
      title: '报关人员电话',
      width: 150,
      align: 'center',
      dataIndex: 'decPersonnelTel',
      key: 'decPersonnelTel'
    }
  ])

  return{
    columnsConfig,
    excelColumnsConfig,
    totalColumns
  }
}


