import {baseColumns} from "@/view/common/baseColumns";
import {useColumnsRender} from "@/view/common/useColumnsRender";
const { cmbShowRender } = useColumnsRender()
import {h, onMounted, reactive, ref} from "vue";
import {productClassify} from "@/view/common/constant";
import {Tag} from "ant-design-vue";
import {getMerchantCodeValueClient} from "../../../api/bi/bi_client_info";
const {baseColumnsExport, baseColumnsShow} = baseColumns()





export function getColumns() {

  const commColumns = reactive([
    'gName',
    'shortCn',
    'billingName',
    'fullEnName',
    'shortEnName',
    'merchandiseCategories',
    'nationalProductCatalogue',
    'barCode',
    'commonMarkList',
    'packagingInformation',
    'misCode',
    'statisticalName',
    'nameMethod',
    // 'taxExclusive',
    // 'includingTax',
    // 'taxRate',
    'priceExcludingTax',
    'note',
    'dataState',
    'supplierCode',
    // 'supplierDiscountRate',
    // 'importUnitPrice',
    'insertTime',
    'insertUser',
    'curr'
  ])
  const commColumnsInput = reactive([
    'gName',
    'shortCn',
    'billingName',
    'fullEnName',
    'shortEnName',
    'nationalProductCatalogue',
    'barCode',
    'misCode',
    'statisticalName',
    // 'taxExclusive',
    // 'includingTax',
    // 'taxRate',
    // 'priceExcludingTax',
    'note',
    // 'supplierDiscountRate',
    // 'importUnitPrice'
  ])

// 导出字段设置`
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns,
    'taxRateStr',
    'supplierDiscountRateStr'
  ])

  // table表格字段设置
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns,
    // 'taxRate',
    // 'supplierDiscountRate'
  ])


  return{
    columnsConfig,
    excelColumnsConfig,
    commColumnsInput,
  }
}


