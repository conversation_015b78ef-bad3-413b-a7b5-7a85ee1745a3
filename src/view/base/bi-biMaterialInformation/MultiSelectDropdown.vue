<template>
  <div class="multi-select-dropdown">
    <div class="selected-items" @click="toggleDropdown">
      {{ selectedOptions.length > 0 ? selectedOptions.map(option => option.label).join(', ') : '请选择' }}
      <i :class="{ 'fa-chevron-up': isDropdownOpen, 'fa-chevron-down': !isDropdownOpen }" class="fas"></i>
    </div>
    <ul v-if="isDropdownOpen" class="dropdown-options">
      <li v-for="option in options" :key="option.value" @click="toggleOption(option)">
        <input type="checkbox" :checked="isOptionSelected(option)" @change="toggleOption(option)" />
        {{ option.label }}
      </li>
    </ul>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits } from 'vue';

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  options: {
    type: Array,
    default: () => []
  }
});

const emits = defineEmits(['update:modelValue']);

// 将传入的字符串形式的 value 转换为数组
const selectedValues = ref(props.modelValue ? props.modelValue.split(',') : []);
const selectedOptions = ref(props.options.filter(option => selectedValues.value.includes(option.value)));
const isDropdownOpen = ref(false);

const toggleDropdown = () => {
  isDropdownOpen.value = !isDropdownOpen.value;
};

const toggleOption = (option) => {
  const index = selectedValues.value.indexOf(option.value);
  if (index === -1) {
    selectedValues.value.push(option.value);
    selectedOptions.value.push(option);
  } else {
    selectedValues.value.splice(index, 1);
    const optionIndex = selectedOptions.value.findIndex(item => item.value === option.value);
    selectedOptions.value.splice(optionIndex, 1);
  }
  // 将选中的 value 数组拼接成字符串并触发更新事件
  emits('update:modelValue', selectedValues.value.join(','));
};

const isOptionSelected = (option) => {
  return selectedValues.value.includes(option.value);
};
</script>

<style scoped>
.multi-select-dropdown {
  position: relative;
  width: 200px;
}

.selected-items {
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dropdown-options {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  border: 1px solid #ccc;
  border-top: none;
  border-radius: 0 0 4px 4px;
  background-color: #fff;
  list-style-type: none;
  padding: 0;
  margin: 0;
  z-index: 1;
}

.dropdown-options li {
  padding: 8px 12px;
  cursor: pointer;
}

.dropdown-options li:hover {
  background-color: #f0f0f0;
}
</style>
