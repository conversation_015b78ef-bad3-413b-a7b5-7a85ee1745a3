<template>
  <a-form  layout="inline"  label-align="right"  :label-col="{ style: { width: '100px' } }" :model="searchParam"   class="cs-form  grid-container" >
    <a-form-item name="gName"   :label="'商品名称'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.gName" />
    </a-form-item>
    <a-form-item name="merchandiseCategories"   :label="'商品类别'" class="grid-item"  :colon="false">
<!--      <a-select-->
<!--        v-model:value="searchParam.merchandiseCategoriesMap"-->
<!--        style="width: 100%"-->
<!--        placeholder="Please select"-->
<!--        :options="merchandiseCategoriesMap"-->
<!--      ></a-select>-->
      <cs-select optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="searchParam.merchandiseCategories" id="merchandiseCategories">
        <a-select-option   class="cs-select-dropdown"  v-for="item in merchandiseCategoriesMap"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.label">
          {{item.value}} {{item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>
<!--    <a-form-item name="merchandiseCategories"   :label="'商品代码'" class="grid-item"  :colon="false">-->
<!--      <a-select  size="small" v-model:value="searchParam.merchandiseCategories" :options="merchandiseCategoriesMap" placeholder="Please select"/>-->
<!--    </a-form-item>-->
    <!-- 客户代码 -->
<!--    <a-form-item name="customerCode"   :label="'客户代码'" class="grid-item"  :colon="false">-->
<!--      <a-input  size="small" v-model:value="searchParam.customerCode" />-->
<!--    </a-form-item>-->

<!--    &lt;!&ndash; 客户中文名称 &ndash;&gt;-->
<!--    <a-form-item name="companyName"   :label="'客户中文名称'" class="grid-item"  :colon="false">-->
<!--      <a-input  size="small" v-model:value="searchParam.companyName" />-->
<!--    </a-form-item>-->

<!--    &lt;!&ndash; 商检代码 &ndash;&gt;-->
<!--    <a-form-item name="inspectionCode"   :label="'商检代码'" class="grid-item"  :colon="false">-->
<!--      <a-input  size="small" v-model:value="searchParam.inspectionCode" />-->
<!--    </a-form-item>-->

<!--    &lt;!&ndash; 企业名称缩写 &ndash;&gt;-->
<!--    <a-form-item name="companyNameShort"   :label="'企业名称缩写'" class="grid-item"  :colon="false">-->
<!--      <a-input  size="small" v-model:value="searchParam.companyNameShort" :colon="false" />-->
<!--    </a-form-item>-->

<!--    &lt;!&ndash; 更新时间 &ndash;&gt;-->
<!--    <a-form-item name="insertTime" label="更新时间" class="grid-item" :colon="false">-->
<!--      &lt;!&ndash; Warning: [ant-design-vue: Form.Item] FormItem can only collect one field item,-->
<!--            you haved set ASelect, ASelect, AInputNumber, AInputNumber, AInput 5 field items. You can set not need to be collected fields into a-form-item-rest-->
<!--      &ndash;&gt;-->
<!--      <a-form-item-rest>-->
<!--        <a-row>-->
<!--          <a-col :span="11">-->
<!--            <a-date-picker-->
<!--              v-model:value="searchParam.insertTimeFrom"-->
<!--              id="insertTimeFrom"-->
<!--              valueFormat="YYYY-MM-DD HH:mm:ss"-->
<!--              format="YYYY-MM-DD"-->
<!--              :locale="locale"-->
<!--              size="small"-->
<!--              style="width: 100%"-->
<!--              placeholder=""-->
<!--            />-->
<!--          </a-col>-->
<!--          <a-col :span="2" style="text-align: center">-->
<!--            - -->
<!--          </a-col>-->
<!--          <a-col :span="11">-->
<!--            <a-date-picker-->
<!--              v-model:value="searchParam.insertTimeTo"-->
<!--              size="small"-->
<!--              valueFormat="YYYY-MM-DD HH:mm:ss"-->
<!--              format="YYYY-MM-DD"-->
<!--              :locale="locale"-->
<!--              style="width: 100%"-->
<!--              placeholder=""-->
<!--            />-->
<!--          </a-col>-->
<!--        </a-row>-->
<!--      </a-form-item-rest>-->

<!--    </a-form-item>-->


<!--    &lt;!&ndash; 数据状态 &ndash;&gt;-->
<!--    <a-form-item name="status"   :label="'数据状态'" class="grid-item"  :colon="false">-->
<!--      <cs-select optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="searchParam.status" id="authorize">-->
<!--        <a-select-option   class="cs-select-dropdown"  v-for="item in productClassify.status"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">-->
<!--          {{item.value}} {{item.label }}-->
<!--        </a-select-option>-->
<!--      </cs-select>-->
<!--    </a-form-item>-->
  </a-form>
</template>

<script setup>
import {inject, onMounted, reactive} from 'vue'
import {productClassify} from "@/view/common/constant";
import CsSelect from "@/components/select/CsSelect.vue";

defineOptions({
  name: 'BaseHeadSearch'
})
const searchParam = reactive({
  customerType:'',
  customerCode:'',
  companyName:'',
  customsCreditRating:'',
  inspectionCode:'',
  creditCode:'',
  declareCode:'',
  companyNameShort:'',
  telephoneNo:'',
  linkmanName:'',
  linkmanDuty:'',
  mobilePhone:'',
  eMail:'',
  address:'',
  companyNameEn:'',
  countryEn:'',
  areaEn:'',
  cityEn:'',
  addressEn:'',
  telephoneNoEn:'',
  linkmanNameEn:'',
  mobilePhoneEn:'',
  eMailEn:'',
  note:'',
  insertUser:'',
  insertTime:'',
  updateUser:'',
  updateTime:'',
  tradeCode:'',
  sid:'',
  linkmanDutyEn:'',
  aeoCode:'',
  masterCustoms:'',
  fax:'',
  postal:'',
  country:'',
  area:'',
  city:'',
  invoiceAddress:'',
  invoiceAddressEn:'',
  deliverAddress:'',
  deliverAddressEn:'',
  status:'',
  insertUserName:'',
  updateUserName:'',
  authorize:'',
  authorizeDeadline:'',
  freeAddress:'',
  freeProperties:'',
  costCenter:'',
  decPersonnel:'',
  decPersonnelTel:'',
  insertTimeFrom:'',
  insertTimeTo:'',
})
/* 定义重置方法(注意前后顺序) */
const resetSearch = () => {
  Object.keys(searchParam).forEach(key => {
    searchParam[key] = '';
  });
}
defineExpose({searchParam,resetSearch});
onMounted(() => {

});

const props = defineProps({
  merchandiseCategoriesMap:{
    type: Array,
    default: () => ([])
  }
})



</script>
