import { ref } from 'vue'
import {localeContent} from "@/view/utils/commonUtil";
export default function useListDataStruct(ieMark) {
    const decLicenseDocusColumns = ref([

        {
            title: localeContent('gw3_docu_code2'/*单证代码 */),
            dataIndex: 'docuCode',
            key: 'docuCode',
            ellipsis: true, // 启用自动换行
        },
        {
            title: localeContent('gw3_guarantee_cert_number'/*单证编号 */) ,
            dataIndex: 'certCode',
            key: 'certCode',
            width: '120px',
            ellipsis: true, // 启用自动换行
        },
    ])

    const containerColumns = ref([
        {
            title: localeContent('gw3_container_no'/*集装箱号 */) ,
            dataIndex: 'containerId',
            key: 'containerId',
            ellipsis: true, // 启用自动换行
        },
        // {
        //     title: '自重(KG)',
        //     dataIndex: 'containerWt',
        //     key: 'containerWt',
        //     ellipsis: true, // 启用自动换行
        // },
        {
            title: localeContent('gw3_container_md'/*集装箱规格 */) ,
            dataIndex: 'containerMd',
            key: 'containerMd',
            ellipsis: true, // 启用自动换行
        },
        {
            title: localeContent('gw3_lcl_flag'/*拼箱标识 */) ,
            dataIndex: 'lclFlag',
            key: 'lclFlag',
            width: '100px',
        },
        // {
        //     title: '商品项号关系',
        //     dataIndex: 'goodsNo',
        //     key: 'goodsNo',
        //     ellipsis: true, // 启用自动换行
        // },
    ],)

    const decListColumns = ref([
        {
            title: localeContent('gw3_serial_no'/*序号 */) ,
            dataIndex: 'serialNo',
            key: 'serialNo',
            align: 'center',
            width: '60px',
            fixed: 'left'
        },
        {
            title: localeContent('gw3_g_no'/*备案序号 */) ,
            dataIndex: 'gNo',
            key: 'gNo',
            align: 'center',
            width: '70px'
        },
        {
            title: localeContent('gw3_code_t_s'/*商品编码 */) ,
            dataIndex: 'codeTS',
            key: 'codeTS',
            align: 'center',
            width: '90px'
        },
        {
            title: localeContent('gw3_ciq_name2'/*监管类别名称 */) ,
            dataIndex: 'ciqName',
            key: 'ciqName',
            align: 'center',
            width: '150px',
            ellipsis: true, // 启用自动换行
        },
        {
            title: localeContent('gw3_b_g_name'/*商品名称 */) ,
            dataIndex: 'gName',
            key: 'gName',
            align: 'center',
            width: '150px',
            ellipsis: true, // 启用自动换行
        },
        {
            title: localeContent('gw3_g_model'/*规格型号 */) ,
            dataIndex: 'gModel',
            key: 'gModel',
            align: 'center',
            width: '150px',
            ellipsis: true, // 启用自动换行
        },
        {
            title: ieMark === 'I' ? localeContent('gw3_qty6'/*成交数量 */) : localeContent('gw3_qty'/*申报数量 */) ,
            dataIndex: 'qty',
            key: 'qty',
            align: 'center',
            width: '70px'
        },
        {
            title: ieMark === 'I' ? localeContent('gw3_unit6'/*成交计量单位 */) : localeContent('gw3_mid_unit'/*申报单位 */) ,
            dataIndex: 'unit',
            key: 'unit',
            align: 'center',
            width: '110px'
        },
        {
            title: localeContent('gw3_b_dec_price'/*单价 */) ,
            dataIndex: 'decPrice',
            key: 'decPrice',
            align: 'center',
            width: '60px'
        },
        {
            title: localeContent('gw3_dec_total2'/*总价 */) ,
            dataIndex: 'decTotal',
            key: 'decTotal',
            align: 'center',
            width: '60px'
        },
        {
            title: localeContent('gw3_curr'/*币制 */),
            dataIndex: 'curr',
            key: 'curr',
            align: 'center',
            width: '80px',
            ellipsis: true, // 启用自动换行
        },
        {
            title: localeContent('gw3_origin_country2'/*原产国(地区) */) ,
            dataIndex: 'originCountry',
            key: 'originCountry',
            align: 'center',
            width: '150px',
            ellipsis: true, // 启用自动换行

        },
        {
            title: localeContent('gw3_district_country2'/*最终目的国(地区) */) ,
            dataIndex: 'destinationCountry',
            key: 'destinationCountry',
            align: 'center',
            width: '150px',
            ellipsis: true, // 启用自动换行
        },
        {
            title: localeContent('gw3_duty_mode'/*征免方式 */) ,
            dataIndex: 'dutyMode',
            key: 'dutyMode',
            align: 'center',
            width: '80px'
        },
        {
            title: localeContent('gw3_qty1'/*法一数量 */) ,
            dataIndex: 'qty1',
            key: 'qty1',
            align: 'center',
            width: '80px'
        },
        {
            title: localeContent('gw3_unit1'/*法一单位 */) ,
            dataIndex: 'unit1',
            key: 'unit1',
            align: 'center',
            width: '90px'
        },
        {
            title: localeContent('gw3_qty2'/*法二数量 */) ,
            dataIndex: 'qty2',
            key: 'qty2',
            align: 'center',
            width: '80px'
        },
        {
            title: localeContent('gw3_unit2'/*法二单位 */) ,
            dataIndex: 'unit2',
            key: 'unit2',
            align: 'center',
            width: '90px'
        },
        {
            title: ieMark === 'I' ? localeContent('gw3_district_code'/*境内目的地 */) : localeContent('gw3_district_code_e'/*境内货源地 */) ,
            dataIndex: 'districtCode',
            key: 'districtCode',
            align: 'center',
            width: '150px'
        },
        {
            title: ieMark === 'I' ? localeContent('gw3_dest_code2'/*境内目的地（行政区域） */) : localeContent('gw3_dest_code3'/*境内货源地（行政区域） */),
            dataIndex: 'destCode',
            key: 'destCode',
            align: 'center',
            width: '150px'
        },
        {
            title: localeContent('gw3_bol_serial_no2'/*原序号 */) ,
            dataIndex: 'bolSerialNo',
            key: 'bolSerialNo',
            align: 'center',
            width: '80px'
        },
    ],)

    const annexFileColumns = ref([
        {
            title: localeContent('gw3_serial_no'/*序号 */) ,
            width: 60,
            dataIndex: 'gNo',
            key: 'gNo',
        },
        {
            title: localeContent('gw3_att_doc_type'/*随附单据类型 */) ,
            dataIndex: 'fileType',
            key: 'fileType',
            width: 160,
            ellipsis: true, // 启用自动换行
        },
        {
            title: localeContent('gw3_file_name'/*文件名 */) ,
            dataIndex: 'fileName',
            key: 'fileName',
            width: 160,
            ellipsis: true, // 启用自动换行
        },
        {
            title: localeContent('gw3_goods_no2'/*商品项号关系 */) ,
            dataIndex: 'goodsNo',
            key: 'goodsNo',
            width: 100,
        },
        {
            title: localeContent('gw3_att_doc_no'/*随附单据编号 */) ,
            dataIndex: 'remark',
            key: 'remark',
            width: 160,
            ellipsis: true, // 启用自动换行
        },
        {
            title: localeContent('gw3_update_by2'/*上传人 */) ,
            dataIndex: 'updateBy',
            key: 'updateBy',
            width: 100,
        },
        {
            title: localeContent('gw3_upload_time'/*上传时间 */) ,
            dataIndex: 'updateTime',
            key: 'updateTime',
            width: 130,
        },
        {
            title: localeContent('gw3_operate'/*操作 */) ,
            key: 'action',
            dataIndex: 'action',
            width: 200,
        },
    ],)

    const ciqCodeColumns = ref(
        [
            {
                title: localeContent('gw3_ciq_name2'/*监管类别名称 */) ,
                dataIndex: 'NAME',
                key: 'NAME',
                align: 'center',
                width: '160px',
                ellipsis: true, // 启用自动换行
            },
            {
                title: localeContent('gw3_type3'/*类型 */) ,
                dataIndex: 'CLASSIFYCNNM',
                key: 'CLASSIFYCNNM',
                align: 'center',
                width: '160px',
                ellipsis: true, // 启用自动换行
            },
            {
                title: localeContent('gw3_hs_code'/*HS编码 */) ,
                dataIndex: 'STAT_CODE',
                key: 'CODE',
                align: 'center',
                width: '120px',
                ellipsis: true, // 启用自动换行
            },
            {
                title: localeContent('gw3_hs_name'/*HS名称 */) ,
                dataIndex: 'CODE_NAME',
                key: 'CODE_NAME',
                align: 'center',
                width: '160px',
                ellipsis: true, // 启用自动换行
            },
        ]
    )

    const companyQualifyColumns = ref(
        [
            {
                title: localeContent('gw3_serial_no'/*序号 */) ,
                dataIndex: 'no',
                key: 'no',
                width: '90px',
            },
            {
                title: localeContent('gw3_ent_qualif_type_code2'/*企业资质类别代码 */) ,
                dataIndex: 'entQualifTypeCode',
                key: 'entQualifTypeCode',
                ellipsis: true, // 启用自动换行
            },
            {
                title: localeContent('gw3_ent_qualif_type_name'/*企业资质类别名称 */) ,
                dataIndex: 'entQualifTypeName',
                key: 'entQualifTypeName',
                ellipsis: true, // 启用自动换行
            },
            {
                title: localeContent('gw3_ent_qualif_no2'/*企业资质编号 */) ,
                dataIndex: 'entQualifNo',
                key: 'entQualifNo',
                ellipsis: true, // 启用自动换行
            },
        ]
    )

    const decManifestCallsColumns = [
        {
            title: localeContent('gw3_manifest_field'/*舱单字段 */) ,
            dataIndex: 'manifestField',
            key: 'manifestField',
            width: '100px',
            ellipsis: true, // 启用自动换行
        },
        {
            title: localeContent('gw3_manifest_data'/*调用舱单数据 */) ,
            dataIndex: 'manifestData',
            key: 'manifestData',
            ellipsis: true, // 启用自动换行
        },
        {
            title: localeContent('gw3_customs_data'/*报关单数据 */) ,
            dataIndex: 'customsData',
            key: 'customsData',
            ellipsis: true, // 启用自动换行
        },
        {
            title: localeContent('gw3_compaer_result'/*比对结果 */) ,
            dataIndex: 'compaerResult',
            key: 'compaerResult',
            ellipsis: true, // 启用自动换行
        },
    ]

    const goodsPermitColumns = ref(
        [
            {
                title: localeContent('gw3_serial_no'/*序号 */) ,
                dataIndex: 'gNo',
                key: 'gNo',
                width: '90px',
            },
            {
                title: localeContent('gw3_lic_type_code3'/*许可证类别代码 */) ,
                dataIndex: 'licTypeCode',
                key: 'licTypeCode',
                ellipsis: true, // 启用自动换行
            },
            {
                title: localeContent('gw3_lic_type_name'/*许可证类别名称 */) ,
                dataIndex: 'licTypeName',
                key: 'licTypeName',
                ellipsis: true, // 启用自动换行
            },
            {
                title: localeContent('gw3_license_no_loc'/*许可证编号 */) ,
                dataIndex: 'licenceNo',
                key: 'licenceNo',
                ellipsis: true, // 启用自动换行
            },
            {
                title: localeContent('gw3_lic_wrtof_detail_no2'/*核销货物序号 */) ,
                dataIndex: 'licWrtofDetailNo',
                key: 'licWrtofDetailNo',
                ellipsis: true, // 启用自动换行
            },
            {
                title: localeContent('gw3_lic_wrtof_qty2'/*核销数量 */) ,
                dataIndex: 'licWrtofQty',
                key: 'licWrtofQty',
                ellipsis: true, // 启用自动换行
            },
            {
                title: localeContent('gw3_lic_wrtof_qty_unit2'/*核销数量单位 */) ,
                dataIndex: 'licWrtofQtyUnit',
                key: 'licWrtofQtyUnit',
                ellipsis: true, // 启用自动换行
            },
            {
                title: localeContent('gw3_produce_date2'/*生产日期 */) ,
                dataIndex: 'produceDate',
                key: 'produceDate',
                ellipsis: true, // 启用自动换行
            },
        ]
    )

    return {
        containerColumns, decLicenseDocusColumns, decListColumns, annexFileColumns, ciqCodeColumns,
        companyQualifyColumns, decManifestCallsColumns, goodsPermitColumns
    }
}
