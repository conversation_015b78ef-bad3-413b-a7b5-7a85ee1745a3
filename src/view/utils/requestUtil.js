import { axios } from '@/utils/requestProxy'
// process.env.NODE_ENV === 'production'?:"main_local"
//post

export function postAction(url, parameter) {
  return window.majesty.httpUtil.postAction(url,parameter)
}
//post method= {post | put}
export function httpAction(url, parameter, method) {
  return window.majesty.httpUtil.httpAction(url,parameter,method)
}
export function httpActionWithHead(url, parameter, method,head) {
  return window.majesty.httpUtil.httpAction(url,parameter,method)
}
//put
export function putAction(url, parameter) {
  return window.majesty.httpUtil.putAction(url,parameter)
}

//get
export function getAction(url, parameter) {
  return window.majesty.httpUtil.getAction(url,parameter)
}


export function uploadAction(url, parameter) {
  return window.majesty.httpUtil.uploadAction(url,parameter)
}
//deleteAction
export function deleteAction(url, parameter) {
  return window.majesty.httpUtil.deleteAction(url,parameter)
}

/**
 * 下载文件
 * @param url 文件路径
 * @param fileName 文件名
 * @param parameter
 * @returns {*}
 */
export function downloadFile(url, fileName, parameter, method,type,fileType, callback) {
  return window.majesty.httpUtil.downloadFile(url, fileName, parameter, method,type, fileType,callback)
}






