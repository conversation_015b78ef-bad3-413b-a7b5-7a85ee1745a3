
import { Modal } from 'ant-design-vue'
import { znI18n, enI18n, thI18n } from "@/assets/lang"
export function countStringLength(str) {
  let length = 0;
  for (let i = 0; i < str.length; i++) {
    const charCode = str.charCodeAt(i);
    if (charCode > 0 && charCode < 128) {
      // 单字节字符（英文、数字、符号等）
      length++;
    } else {
      // 多字节字符（中文字符）
      length += 2;
    }
  }
  return length;
}

export function handleKeyDown(e)  {
  if (e.key === 'Enter') {
    // 阻止回车键的默认行为
    e.preventDefault();
  }
};

export function confirmDialog() {
  const confirm = (param) => {
    switch (param.opType) {
      case 'delete':
        param.content = localeContent('gw3_confirm_delete'/*是否确认删除？*/)
        break;
      case 'declare' :
        param.content = localeContent('gw3_send_ok_tips'/*确认是否发送单一窗口*/)
        break;
      case 'agree':
        param.content = localeContent('gw3_del_agree_ok_tips'/*确认是否删除享惠*/)
        break;
    }
    if (!param.title) {
      param.title = localeContent('tips'/*提示*/)
    }
    if (!param.onCancel) {
      param.onCancel = () => {
      };
    }
    Modal.confirm({
      title: param.title,
      content: param.content,
      onOk: param.onOk,
      onCancel: param.onCancel,
      cancelText: param.cancelText,
      okText: param.okText,
    });
  };

  return {
    confirm,
  };
}

export function handleNullStr(value) {
  if (value == null || value == undefined) {
    return ''
  } else {
    return value
  }
}

export function localeContent(value) {
  if (!window.$vueApp) {
    // 如果不是放在框架中，从本地加载i18n
    return znI18n.global.t(value)
  }
  return window.$vueApp.config.globalProperties.$locale(value)
}
