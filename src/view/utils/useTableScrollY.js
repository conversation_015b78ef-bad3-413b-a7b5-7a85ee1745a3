import { ref, onMounted, nextTick, watch } from 'vue';

/**
 * 用于自定义表格内容区高度和分页栏高度的hook
 * @param {Array} deps 依赖项（如[showSearch, dataSourceList]），变化时自动重新计算
 * @returns { containerRef, paginationRef, tableScrollY, calcTableScrollY }
 */
export function useTableScrollY(deps = []) {
  const containerRef = ref(null);
  const paginationRef = ref(null);
  const tableScrollY = ref(400);

  const calcTableScrollY = () => {
    if (!containerRef.value) return;
    const containerHeight = containerRef.value.clientHeight;
    let paginationHeight = 0;
    if (paginationRef.value) {
      paginationHeight = paginationRef.value.offsetHeight;
    }
    // tableScrollY.value = containerHeight - paginationHeight;
    tableScrollY.value = containerHeight - paginationHeight + 40
    if (tableScrollY.value < 100) tableScrollY.value = 100;
  };

  onMounted(() => {
    nextTick(() => {
      calcTableScrollY();
      window.addEventListener('resize', calcTableScrollY);
    });
  });

  watch(deps, () => {
    nextTick(() => {
      calcTableScrollY();
    });
  });

  return {
    containerRef,
    paginationRef,
    tableScrollY,
    calcTableScrollY,
  };
}
