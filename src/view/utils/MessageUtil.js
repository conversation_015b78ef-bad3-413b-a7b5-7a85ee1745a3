import {notification, message, Modal} from 'ant-design-vue'
import { h } from 'vue';

message.config({
  top: '80px',
  duration:3
});

notification.config(
  {
    duration:5
  });

const MessageUtil = {
  info(content, title, duration) {
    /*notification.info({
      message: title || '信息',
      description: content,
      duration: 3
    })*/

    message.info(content, duration ? duration : 3);
  },
  infoInfinite(content, title, duration) {
    // let h = $vm.$createElement;
    /*notification.success({
      message: title || '信息',
      description: content,
      duration: 1.5
    })*/
    notification.info({
      message: title,
      description: h("p", { innerHTML: content }, null),
      duration: duration ? duration : null,
      top: '90px'
    })
  },
  successInfinite(content, title) {
    // let h = $vm.$createElement;
    /*notification.success({
      message: title || '信息',
      description: content,
      duration: 1.5
    })*/
    notification.success({
      message: title,
      description: h("p", { innerHTML: content }, null),
      duration: null,
      top: '90px'
    })
  },
  success(content, title) {
    /*notification.success({
      message: title || '信息',
      description: content,
      duration: 1.5
    })*/

    message.success(content, 1.5);
  },
  count: 0,
  warning(content, title, color) {
    /*notification.warning({
      message: title || '警告',
      description: content,
      duration: 3
    })*/

    this.count++
    let that = this;
    let curIndex = this.count
    message.warning(content, 3, ()=>that.count--);
    // 1671520984417	消息提示，字体设置红色
    setTimeout(()=>{
      if (color) {
        let ele = document.getElementsByClassName("ant-message-custom-content");
        if (ele.length <= 0) {
          let i = 0;
          while (i < 1000) {
            ele = document.getElementsByClassName("ant-message-custom-content");
            i++
            if (ele.length > 0) {
              break
            }
          }
        }
        let lastElement = ele[curIndex - 1];
        lastElement.style.color = color
      }
    }, 100)
  },
  errorInfinite(content, title) {
    // let h = $vm.$createElement;
    notification.error({
      message: title,
      description: h("p", { innerHTML: content }, null),
      duration: null,
      top: '90px'
    })

    // message.error(content, 0);
  },
  error(content, title) {
    // let h = $vm.$createElement;
    notification.error({
      message: title,
      description: h("p", { innerHTML: content }, null),
      duration: 3,
      top: '90px'
    })

    // message.error(content, 0);
  },
  errorOne(key,content, title) {
    // let h = $vm.$createElement;
    notification.error({
      key,
      message: title,
      description: h("p", { innerHTML: content }, null),
      duration: 10,
      top: '90px'
    })

    // message.error(content, 0);
  },
  warningWithBut(content, title) {
    // let h = $vm.$createElement;
    Modal.warning({
      title: title ,
      content: h("p", { innerHTML: content }, null),
      top: '100px'
    })
  }
  // loading (content) {
  // }
}

export default MessageUtil
