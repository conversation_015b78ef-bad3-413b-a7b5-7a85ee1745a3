import { ref, nextTick } from 'vue';

/**
 * 按钮loading状态控制hook
 * @returns {{
 *   buttonLoadingMap: Ref<Record<string, boolean>>,
 *   setLoading: (key: string, loading: boolean) => void,
 *   withLoading: (key: string, fn: () => Promise<any>) => Promise<any>
 * }}
 */
export function useButtonLoading() {
  // 预设常用的按钮key
  const defaultKeys = {
    submit: false,     // 提交
    save: false,       // 保存
    delete: false,     // 删除
    cancel: false,     // 取消
    confirm: false,    // 确认
    search: false,     // 搜索
    export: false,     // 导出
    import: false,     // 导入
    login: false,      // 登录
    logout: false,     // 登出
    refresh: false,    // 刷新
    reset: false,      // 重置
    upload: false,     // 上传
    download: false,   // 下载
    edit: false,       // 编辑
    add: false,        // 添加
  };

  // 使用Map存储多个按钮的loading状态，并预设常用key
  const buttonLoadingMap = ref({ ...defaultKeys });

  /**
   * 设置指定按钮的loading状态
   * @param key - 按钮的唯一标识
   * @param loading - loading状态
   */
  const setLoading = (key, loading) => {
    console.log('设置loading状态:', key, loading);
    console.log('当前状态:', buttonLoadingMap.value);
    
    // 方式1: 直接赋值（确保属性存在）
    if (!(key in buttonLoadingMap.value)) {
      // 如果 key 不存在，先添加这个 key
      buttonLoadingMap.value[key] = false;
    }
    // 然后更新值
    buttonLoadingMap.value[key] = loading;
    
    // 确保 DOM 更新
    nextTick(() => {
      console.log('更新后状态:', buttonLoadingMap.value);
    });
  };

  /**
   * 包装异步函数，自动处理loading状态
   * @param key - 按钮的唯一标识
   * @param fn - 要执行的异步函数
   * @returns {Promise<*>}
   */
  const withLoading = async (key, fn) => {
    try {
      setLoading(key, true);
      const result = await fn();
      return result;
    } finally {
      setLoading(key, false);
    }
  };

  return {
    buttonLoadingMap,
    setLoading,
    withLoading
  };
}
