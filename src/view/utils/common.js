/**
 * 判断输入值是否为空
 * @param val
 * @returns {boolean}
 */
export function isNullOrEmpty (val) {
    if (val === null) {
        return true
    }
    if (typeof val === 'undefined') {
        return true
    }
    if (val instanceof Date) {
        return false
    }
    if (typeof val === 'object' && Object.keys(val).length === 0) {
        return true
    }
    return String(val).trim().length === 0
}

export const blobSaveFile = (blob, fileName) => {
  const objectUrl = window.URL.createObjectURL(blob)
  const a = document.createElement('a')
  document.body.appendChild(a)
  a.style = "display: none"
  a.href = objectUrl
  a.download = fileName
  a.click()
  document.body.removeChild(a)
}



// // 测试
// const obj = {
//   name: "Alice",
//   greet() {
//     console.log(`Hello, ${this.name}!`);
//   }
// };
//
// const clonedObj = deepClone(obj);
// clonedObj.greet(); // 输出: Hello, Alice!

export function deepClone(obj) {
  if (obj === null || typeof obj !== "object") {
    return obj; // 如果是基本类型或 null，直接返回
  }

  if (typeof obj === "function") {
    return obj.bind({}); // 如果是函数，返回绑定新上下文的函数
  }

  const cloned = Array.isArray(obj) ? [] : {}; // 判断是数组还是对象

  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      cloned[key] = deepClone(obj[key]); // 递归拷贝属性
    }
  }

  return cloned;
}
