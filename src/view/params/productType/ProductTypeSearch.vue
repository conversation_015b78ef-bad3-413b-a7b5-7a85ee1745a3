<template>
  <a-form  layout="inline"  label-align="right"  :label-col="{ style: { width: '100px' } }" :model="searchParam"   class="cs-form  grid-container" >
    <!-- 参数代码 -->
    <a-form-item name="paramCode"   :label="'参数代码'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.paramCode" />
    </a-form-item>

    <!-- 类别编号 -->
    <a-form-item name="categoryCode"   :label="'类别编号'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.categoryCode" />
    </a-form-item>

    <!-- 类别名称 -->
    <a-form-item name="categoryName"   :label="'类别名称'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.categoryName" />
    </a-form-item>




  </a-form>
</template>

<script setup>
import {inject, onMounted, reactive} from 'vue'
import {productClassify} from "@/view/common/constant";
import CsSelect from "@/components/select/CsSelect.vue";

defineOptions({
  name: 'ProductTypeSearch'
})
const searchParam = reactive({

  paramCode:'',
  categoryCode:'',
  categoryName:'',
})
/* 定义重置方法(注意前后顺序) */
const resetSearch = () => {
  Object.keys(searchParam).forEach(key => {
    searchParam[key] = '';
  });
}
defineExpose({searchParam,resetSearch});
onMounted(() => {

});





</script>
