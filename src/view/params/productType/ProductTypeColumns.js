import {baseColumns} from "@/view/common/baseColumns";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {h, reactive, ref} from "vue";
import {productClassify} from "@/view/common/constant";
import {Tag} from "ant-design-vue";
const {baseColumnsExport, baseColumnsShow} = baseColumns()
const { cmbShowRender } = useColumnsRender()


export function getColumns() {

  const commColumns = reactive([
    'paramCode',
    'categoryCode',
    'categoryName',
    'note',
  ])

// 导出字段设置`
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])

  // table表格字段设置
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  // table表格字段设置
  const totalColumns = ref([
    {
      width: 80,
      minWidth:80,
      title: '操作',
      dataIndex: 'operation',
      resizable: true,
      key: 'operation',
      align: 'center',
      fixed: 'left',
    },
    {
      title: '参数代码',
      width: 150,
      align: 'center',
      dataIndex: 'paramCode',
      resizable: true,
      key: 'paramCode',
    },
    {
      title: '类别编号',
      width: 220,
      align: 'center',
      dataIndex: 'categoryCode',
      resizable: true,
      key: 'categoryCode',
    },
    {
      title: '类别名称',
      width: 220,
      align: 'center',
      dataIndex: 'categoryName',
      resizable: true,
      key: 'categoryName',
    },
    {
      title: '备注',
      width: 220,
      align: 'center',
      dataIndex: 'note',
      resizable: true,
      key: 'note',
    },

  ])

  return{
    columnsConfig,
    excelColumnsConfig,
    totalColumns
  }
}


