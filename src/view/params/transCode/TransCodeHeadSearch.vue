<template>
  <a-form  layout="inline"  label-align="right"  :label-col="{ style: { width: '100px' } }" :model="searchParam"   class="cs-form  grid-container" >
    <a-form-item name="bizType" :label="'业务类型'" class="grid-item" :colon="false">
      <cs-select   optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="searchParam.bizType" id="bizType">
        <a-select-option class="cs-select-dropdown" v-for="item in productClassify.bizType"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
          {{item.value}} {{item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>

    <!-- 制单人 -->
    <a-form-item name="insertUserName"  clearable :label="'录入人'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.insertUserName" allow-clear />
    </a-form-item>


  </a-form>
</template>

<script setup>
import {inject, onMounted, reactive} from 'vue'
import {productClassify} from "@/view/common/constant";
import CsSelect from "@/components/select/CsSelect.vue";

defineOptions({
  name: 'NotifyHeadSearch'
})
const searchParam = reactive({

  bizType:'',
  insertUserName:'',
})
/* 定义重置方法(注意前后顺序) */
const resetSearch = () => {
  Object.keys(searchParam).forEach(key => {
    searchParam[key] = '';
  });
}
defineExpose({searchParam,resetSearch});
onMounted(() => {

});





</script>
