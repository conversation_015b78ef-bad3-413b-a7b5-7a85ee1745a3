import {baseColumns, createDateSorter, createNumberSorter, createSorter} from "@/view/common/baseColumns";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {h, reactive, ref} from "vue";
import {productClassify} from "@/view/common/constant";
import {Tag} from "ant-design-vue";

const {baseColumnsExport, baseColumnsShow} = baseColumns()
const { cmbShowRender,formatSpecifiedNumber,formatNumber } = useColumnsRender()


export function getColumns() {

  const commColumns = reactive([
    'bizType',
    'tariffRate',
    'consumptionTaxRate',
    'vatRate',
    'ieAgentFeeRate',
    'hqAgentFeeRate',
    'intlTransType',
    'isContainerShip',
    'containerCap',
    'containerType',
    'intlFreightAmt',
    'portChargesAmt',
    'landFreightAmt',
    'customsFeeAmt',
    'cntrInspFeeAmt',
    'insuranceRate',
    'otherChargesAmt',
    'remark',
    'insertUserName',
    'createTime',
  ])

// 导出字段设置`
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])

  // table表格字段设置
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  // table表格字段设置
  const totalColumns = ref([
    {
      width: 80,
      minWidth:80,
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      resizable: true,
      align: 'center',
      fixed: 'left',
    },
    {
      title: '业务类型',
      width: 150,
      align: 'center',
      dataIndex: 'bizType',
      key: 'bizType',
      resizable: true,
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,productClassify.bizType))
      }
    },
    {
      title: '关税率% ',
      width: 220,
      align: 'center',
      dataIndex: 'tariffRate',
      key: 'tariffRate',
      resizable: true,
      // customRender: ({ text }) => {
      //   return formatSpecifiedNumber(text,true,2)
      // }
    },
    {
      title: '消费税率%',
      width: 220,
      align: 'center',
      dataIndex: 'consumptionTaxRate',
      key: 'consumptionTaxRate',
      resizable: true,
      // customRender: ({ text }) => {
      //   return formatSpecifiedNumber(text,true,2)
      // }
    },
    {
      title: '增值税率% ',
      width: 220,
      align: 'center',
      dataIndex: 'vatRate',
      key: 'vatRate',
      resizable: true,
      // customRender: ({ text }) => {
      //   return formatSpecifiedNumber(text,true,2)
      // }
    },
    {
      title: '进出口公司代理费率%',
      width: 220,
      align: 'center',
      dataIndex: 'ieAgentFeeRate',
      key: 'ieAgentFeeRate',
      resizable: true,
      // customRender: ({ text }) => {
      //   return formatSpecifiedNumber(text,true,2)
      // }
    },
    {
      title: '总公司代理费率%',
      width: 220,
      align: 'center',
      dataIndex: 'hqAgentFeeRate',
      key: 'hqAgentFeeRate',
      resizable: true,
      // customRender: ({ text }) => {
      //   return formatSpecifiedNumber(text,true,2)
      // }
    },
    {
      title: '国际运输类型',
      width: 220,
      align: 'center',
      dataIndex: 'intlTransType',
      key: 'intlTransType',
      resizable: true,
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,productClassify.intlTransType))
      }
    },
    {
      title: '是否集装箱装运',
      width: 220,
      align: 'center',
      dataIndex: 'isContainerShip',
      key: 'isContainerShip',
      resizable: true,
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,productClassify.isFlag))
      }
    },
    {
      title: '集装箱容量',
      width: 220,
      align: 'center',
      dataIndex: 'containerCap',
      key: 'containerCap',
      resizable: true,
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,productClassify.containerCap))
      }
    },
    {
      title: '集装箱型',
      width: 220,
      align: 'center',
      dataIndex: 'containerType',
      key: 'containerType',
      resizable: true,
      // customRender: ({ text }) => {
      //   return h(<div></div>, cmbShowRender(text,productClassify.containerCap))
      // }
    },
    {
      title: '国际运费',
      width: 220,
      align: 'center',
      dataIndex: 'intlFreightAmt',
      key: 'intlFreightAmt',
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '港杂费',
      width: 220,
      align: 'center',
      dataIndex: 'portChargesAmt',
      key: 'portChargesAmt',
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '陆运费',
      width: 220,
      align: 'center',
      dataIndex: 'landFreightAmt',
      key: 'landFreightAmt',
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '通关费',
      width: 220,
      align: 'center',
      dataIndex: 'customsFeeAmt',
      key: 'customsFeeAmt',
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '验柜服务费',
      width: 220,
      align: 'center',
      dataIndex: 'cntrInspFeeAmt',
      key: 'cntrInspFeeAmt',
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '保险费率',
      width: 220,
      align: 'center',
      dataIndex: 'insuranceRate',
      key: 'insuranceRate',
      resizable: true,
    },
    {
      title: '其他费用',
      width: 220,
      align: 'center',
      dataIndex: 'otherChargesAmt',
      key: 'otherChargesAmt',
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '备注',
      width: 220,
      align: 'center',
      dataIndex: 'remark',
      key: 'remark',
      resizable: true,
    },
    {
      title: '录入人',
      width: 220,
      align: 'center',
      dataIndex: 'insertUserName',
      key: 'insertUserName',
      resizable: true,
    },
    {
      title: '录入时间',
      width: 220,
      align: 'center',
      dataIndex: 'createTime',
      key: 'createTime',
      resizable: true,
    },


  ])

  return{
    columnsConfig,
    excelColumnsConfig,
    totalColumns
  }
}


