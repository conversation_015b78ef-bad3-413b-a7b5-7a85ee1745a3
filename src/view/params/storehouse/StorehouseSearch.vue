<template>
  <a-form  layout="inline"  label-align="right"  :label-col="{ style: { width: '100px' } }" :model="searchParam"   class="cs-form  grid-container" >
    <!-- 参数代码 -->
    <a-form-item name="paramCode"   :label="'参数代码'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.paramCode" />
    </a-form-item>

    <!-- 仓库名称 -->
    <a-form-item name="storehouseName"   :label="'仓库名称'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.storehouseName" />
    </a-form-item>

    <!-- 库存组织名称 -->
    <a-form-item name="groupName"   :label="'库存组织名称'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.groupName" />
    </a-form-item>


  </a-form>
</template>

<script setup>
import {inject, onMounted, reactive} from 'vue'
import {productClassify} from "@/view/common/constant";
import CsSelect from "@/components/select/CsSelect.vue";

defineOptions({
  name: 'StorehouseSearch'
})
const searchParam = reactive({

  paramCode:'',
  storehouseName:'',
  groupName:'',
})
/* 定义重置方法(注意前后顺序) */
const resetSearch = () => {
  Object.keys(searchParam).forEach(key => {
    searchParam[key] = '';
  });
}
defineExpose({searchParam,resetSearch});
onMounted(() => {

});





</script>
