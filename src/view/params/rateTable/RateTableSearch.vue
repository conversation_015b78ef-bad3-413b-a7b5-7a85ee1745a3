<template>
  <a-form  layout="inline"  label-align="right"  :label-col="{ style: { width: '100px' } }" :model="searchParam"   class="cs-form  grid-container" >
    <!-- 参数代码 -->
    <a-form-item name="paramCode"   :label="'参数代码'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.paramCode" />
    </a-form-item>

    <!-- 币种 -->
    <a-form-item name="curr"   :label="'币种'" class="grid-item"  :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="searchParam.curr" id="curr">
        <a-select-option   class="cs-select-dropdown"  v-for="item in currList"  :key="item.label  " :value="item.value" :label="item.label">
          {{item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>



  </a-form>
</template>

<script setup>
import {inject, onMounted, reactive, ref} from 'vue'
import {productClassify} from "@/view/common/constant";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
const { getPCode } = usePCode();
defineOptions({
  name: 'PackageInfoSearch'
})
const searchParam = reactive({

  paramCode:'',
  curr:'',
})
/* 定义重置方法(注意前后顺序) */
const resetSearch = () => {
  Object.keys(searchParam).forEach(key => {
    searchParam[key] = '';
  });
}

const currList = ref([])
const pCode = ref('');
defineExpose({searchParam,resetSearch});
onMounted(() => {
  getPCode().then(res => {
    pCode.value = res;
    currList.value = Object.entries(pCode.value.CURR).map(([value, label]) => ({
      label: `${value} ${label}`,
      value
    }));
  })
});





</script>
