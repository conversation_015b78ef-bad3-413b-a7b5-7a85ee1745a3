import {baseColumns} from "@/view/common/baseColumns";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {h, reactive, ref} from "vue";
import {productClassify} from "@/view/common/constant";


const {baseColumnsExport, baseColumnsShow} = baseColumns()
const { cmbShowRender } = useColumnsRender()




export function getColumns() {


  const commColumns = reactive([
    'paramCode',
    'curr',
    'floatRate',
    'note',
  ])

// 导出字段设置`
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])

  // table表格字段设置
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  // table表格字段设置
  const totalColumns = ref([
    {
      width: 80,
      minWidth:80,
      title: '操作',
      dataIndex: 'operation',
      resizable: true,
      key: 'operation',
      align: 'center',
      fixed: 'left',
    },
    {
      title: '参数代码',
      width: 150,
      align: 'center',
      dataIndex: 'paramCode',
      resizable: true,
      key: 'paramCode',
    },
    {
      title: '币种',
      width: 220,
      align: 'center',
      dataIndex: 'curr',
      resizable: true,
      key: 'curr',

    },
    {
      title: '上浮比率',
      width: 220,
      align: 'center',
      dataIndex: 'floatRate',
      resizable: true,
      key: 'floatRate',

    },
    {
      title: '备注',
      width: 220,
      align: 'center',
      dataIndex: 'note',
      resizable: true,
      key: 'note',
    },

  ])
  const totalExportColumns = ref([
    {
      width: 80,
      minWidth:80,
      title: '操作',
      dataIndex: 'operation',
      resizable: true,
      key: 'operation',
      align: 'center',
      fixed: 'left',
    },
    {
      title: '参数代码',
      width: 150,
      align: 'center',
      dataIndex: 'paramCode',
      resizable: true,
      key: 'paramCode',
    },
    {
      title: '币种',
      width: 220,
      align: 'center',
      dataIndex: 'curr',
      resizable: true,
      key: 'curr',

    },
    {
      title: '上浮比率',
      width: 220,
      align: 'center',
      dataIndex: 'floatRateStr',
      resizable: true,
      key: 'floatRateStr',

    },
    {
      title: '备注',
      width: 220,
      align: 'center',
      dataIndex: 'note',
      resizable: true,
      key: 'note',
    },

  ])

  return{
    columnsConfig,
    excelColumnsConfig,
    totalExportColumns,
    totalColumns
  }
}


