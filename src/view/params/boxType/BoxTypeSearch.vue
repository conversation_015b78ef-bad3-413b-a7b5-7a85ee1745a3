<template>
  <a-form  layout="inline"  label-align="right"  :label-col="{ style: { width: '100px' } }" :model="searchParam"   class="cs-form  grid-container" >
    <!-- 参数代码 -->
    <a-form-item name="paramCode"   :label="'参数代码'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.paramCode" />
    </a-form-item>

    <!-- 箱型 -->
    <a-form-item name="boxType"   :label="'箱型'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.boxType" />
    </a-form-item>



  </a-form>
</template>

<script setup>
import {inject, onMounted, reactive, ref} from 'vue'
import {productClassify} from "@/view/common/constant";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
const { getPCode } = usePCode();
defineOptions({
  name: 'BoxTypeSearch'
})
const searchParam = reactive({

  paramCode:'',
  boxType:'',
})
/* 定义重置方法(注意前后顺序) */
const resetSearch = () => {
  Object.keys(searchParam).forEach(key => {
    searchParam[key] = '';
  });
}


defineExpose({searchParam,resetSearch});
onMounted(() => {

});





</script>
