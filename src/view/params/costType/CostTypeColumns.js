import {baseColumns} from "@/view/common/baseColumns";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {h, reactive, ref} from "vue";
import {productClassify} from "@/view/common/constant";
import {Tag} from "ant-design-vue";
const {baseColumnsExport, baseColumnsShow} = baseColumns()
const { cmbShowRender } = useColumnsRender()


export function getColumns() {

  const commColumns = reactive([
    'paramCode',
    'costName',
    'accountSubject',
    'customerSupplier',
    'commonFlag',
    'note',
  ])

// 导出字段设置`
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])

  // table表格字段设置
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  // table表格字段设置
  const totalColumns = ref([
    {
      width: 80,
      minWidth:80,
      title: '操作',
      dataIndex: 'operation',
      resizable: true,
      key: 'operation',
      align: 'center',
      fixed: 'left',
    },
    {
      title: '参数代码',
      width: 150,
      align: 'center',
      dataIndex: 'paramCode',
      resizable: true,
      key: 'paramCode',
    },
    {
      title: '费用名称',
      width: 220,
      align: 'center',
      dataIndex: 'costName',
      resizable: true,
      key: 'costName',
    },
    {
      title: '会计科目',
      width: 220,
      align: 'center',
      dataIndex: 'accountSubject',
      resizable: true,
      key: 'accountSubject',
    },
    {
      title: '客商',
      width: 220,
      align: 'center',
      dataIndex: 'customerSupplier',
      resizable: true,
      key: 'customerSupplier',
    },
    {
      title: '常用标志',
      width: 600,
      align: 'center',
      dataIndex: 'businessTypeList',
      resizable: true,
      key: 'businessTypeList',
    },
    {
      title: '备注',
      width: 220,
      align: 'center',
      dataIndex: 'note',
      resizable: true,
      key: 'note',
    },

  ])
  const exportTotalColumns = ref([
    {
      width: 80,
      minWidth:80,
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      align: 'center',
      fixed: 'left',
    },
    {
      title: '参数代码',
      width: 150,
      align: 'center',
      dataIndex: 'paramCode',
      resizable: true,
      key: 'paramCode',
    },
    {
      title: '费用名称',
      width: 220,
      align: 'center',
      dataIndex: 'costName',
      resizable: true,
      key: 'costName',
    },
    {
      title: '会计科目',
      width: 220,
      align: 'center',
      dataIndex: 'accountSubject',
      resizable: true,
      key: 'accountSubject',
    },
    {
      title: '客商',
      width: 220,
      align: 'center',
      dataIndex: 'customerSupplier',
      resizable: true,
      key: 'customerSupplier',
    },
    {
      title: '常用标志',
      width: 220,
      align: 'center',
      dataIndex: 'commonFlag',
      resizable: true,
      key: 'commonFlag',
    },
    {
      title: '备注',
      width: 220,
      align: 'center',
      dataIndex: 'note',
      resizable: true,
      key: 'note',
    },

  ])

  return{
    columnsConfig,
    excelColumnsConfig,
    exportTotalColumns,
    totalColumns
  }
}


