<template>
  <a-form  layout="inline"  label-align="right"  :label-col="{ style: { width: '100px' } }" :model="searchParam"   class="cs-form  grid-container" >
    <!-- 参数代码 -->
    <a-form-item name="paramCode"   :label="'参数代码'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.paramCode" />
    </a-form-item>

    <!-- 类别编号 -->
    <a-form-item name="costName"   :label="'费用名称'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.costName" />
    </a-form-item>

    <!-- 类别名称 -->
    <a-form-item name="accountSubject"   :label="'会计科目'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.accountSubject" />
    </a-form-item>
    <!-- 类别名称 -->
    <a-form-item name="businessTypeList"   :label="'常用标志'" class="grid-item"  :colon="false">
      <a-select
                v-model:value="searchParam.businessTypeList"
                mode="multiple"
                style="width: 100%"
                placeholder="Please select"
                :options="productClassify.commonBusinessType"
      ></a-select>
    </a-form-item>




  </a-form>
</template>

<script setup>
import {inject, onMounted, reactive} from 'vue'
import {productClassify} from "@/view/common/constant";
import CsSelect from "@/components/select/CsSelect.vue";

defineOptions({
  name: 'ProductTypeSearch'
})
const searchParam = reactive({

  paramCode:'',
  costName:'',
  accountSubject:'',
  commonFlag:'',
  businessTypeList:[],
})
/* 定义重置方法(注意前后顺序) */
const resetSearch = () => {
  Object.keys(searchParam).forEach(key => {
    if (key === 'businessTypeList'){
      searchParam[key] = [];
    }else {
      searchParam[key] = '';
    }

  });
}
defineExpose({searchParam,resetSearch});
onMounted(() => {

});





</script>
