import {baseColumns} from "@/view/common/baseColumns";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {h, reactive, ref} from "vue";
import {productClassify} from "@/view/common/constant";
import {Tag} from "ant-design-vue";
const {baseColumnsExport, baseColumnsShow} = baseColumns()
const { cmbShowRender } = useColumnsRender()


export function getColumns() {

  const commColumns = reactive([
    'paramCode',
    'packUnitCnName',
    'packUnitEnName',
    'tareWt',
    'netWt',
    'volume',
    'note',
  ])

// 导出字段设置`
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])

  // table表格字段设置
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  // table表格字段设置
  const totalColumns = ref([
    {
      width: 80,
      minWidth:80,
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      resizable: true,
      align: 'center',
      fixed: 'left',
    },
    {
      title: '参数代码',
      width: 150,
      align: 'center',
      dataIndex: 'paramCode',
      resizable: true,
      key: 'paramCode',
    },
    {
      title: '包装单位中文名称',
      width: 220,
      align: 'center',
      dataIndex: 'packUnitCnName',
      resizable: true,
      key: 'packUnitCnName',
    },
    {
      title: '包装单位英文名称',
      width: 220,
      align: 'center',
      dataIndex: 'packUnitEnName',
      resizable: true,
      key: 'packUnitEnName',
    },
    {
      title: '单位皮重',
      width: 220,
      align: 'center',
      dataIndex: 'tareWt',
      resizable: true,
      key: 'tareWt',
    },
    {
      title: '单位净重',
      width: 220,
      align: 'center',
      dataIndex: 'netWt',
      resizable: true,
      key: 'netWt',
    },
    {
      title: '单位体积',
      width: 220,
      align: 'center',
      dataIndex: 'volume',
      resizable: true,
      key: 'volume',
    },
    {
      title: '备注',
      width: 220,
      align: 'center',
      dataIndex: 'note',
      resizable: true,
      key: 'note',
    },

  ])

  return{
    columnsConfig,
    excelColumnsConfig,
    totalColumns
  }
}


