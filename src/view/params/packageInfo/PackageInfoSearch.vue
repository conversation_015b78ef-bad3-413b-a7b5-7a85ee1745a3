<template>
  <a-form  layout="inline"  label-align="right"  :label-col="{ style: { width: '200px' } }" :model="searchParam"   class="cs-form  grid-container" >
    <!-- 参数代码 -->
    <a-form-item name="paramCode"   :label="'参数代码'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.paramCode" />
    </a-form-item>

    <!-- 包装单位中文名称 -->
    <a-form-item name="packUnitCnName"     :label="'包装单位中文名称'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.packUnitCnName" />
    </a-form-item>

    <!-- 包装单位英文名称 -->
    <a-form-item name="packUnitEnName"     :label="'包装单位英文名称'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.packUnitEnName" />
    </a-form-item>



  </a-form>
</template>

<script setup>
import {inject, onMounted, reactive} from 'vue'
import {productClassify} from "@/view/common/constant";
import CsSelect from "@/components/select/CsSelect.vue";

defineOptions({
  name: 'PackageInfoSearch'
})
const searchParam = reactive({

  paramCode:'',
  packUnitCnName:'',
  packUnitEnName:'',
})
/* 定义重置方法(注意前后顺序) */
const resetSearch = () => {
  Object.keys(searchParam).forEach(key => {
    searchParam[key] = '';
  });
}
defineExpose({searchParam,resetSearch});
onMounted(() => {

});





</script>
