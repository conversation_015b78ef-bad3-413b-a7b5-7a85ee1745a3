<template>
  <section  >
<!--    <div class="tag-card">
      客户基础信息
    </div>
    <div class="cs-divider"></div>-->
    <a-card size="small" title="仓库" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData"   class=" grid-container">

          <a-form-item name="paramCode" :label="'参数代码'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.paramCode"/>
          </a-form-item>

          <a-form-item name="packUnitCnName" :label="'包装单位中文名称'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.packUnitCnName"/>
          </a-form-item>

          <a-form-item name="packUnitEnName" :label="'包装单位英文名称'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.packUnitEnName"/>
          </a-form-item>
          <a-form-item name="tareWt" :label="'单位皮重'" class="grid-item" :colon="false">
            <a-input
              :disabled="showDisable"
              size="small"
              v-model:value.number="formData.tareWt"
              type="number"
            />
          </a-form-item>
          <a-form-item name="netWt" :label="'单位净重'" class="grid-item" :colon="false">
            <a-input
              :disabled="showDisable"
              size="small"
              v-model:value.number="formData.netWt"
              type="number"
            />
          </a-form-item>
          <a-form-item name="volume" :label="'单位体积'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.volume"/>
          </a-form-item>

          <!-- 客商简称 -->
          <a-form-item name="note" :label="'备注'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.note"/>
          </a-form-item>

          <div class="cs-submit-btn merge-3">
            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right"
                      v-show="props.editConfig.editStatus !== 'SHOW' ">保存
            </a-button>
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(false)">返回</a-button>
          </div>
        </a-form>
      </div>
    </a-card>


  </section>
</template>

<script setup>
import {editStatus, productClassify} from '@/view/common/constant'
import {message} from "ant-design-vue";
import {onMounted, reactive, ref} from "vue";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
import {insertPackageInfo, updatePackageInfo,getSnoPackageInfo} from "@/api/params/params_info";
const { getPCode } = usePCode()



const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});

// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onBack']);

const onBack = (val) => {
  emit('onBack', val);
};

// 是否禁用
const showDisable = ref(false)

// 表单数据
const formData = reactive({
  paramCode: '',
  packUnitCnName: '',
  packUnitEnName: '',
  tareWt: '',
  netWt: '',
  volume: '',
  note: '',


})
// 校验规则
const rules = {

  paramCode: [
    {required: true, message: '不能为空', trigger: 'blur'},
    {max: 30, message: '参数代码长度不能超过30位字节', trigger: 'blur'}
  ],
  packUnitCnName: [
    {required: true, message: '不能为空', trigger: 'blur'},
    {max: 80, message: '包装单位中文名称长度不能超过80位字节', trigger: 'blur'}
  ],
  packUnitEnName: [
    {max: 80, message: '包装单位英文名称长度不能超过80位字节', trigger: 'blur'}
  ],
  note: [
    {max: 200, message: '备注长度不能超过200位字节', trigger: 'blur'}
  ],


}

const pCode = ref('')
// 初始化操作
onMounted(() => {
  getPCode().then(res=>{
    pCode.value = res;
  })
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    showDisable.value = false
    //获取下一个流水号




    Object.assign(formData, {});
    getSno()


  }
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = true
  }
});




// vue3中使用ref,需要先定义，然后在模板中使用。因为在模板渲染之前，DOM 元素还不存在。
const formRef = ref(null);
// 保存
const handlerSave = () => {

  formRef.value
    .validate()
    .then(() => {
      if (props.editConfig && props.editConfig.editStatus === editStatus.ADD){
        insertPackageInfo(formData).then((res)=>{
          if (res.code === 200){
            message.success('新增成功!')
            onBack(true)
          }else {
            message.error(res.message)
          }
        })
      }else if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT){
        updatePackageInfo(formData.sid,formData).then((res)=>{
          if (res.code === 200){
            message.success('修改成功!')
            onBack(true)
          }else {
            message.error(res.message)
          }
        })
      }
    })
    .catch(error => {
      console.log('validate failed', error);
    })
};

const getSno = () => {
  getSnoPackageInfo().then((res)=>{
    if (res.data !=null){
      formData.paramCode =  res.data
    }
  })
}

</script>

<style lang="less" scoped>


</style>



