import {baseColumns} from "@/view/common/baseColumns";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {h, reactive, ref} from "vue";
import {productClassify} from "@/view/common/constant";


const {baseColumnsExport, baseColumnsShow} = baseColumns()
const { cmbShowRender } = useColumnsRender()




export function getColumns() {


  const commColumns = reactive([
    'paramCode',
    'curr',
    'floatRate',
    'note',
    'month',
    'usdRate'
  ])

// 导出字段设置`
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])

  // table表格字段设置
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  // table表格字段设置
  const totalColumns = ref([
    {
      width: 80,
      minWidth:80,
      title: '操作',
      dataIndex: 'operation',
      resizable: true,
      key: 'operation',
      align: 'center',
      fixed: 'left',
    },
    {
      title: '月份',
      width: 220,
      align: 'center',
      dataIndex: 'month',
      resizable: true,
      key: 'month',
    },
    {
      title: '币种',
      width: 220,
      align: 'center',
      dataIndex: 'curr',
      resizable: true,
      key: 'curr',
    },
    {
      title: '汇率',
      width: 220,
      align: 'center',
      dataIndex: 'rate',
      resizable: true,
      key: 'rate',
    },
    {
      title: '美元汇率',
      width: 220,
      align: 'center',
      dataIndex: 'usdRate',
      resizable: true,
      key: 'usdRate',
    },
    // {
    //   title: '备注',
    //   width: 220,
    //   align: 'center',
    //   dataIndex: 'note',
    //   resizable: true,
    //   key: 'note',
    // },

  ])
  const totalExportColumns = ref([
    {
      width: 80,
      minWidth:80,
      title: '操作',
      dataIndex: 'operation',
      resizable: true,
      key: 'operation',
      align: 'center',
      fixed: 'left',
    },
    {
      title: '参数代码',
      width: 150,
      align: 'center',
      dataIndex: 'paramCode',
      resizable: true,
      key: 'paramCode',
    },
    {
      title: '币种',
      width: 220,
      align: 'center',
      dataIndex: 'curr',
      resizable: true,
      key: 'curr',

    },
    {
      title: '汇率',
      width: 220,
      align: 'center',
      dataIndex: 'rate',
      resizable: true,
      key: 'rate',

    },
    // {
    //   title: '备注',
    //   width: 220,
    //   align: 'center',
    //   dataIndex: 'note',
    //   resizable: true,
    //   key: 'note',
    // },

  ])

  return{
    columnsConfig,
    excelColumnsConfig,
    totalExportColumns,
    totalColumns
  }
}


