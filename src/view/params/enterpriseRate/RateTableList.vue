<template>
  <section  class="dc-section">
    <div class="cs-action"  >
      <!-- 查询列表区域 -->
      <div class="cs-search">
        <a-card :bordered="false">
          <bread-crumb>
            <div ref="area_head">
              <div class="search-btn">
                <a-button size="small" type="primary" class="cs-margin-right cs-refresh" @click="handlerRefresh" v-show="showSearch">
                  <template #icon>
                    <GlobalIcon type="redo" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" class="cs-margin-right" @click="handlerSearch">
                  {{localeContent('m.common.button.query')}}
                  <template #icon>
                    <GlobalIcon type="search" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" danger class="cs-margin-right cs-warning" @click="handleShowSearch">
                  <template #icon>
                    <GlobalIcon v-show="!showSearch" type="down" style="color:#fff"/>
                    <GlobalIcon v-show="showSearch" type="up" style="color:#fff"/>
                  </template>
                </a-button>
              </div>
            </div>
          </bread-crumb>
          <div class="separateLine"></div>
          <div ref="area_search">
            <div v-show="showSearch">
              <RateTableSearch ref="headSearch" />
            </div>
          </div>
        </a-card>
      </div>
      <!-- 操作按钮区域 -->
      <div class="cs-action-btn">
          <div class="cs-action-btn-item" v-has="['yc-cs:enterpriseRate:add']">
            <a-button size="small" @click="handlerAdd" >
              <template #icon>
                <GlobalIcon type="plus" style="color:green"/>
              </template>
              {{localeContent('m.common.button.add')}}
            </a-button>
          </div>
          <div class="cs-action-btn-item" v-has="['yc-cs:enterpriseRate:edit']">
            <a-button  size="small"  @click="handlerEdit">
              <template #icon>
                <GlobalIcon type="form" style="color:orange"/>
              </template>
              {{localeContent('m.common.button.update')}}
            </a-button>
          </div>
          <div class="cs-action-btn-item" v-has="['yc-cs:enterpriseRate:delete']">
            <a-button  size="small" :loading="deleteLoading" @click="handlerDelete">
              <template #icon>
                <GlobalIcon type="delete" style="color:red"/>
              </template>
              {{localeContent('m.common.button.delete')}}
            </a-button>
          </div>
          <div class="cs-action-btn-item" v-has="['yc-cs:enterpriseRate:export']">
            <a-button  size="small" :loading="exportLoading" @click="handlerExport">
              <template #icon>
                <GlobalIcon type="folder-open" style="color:orange"/>
              </template>
              {{localeContent('m.common.button.export')}}
            </a-button>
          </div>



        <div class="cs-action-btn-settings">
          <!-- 自定义显示组件 -->
          <CsTableColSettings
            :resId="tableKey"
            :tableKey="tableKey+'-client_code'"
            :initSettingColumns="originalColumns"
            :showColumnSettings="true"
            @customColumnChange="customColumnChange"
          >
          </CsTableColSettings>
        </div>





      </div>

      <!-- 表格区域 -->
      <div  v-if="showColumns && showColumns.length > 0">
        <s-table
          :animate-rows="false"
          ref="tableRef"
          class="cs-action-item"
          size="small"
          :scroll="{ y: tableHeight,x:400 }"
          bordered
          column-drag
          :pagination="false"
          :columns="showColumns.length > 0 ?showColumns:totalColumns"
          :data-source="dataSourceList"
          :row-selection="{  selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          row-key="sid"
        >
          <!-- 操作 -->
          <template #bodyCell="{ column, text, record }">
<!--            <template v-if="['note'].includes(column.dataIndex)">-->
<!--              <div>-->
<!--                <a-input-->
<!--                  v-if="editableData[record.sid]"-->
<!--                  v-model:value="editableData[record.sid][column.dataIndex]"-->
<!--                  style="margin: -5px 0"-->
<!--                />-->
<!--                <template v-else>-->
<!--                  {{ text }}-->
<!--                </template>-->
<!--              </div>-->
<!--            </template>-->



            <template v-if="['rate'].includes(column.dataIndex)">
              <div>
                <span v-if="editableData[record.sid]">
                   <a-input-number
                     v-model:value="editableData[record.sid][column.dataIndex]"
                     style="margin: -5px 0"
                   />
              </span>
                    {{ text }}
              </div>
            </template>
            <template v-if="['usdRate'].includes(column.dataIndex)">
              <div>
                <span v-if="editableData[record.sid]">
                   <a-input-number
                     v-model:value="editableData[record.sid][column.dataIndex]"
                     style="margin: -5px 0"
                   />
              </span>
                    {{ text }}
              </div>
            </template>

            <template v-if="['curr'].includes(column.dataIndex)">
              <div>
                <span v-if="editableData[record.sid]">
                <a-select
                  v-model:value="editableData[record.sid][column.dataIndex]"
                  style="width: 100%"
                  placeholder="Please select"
                  :options="currList"
                ></a-select>

              </span>
                <span v-else>
                      {{ formatCurrency(record.curr) }}
                </span>
              </div>
            </template>


            <template v-else-if="column.dataIndex === 'operation'">
              <div class="editable-row-operations">
          <span v-if="editableData[record.sid]">
            <a-typography-link @click="save(record.sid)">保存 </a-typography-link>
             <a @click="cancel(record.sid)">取消</a>
          </span>
                <span v-else>
            <a @click="edit(record.sid)">编辑</a>
          </span>
              </div>
            </template>
          </template>
        </s-table>
      </div>
      <!-- 分页 -->
      <div class=cs-pagination           v-if="showColumns && showColumns.length > 0">
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer  :page-size="page.pageSize" :total="page.total"  @change="onPageChange">
          <template #buildOptionText="props">
            <span >{{ props.value }}条/页</span>
          </template>
        </a-pagination>

      </div>
    </div>

    <!-- 新增 编辑数据 -->
<!--    <div v-if="!show">-->
<!--      <RateTableEdit :editConfig="editConfig" @on-back="handlerOnBack" />-->
<!--    </div>-->


    <!-- 导入数据 -->
<!--    <ImportIndex :importShow="importShow" :importConfig="importConfig"   @onImportSuccess="importSuccess"></ImportIndex>-->


  </section>


</template>

<script setup>
/* 使用自定义 Hook 函数 */
import {useCommon} from '@/view/common/useCommon'
import {createVNode, onMounted, provide, reactive, ref, watch} from "vue";
import RateTableSearch from "@/view/params/enterpriseRate/RateTableSearch";
import {getColumns} from "@/view/params/enterpriseRate/RateTableColumns";
import {message, Modal} from "ant-design-vue";
import BreadCrumb from "@/components/breadcrumb/BreadCrumb.vue";
import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
import {
  deleteEnterpriseRateTable,
  getSnoEnterpriseRateTable,
  insertEnterpriseRateTable,
  updateEnterpriseRateTable,
} from "@/api/params/params_info";
const { totalColumns ,totalExportColumns} = getColumns()
import {ImportIndex} from 'yao-import'
import {localeContent} from "../../utils/commonUtil";
import { useImport } from "@/view/common/useImport";
import ycCsApi from "@/api/ycCsApi";
import CsTableColSettings from "@/components/settings/CsTableColSettings.vue";
import {useRoute} from "vue-router";
import {editStatus, productClassify} from "@/view/common/constant";
import {deepClone} from "@/view/utils/common";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
const { importConfig } = useImport()
const { getPCode } = usePCode();

/* 引入通用方法 */
const {
  editConfig,
  show,
  page,
  showSearch,
  headSearch,
  handleEditByRow,
  handleViewByRow,
  operationEdit,
  onPageChange,
  handleShowSearch,
  handlerSearch,
  dataSourceList,
  tableLoading,
  getTableScroll,
  exportLoading,
  getList,
  ajaxUrl,
  doExport,
  handlerRefresh,
  gridData

} = useCommon()



defineOptions({
  name: 'RateTableList',
});


const tableRef = ref(null);
const editableData = reactive({});
const importShow = ref(false)

const currList = ref([])
const pCode = ref('');

const formatCurrency = (code) => {
  const option = currList.value.find(opt => opt.value === code);
  return option ? option.label : code;
};

onMounted(fn => {


  ajaxUrl.selectAllPage = ycCsApi.params.enterpriseRate.list
  ajaxUrl.exportUrl = ycCsApi.params.enterpriseRate.export

  tableHeight.value = getTableScroll(100,'');

  getList()

  initCustomColumn()
  let param = {
    paramsType: 'CURR',
  };

  getPCode().then(res => {
    pCode.value = res;
    currList.value = Object.entries(pCode.value.CURR).map(([value, label]) => ({
      label: `${value} ${label}`,
      value
    }));
  })


})

const tableHeight = ref('')





/* 选中事件(这里的事件，顺序不能乱，必须key,后面点跟实体，不然回显出问题。) */
const onSelectChange = (selectedRowKeys, rowSelectData) => {
  gridData.selectedData = rowSelectData;
  gridData.selectedRowKeys = selectedRowKeys;
};


/* 按钮loading */
const deleteLoading = ref(false)






/* 新增数据 */
const handlerAdd = ()=>{
  const now = new Date();
  // 获取当前年份（如 2025）
  const currentYear = now.getFullYear();

  // 获取当前月份（0-11 → 1-12），并补零成两位数（如 "04"）
  const currentMonth = String(now.getMonth() + 1).padStart(2, '0');

  // 拼接成 "YYYYMM" 格式（如 "202504"）
  const formattedMonth = `${currentYear}${currentMonth}`;
    const sid = Date.now() + "add";
    const newData = {
      sid: `${sid}`,
      month: formattedMonth,
      curr: "",
      floatRate: null,
      note: ""
    };


    // 在异步回调中更新数据源
    dataSourceList.value.unshift(newData);
    dataSourceList.value = [...dataSourceList.value];
    editableData[sid] = deepClone(newData);
}


/* 编辑数据 */
const handlerEdit = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1){
    message.warning('只能选择一条数据')
    return
  }
  let sid = gridData.selectedRowKeys[0]

  editableData[sid] = deepClone(dataSourceList.value.filter(item => sid === item.sid)[0]);
}


/* 删除数据 */
const handlerDelete = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  // 弹出确认框
  Modal.confirm({
    title: '提醒?',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '删除',
    cancelText: '取消',
    content: '确认删除所选项吗？',
    onOk() {
      deleteLoading.value = true
      deleteEnterpriseRateTable(gridData.selectedRowKeys).then(res => {
        if (res.code === 200) {
          message.success("删除成功！")
          getList()
        }
      }).finally(() => {
        deleteLoading.value = false
      })
    },
    onCancel() {

    },
  });

}

const edit = (sid) => {
  editableData[sid] = deepClone(dataSourceList.value.filter(item => sid === item.sid)[0]);
};
const save = (sid) => {

  Object.assign(dataSourceList.value.filter(item => sid === item.sid)[0], editableData[sid]);
  // 1. 找到数据源中与当前 sid 匹配的项
  const targetItem = dataSourceList.value.find(item => item.sid === sid);

  // 2. 将临时编辑的数据（editableData[sid]）合并到目标项中
  Object.assign(targetItem, editableData[sid]);


  if (targetItem.curr == null || targetItem.curr === "" || targetItem.curr === undefined ){
    message.error('币种不能为空！')
    return
  }

  // 3. 删除临时编辑的数据，清理内存
  delete editableData[sid];
  if (sid.includes("add")){
    //执行保存请求
    insertEnterpriseRateTable(targetItem).then((res)=>{
      if (res.code === 200){
        message.success('新增成功!')
      }else {
        message.error(res.message)
      }
    }).finally(()=>{
      getList()
    })
  }else {
    //执行保存请求
    updateEnterpriseRateTable(sid,targetItem).then((res)=>{
      if (res.code === 200){
        message.success('修改成功!')
      }else {
        message.error(res.message)
      }
    }).finally(()=>{
      getList()
    })
  }


};
const cancel = (sid) => {
  delete editableData[sid];
  getList()
};


/* 打开导入 */
// const handlerImport = ()=>{
//   importShow.value = !importShow.value
//   // 参数外部重置 可以选择在onMounted里面重置 或者 打开时重置
//   importConfig.taskCode = 'base_client_import'
// }


/* 导入成功后事件 */
// const importSuccess = ()=>{
//   importShow.value =!importShow.value
//   getList()
// }


/* 导出事件 */
const handlerExport = () =>{
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  const timestamp = `${year}${month}${day}${hours}${minutes}${seconds}`
  doExport( `企业汇率表${timestamp}.xlsx`,totalColumns)
}



/* 自定义设置 */
/* 显示列数据 */
const showColumns =  ref([])

/* 唯一键 */
const tableKey = ref('')

tableKey.value = window.$vueApp ? window.majesty.router.currentRoute.value.path : useRoute().path
const originalColumns = ref()




/* 自定义显示列初始化操作 */
const initCustomColumn = () => {
  // 这里是拷贝是属于
  let tempColumns = deepClone(totalColumns.value)
  let dealColumns = []
  // 使用map遍历会丢失customRender方法，所以使用forEach
  tempColumns.map((item) => {
    let newObj = Object.assign({}, item);
    newObj["visible"] = true;
    // 需要将customRender 方法追加到新对象中
    if (item.customRender) {
      newObj["customRender"] = item.customRender;
    }
    dealColumns.push(newObj);
  });
  //原始列信息
  originalColumns.value = dealColumns;
}



/* 选中visible为true的数据进行显示 */
const customColumnChange = (settingColumns)  => {
  totalColumns.value = settingColumns.filter((item) => item.visible === true);
  showColumns.value = [...totalColumns.value]
  //  深拷贝之前实现 丢失了方法，所以修改了utils的deepClone方法，这里不要了
  // showColumns.value.map((item) => {
  //   let temp = totalColumns.value.find((tempItem) => tempItem.key === item.key);
  //   if (temp && temp.customRender) {
  //     item.customRender = temp.customRender;
  //   }
  // });
}





/* 监控 dataSourceList */
watch(dataSourceList, (newValue, oldValue) => {
  showColumns.value = [...totalColumns.value];
  // 将showColumns的数据属性 和 初始属性进行比对，如果初始属性存在customRender 方法，追加到showColumns中
},{deep:true})






</script>

<style lang="less" scoped>


</style>
