<template>
  <a-form ref="formRef" layout="inline" label-align="right" :label-col="{ style: { width: '100px' } }"
          :model="searchParam"
          class="cs-form  grid-container">
    <!-- 参数代码 -->
    <a-form-item name="paramCode" :label="'参数代码'" class="grid-item" :colon="false">
      <a-input size="small" v-model:value="searchParam.paramCode"/>
    </a-form-item>

    <!-- 价格条款 -->
    <a-form-item name="priceTerm" :label="'价格条款'" class="grid-item" :colon="false">
      <a-input size="small" v-model:value="searchParam.priceTerm"/>
    </a-form-item>

    <!-- 价格条款描述 -->
    <a-form-item name="priceTermDesc" :label="'价格条款描述'" class="grid-item" :colon="false">
      <a-input size="small" v-model:value="searchParam.priceTermDesc"/>
    </a-form-item>
  </a-form>
</template>

<script setup>
import { ref } from 'vue'

const searchParam = ref({
  paramCode: '',
  priceTerm: '',
  priceTermDesc: ''
})

const formRef = ref(null)

const resetSearch = () => {
  formRef.value.resetFields()
}

defineExpose({searchParam, resetSearch})

defineOptions({
  name: 'PriceTermsSearch'
})
</script>
