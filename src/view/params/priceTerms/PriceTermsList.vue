<template>
  <section class="dc-section">
    <div class="cs-action">
      <!-- 查询列表区域 -->
      <div class="cs-search">
        <a-card :bordered="false">
          <bread-crumb>
            <div ref="area_head">
              <div class="search-btn">
                <a-button size="small" type="primary" class="cs-margin-right cs-refresh" @click="handlerRefresh"
                          v-show="showSearch">
                  <template #icon>
                    <GlobalIcon type="redo" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" class="cs-margin-right" @click="handlerSearch">
                  {{ localeContent('m.common.button.query') }}
                  <template #icon>
                    <GlobalIcon type="search" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" danger class="cs-margin-right cs-warning"
                          @click="handleShowSearch">
                  <template #icon>
                    <GlobalIcon v-show="!showSearch" type="down" style="color:#fff"/>
                    <GlobalIcon v-show="showSearch" type="up" style="color:#fff"/>
                  </template>
                </a-button>
              </div>
            </div>
          </bread-crumb>
          <div class="separateLine"></div>
          <div ref="area_search">
            <div v-show="showSearch">
              <price-terms-search ref="headSearch"/>
            </div>
          </div>
        </a-card>
      </div>
      <!-- 操作按钮区域 -->
      <div class="cs-action-btn">
        <div class="cs-action-btn-item" v-has="['yc-cs:priceTerms:add']">
          <a-button :loading="addLoading" size="small" @click="handleAdd">
            <template #icon>
              <GlobalIcon type="plus" style="color:green"/>
            </template>
            {{ localeContent('m.common.button.add') }}
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:priceTerms:edit']">
          <a-button size="small" @click="handleEdit">
            <template #icon>
              <GlobalIcon type="form" style="color:orange"/>
            </template>
            {{ localeContent('m.common.button.update') }}
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:priceTerms:delete']">
          <a-button size="small" :loading="deleteLoading" @click="handleDelete">
            <template #icon>
              <GlobalIcon type="delete" style="color:red"/>
            </template>
            {{ localeContent('m.common.button.delete') }}
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:priceTerms:export']">
          <a-button size="small" :loading="exportLoading" @click="handleExport">
            <template #icon>
              <GlobalIcon type="folder-open" style="color:orange"/>
            </template>
            {{ localeContent('m.common.button.export') }}
          </a-button>
        </div>

        <div class="cs-action-btn-settings">
          <!-- 自定义显示组件 -->
          <CsTableColSettings
            :resId="tableKey"
            :tableKey="tableKey+'-client_code'"
            :initSettingColumns="originalColumns"
            :showColumnSettings="true"
            @customColumnChange="customColumnChange"
          >
          </CsTableColSettings>
        </div>
      </div>

      <!-- 表格区域 -->
      <div v-if="showColumns && showColumns.length > 0">
        <s-table
          :animate-rows="false"
          ref="tableRef"
          column-drag
          class="cs-action-item"
          size="small"
          :scroll="{ y: tableHeight, x: 400 }"
          bordered
          :pagination="false"
          :columns="showColumns"
          :data-source="dataSourceList"
          :row-selection="{ selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          row-key="sid"
        >
          <!-- 操作 -->
          <template #bodyCell="{ column, text, record }">
            <div v-if="['priceTerm', 'priceTermDesc', 'note'].includes(column.dataIndex)">
              <a-input
                v-if="editableData[record.sid]"
                v-model:value="editableData[record.sid][column.dataIndex]"
                :maxlength="column.maxLength"
                style="margin: -5px 0"
              />
              <span v-else>{{ text }}</span>
            </div>

            <div class="editable-row-operations" v-else-if="column.dataIndex === 'operation'">
              <span v-if="editableData[record.sid]">
                <a-typography-link @click="save(record.sid)">保存 </a-typography-link>
                <a @click="cancel(record.sid)">取消</a>
              </span>
              <span v-else>
                <a @click="edit(record.sid)">编辑</a>
              </span>
            </div>
          </template>
        </s-table>
      </div>
      <!-- 分页 -->
      <div class=cs-pagination v-if="showColumns && showColumns.length > 0">
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer :page-size="page.pageSize"
                      :total="page.total" @change="onPageChange">
          <template #buildOptionText="props">
            <span>{{ props.value }}条/页</span>
          </template>
        </a-pagination>
      </div>
    </div>
  </section>
</template>

<script setup>
import BreadCrumb from '@/components/breadcrumb/BreadCrumb.vue'
import PriceTermsSearch from '@/view/params/priceTerms/PriceTermsSearch.vue'
import CsTableColSettings from '@/components/settings/CsTableColSettings.vue'
import { createVNode, onMounted, ref, toRaw } from 'vue'
import { useRoute } from 'vue-router'
import { message, Modal } from 'ant-design-vue'
import ExclamationCircleOutlined from '@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined'
import { useCommon } from '@/view/common/useCommon'
import { localeContent } from '@/view/utils/commonUtil'
import { deepClone } from '@/view/utils/common'
import ycCsApi from '@/api/ycCsApi'
import { getColumns } from '@/view/params/priceTerms/PriceTermsColumns'
import { deletePriceTerms, insertPriceTerms, updatePriceTerms } from '@/api/params/params_info'

/* 引入通用方法 */
const {
  editConfig,
  page,
  showSearch,
  headSearch,
  onPageChange,
  handleShowSearch,
  handlerSearch,
  dataSourceList,
  tableLoading,
  getTableScroll,
  exportLoading,
  getList,
  ajaxUrl,
  doExport,
  handlerRefresh,
  gridData
} = useCommon()

// 配置表格列、导出列
const {tableColumns, excelColumns} = getColumns()

// 原始显示列
const originalColumns = ref((() => {
  for (let columns of tableColumns) {
    columns.visible = true
  }
  return tableColumns
})())

// 当前显示列
const showColumns = ref([...originalColumns.value])

// 自定义显示列更改回调
function customColumnChange(settingColumns) {
  showColumns.value = settingColumns.filter(item => item.visible === true)
}

// 表格唯一key
const tableKey = ref(window['$vueApp'] ? window.majesty.router.currentRoute.value.path : useRoute().path)

// 表格高度
const tableHeight = ref(getTableScroll(100, ''))

// 请求url
ajaxUrl.selectAllPage = ycCsApi.params.priceTerms.list
ajaxUrl.exportUrl = ycCsApi.params.priceTerms.export

// 选中行更改回调
function onSelectChange(selectedRowKeys, rowSelectData) {
  gridData.selectedData = rowSelectData
  gridData.selectedRowKeys = selectedRowKeys
}

// 编辑数据
const editableData = ref({})

/**
 * 新增数据
 */
const addLoading = ref(false)

function handleAdd() {
  addLoading.value = true
  try {
    const paramCodes = toRaw(dataSourceList.value).map(item => Number(item.paramCode))
      .sort((n1, n2) => n2 - n1)
    const nextParamCode = String((paramCodes && paramCodes.length > 0) ? paramCodes[0] + 1 : 1).padStart(3, '0')
    const sid = Date.now() + String(Math.floor(Math.random() * 1000)) + 'add'
    const newData = {
      sid,
      paramCode: nextParamCode,
      priceTerm: '',
      priceTermDesc: '',
      note: ''
    }
    const dataList = [...toRaw(dataSourceList.value)]
    dataList.unshift(newData)
    dataSourceList.value = dataList
    editableData.value[sid] = deepClone(newData)
  } finally {
    addLoading.value = false
  }
}

/**
 * 编辑数据
 */
function handleEdit() {
  if (gridData.selectedRowKeys.length <= 0) {
    message.warning('请选择一条编辑数据!')
    return
  }
  if (gridData.selectedRowKeys.length > 1) {
    message.warning('只能选择一条编辑数据!')
    return
  }
  const sid = gridData.selectedRowKeys[0]
  const currentData = toRaw(dataSourceList.value.filter(item => item.sid === sid)[0])
  editableData.value[sid] = deepClone(currentData)
}

/**
 * 删除数据
 */
const deleteLoading = ref(false)

function handleDelete() {
  if (gridData.selectedRowKeys.length <= 0) {
    message.warning('请选择删除!')
    return
  }
  Modal.confirm({
    title: '提醒?',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '删除',
    cancelText: '取消',
    content: '确认删除所选项吗？',
    async onOk() {
      try {
        deleteLoading.value = true
        const res = await deletePriceTerms(gridData.selectedRowKeys)
        if (res && res.success) {
          message.success("删除成功！")
          getList()
        }
      } finally {
        deleteLoading.value = false
      }
    },
    onCancel() {
    }
  })
}

/**
 * 导出数据
 */
function handleExport() {
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  const timestamp = `${year}${month}${day}${hours}${minutes}${seconds}`
  doExport(`价格条款${timestamp}.xlsx`, ref(excelColumns))
}

/**
 * 行内编辑
 * @param sid sid
 */
function edit(sid) {
  const currentData = dataSourceList.value.filter(item => sid === item.sid)[0]
  editableData.value[sid] = deepClone(toRaw(currentData))
}

/**
 * 行内保存
 * @param sid sid
 */
async function save(sid) {
  const paramObj = toRaw(editableData.value[sid])
  if (!paramObj.priceTerm) {
    message.error('价格条款不能为空！')
    return
  }
  const isAdd = sid.endsWith('add')
  try {
    const res = await (isAdd ? insertPriceTerms(paramObj) : updatePriceTerms(sid, paramObj))
    if (res && res.success) {
      delete editableData.value[sid]
      const currentIndex = dataSourceList.value.findIndex(item => item.sid === sid)
      if (currentIndex > -1) {
        const arr = dataSourceList.value.slice(0, currentIndex)
        arr.push(...[res.data, ...dataSourceList.value.slice(currentIndex + 1)])
        dataSourceList.value = arr
      }
      message.success(isAdd ? '新增成功!' : '修改成功!')
    } else {
      message.error(res.message)
    }
  } finally {
    // getList()
  }
}

/**
 * 行内取消
 * @param sid sid
 */
function cancel(sid) {
  delete editableData.value[sid]
  if (sid.endsWith("add")) {
    dataSourceList.value = dataSourceList.value.filter(item => item.sid !== sid)
  }
}

onMounted(() => {
  getList()
})

defineOptions({
  name: 'PriceTermsList',
})
</script>

<style lang="less" scoped>
</style>
