<template>
  <a-form ref="formRef" layout="inline" label-align="right" :label-col="{ style: { width: '100px' } }"
          :model="searchParam"
          class="cs-form  grid-container">
    <!-- 参数代码 -->
    <a-form-item name="paramCode" :label="'参数代码'" class="grid-item" :colon="false">
      <a-input size="small" v-model:value="searchParam.paramCode"/>
    </a-form-item>

    <!-- 城市中文名称 -->
    <a-form-item name="cityCnName" :label="'城市中文名称'" class="grid-item" :colon="false">
      <a-input size="small" v-model:value="searchParam.cityCnName"/>
    </a-form-item>

    <!-- 城市英文名称 -->
    <a-form-item name="cityEnName" :label="'城市英文名称'" class="grid-item" :colon="false">
      <a-input size="small" v-model:value="searchParam.cityEnName"/>
    </a-form-item>
  </a-form>
</template>

<script setup>
import { ref } from 'vue'

const searchParam = ref({
  paramCode: '',
  cityCnName: '',
  cityEnName: ''
})

const formRef = ref(null)

const resetSearch = () => {
  formRef.value.resetFields()
}

defineExpose({searchParam, resetSearch})

defineOptions({
  name: 'CitySearch'
})
</script>
