import { baseColumns } from '@/view/common/baseColumns'

const {baseColumnsExport, baseColumnsShow} = baseColumns()

export function getColumns() {
  const commColumns = [
    'paramCode',
    'cityCnName',
    'cityEnName',
    'note'
  ]

  // 导出字段设置
  const excelColumnsConfig = [
    ...commColumns,
    ...baseColumnsExport
  ]

  // 表格字段设置
  const columnsConfig = [
    ...commColumns,
    ...baseColumnsShow
  ]

  // table表格字段设置
  const totalColumns = [
    {
      width: 80,
      minWidth: 80,
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      resizable: true,
      align: 'center',
      fixed: 'left',
    },
    {
      title: '参数代码',
      minWidth: 100,
      align: 'center',
      dataIndex: 'paramCode',
      resizable: true,
      key: 'paramCode',
    },
    {
      title: '城市中文名称',
      minWidth: 200,
      align: 'center',
      dataIndex: 'cityCnName',
      resizable: true,
      key: 'cityCnName',
      maxLength: 50
    },
    {
      title: '城市英文名称',
      minWidth: 200,
      align: 'center',
      dataIndex: 'cityEnName',
      resizable: true,
      key: 'cityEnName',
      maxLength: 80
    },
    {
      title: '备注',
      minWidth: 250,
      align: 'center',
      dataIndex: 'note',
      resizable: true,
      key: 'note',
      maxLength: 200
    }
  ]

  return {
    tableColumns: totalColumns.filter(item => columnsConfig.includes(item.key)),
    excelColumns: totalColumns.filter(item => excelColumnsConfig.includes(item.key))
  }
}
