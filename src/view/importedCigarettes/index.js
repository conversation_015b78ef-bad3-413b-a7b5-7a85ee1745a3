import { defineAsyncComponent } from "vue"

export default  [
  {
    path: '/tobacco/importedCigarettes/plan',
    name: 'PlanList',
    meta: {
      title: '进口计划信息'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "./plan/PlanList.vue"))
  },
  {
    path: '/tobacco/importedCigarettes/contract',
    name: 'ContractList',
    meta: {
      title: '进口合同信息'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "./contract/ContractList.vue"))
  },
]
