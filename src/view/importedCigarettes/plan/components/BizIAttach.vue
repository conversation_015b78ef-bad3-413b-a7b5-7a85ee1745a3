

<template>
  <a-table
    style="width:800px"
    size="small"
    :columns="columns"
    :data-source="dataSource"
    bordered
    :pagination="false"
    row-key="sid"
  >
    <template #bodyCell="{ column, record }">
      <!-- 文件列的自定义渲染 -->
      <template v-if="column.key === 'action'">
        <cs-upload :head-id="props.headId" :b-type="record.businessType"  :is-show="props.operationStatus !== editStatus.SHOW" ></cs-upload>
      </template>
    </template>
  </a-table>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { message } from 'ant-design-vue';
import CsUpload from "@/components/upload/CsUpload.vue";
import {docType, editStatus} from "@/view/common/constant";
defineOptions({
  name:'BizIAttach'
})
const props = defineProps({
  headId: {
    type: String,
    default: ''
  },
  operationStatus:{
    type:String,
    default:''
  }
});

/* 数据列 */
const dataSource = reactive([
  {
    id: 1,
    type: '签约环节文件',
    businessType: docType.signingAttachType,
    files: {
      contract: []
    }
  },
]);

/* 列表显示列信息 */
const columns = [
  {
    title: '文件类型',
    dataIndex: 'type',
    align: 'center',
    width: 200
  },
  {
    title: '文件',
    key: 'action',
    align: 'center',
    width: 600,
    flex:1
  }
];



</script>

<style lang="less" scoped>

</style>
