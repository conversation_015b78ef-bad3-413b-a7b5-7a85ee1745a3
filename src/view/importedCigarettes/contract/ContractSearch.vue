<template>
  <a-form  layout="inline"  label-align="right"  :label-col="{ style: { width: '100px' } }" :model="searchParam"   class="cs-form  grid-container" >

    <!-- 数据状态 -->
    <a-form-item name="dataStatus"   :label="'单据状态'" class="grid-item"  :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="searchParam.dataStatus" id="dataStatus">
        <a-select-option   class="cs-select-dropdown"  v-for="item in productClassify.data_status"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
          {{item.value}} {{item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>

    <!-- 计划编号 -->
    <a-form-item name="planNo"   :label="'计划编号'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.planNo" />
    </a-form-item>

    <!-- 合同编号 -->
    <a-form-item name="contractNo"   :label="'合同号'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.contractNo" />
    </a-form-item>

    <!-- 卖方名称 -->
    <a-form-item name="seller"   :label="'供应商'" class="grid-item"  :colon="false">
<!--      <a-input  size="small" v-model:value="searchParam.seller" />-->
      <cs-select optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="searchParam.seller" id="seller">
        <a-select-option   class="cs-select-dropdown"  v-for="item in sellerOptions"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
          {{item.value}} {{item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>


    <!-- 签约日期 -->
    <a-form-item name="signDate" label="制单日期" class="grid-item" :colon="false">
      <a-form-item-rest>
        <a-row>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.prepareTimeFrom"
              id="signDateFrom"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
            />
          </a-col>
          <a-col :span="2" style="text-align: center">
            -
          </a-col>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.prepareTimeTo"
              size="small"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD"
              :locale="locale"
              style="width: 100%"
              placeholder=""
            />
          </a-col>
        </a-row>
      </a-form-item-rest>
    </a-form-item>



    <!-- 审批状态 -->
    <a-form-item name="approvalStatus"   :label="'审批状态'" class="grid-item"  :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="searchParam.approvalStatus" id="approvalStatus">
        <a-select-option   class="cs-select-dropdown"  v-for="item in productClassify.approval_status"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
          {{item.value}} {{item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>
  </a-form>
</template>

<script setup>
import {inject, onMounted, reactive} from 'vue'
import {productClassify} from "@/view/common/constant";
import CsSelect from "@/components/select/CsSelect.vue";
import ycCsApi from "@/api/ycCsApi";
import {message} from "ant-design-vue";

defineOptions({
  name: 'ContractSearch'
})
const searchParam = reactive({
  businessType: '',
  planNo: '',
  planYear: '',
  halfYear: '',
  buyer: '',
  seller: '',
  contractNo: '',
  contractEffectiveDate: '',
  contractExpiryDate: '',
  signDate: '',
  signDateFrom: '',
  signDateTo: '',
  loadingPort: '',
  arrivalPort: '',
  tradeTerms: '',
  priceTermPort: '',
  exportCountry: '',
  totalAmount: '',
  totalQuantity: '',
  shortOverPercent: '',
  preparedBy: '',
  prepareTime: '',
  prepareTimeFrom: '',
  prepareTimeTo: '',
  dataStatus: '',
  confirmTime: '',
  approvalStatus: '',
  versionNo: ''
})

// const locale = {
//   lang: {
//     locale: 'zh_CN',
//     placeholder: 'Select date',
//     rangePlaceholder: ['Start date', 'End date'],
//     today: 'Today',
//     now: 'Now',
//     backToToday: 'Back to today',
//     ok: 'Ok',
//     clear: 'Clear',
//     month: 'Month',
//     year: 'Year',
//     timeSelect: 'Select time',
//     dateSelect: 'Select date',
//     monthSelect: 'Choose a month',
//     yearSelect: 'Choose a year',
//     decadeSelect: 'Choose a decade',
//     yearFormat: 'YYYY',
//     dateFormat: 'M/D/YYYY',
//     dayFormat: 'D',
//     dateTimeFormat: 'M/D/YYYY HH:mm:ss',
//     monthFormat: 'MMMM',
//     monthBeforeYear: true,
//     previousMonth: 'Previous month (PageUp)',
//     nextMonth: 'Next month (PageDown)',
//     previousYear: 'Last year (Control + left)',
//     nextYear: 'Next year (Control + right)',
//     previousDecade: 'Last decade',
//     nextDecade: 'Next decade',
//     previousCentury: 'Last century',
//     nextCentury: 'Next century',
//   },
// }
/* 定义重置方法(注意前后顺序) */
const resetSearch = () => {
  Object.keys(searchParam).forEach(key => {
    searchParam[key] = '';
  });
}
const sellerOptions = reactive([])
const getSellerOptions = async () => {
  try {
    const params = {}
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.importedCigarettes.contract.sellerList}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        sellerOptions.push({
          value: item.key,
          label: item.value
        });
      });
    } else {
      message.error(res.message || '获取供应商数据失败');
    }
  } catch (error) {
    message.error('获取供应商数据失败');
  }
}

onMounted(() => {
  getSellerOptions()
})

defineExpose({
  searchParam,
  resetSearch
})
</script>

<style scoped>

</style>
