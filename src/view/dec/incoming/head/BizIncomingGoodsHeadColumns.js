import {baseColumns} from "@/view/common/baseColumns";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {h, onMounted, reactive, ref} from "vue";
import {productClassify} from "@/view/common/constant";
import {Tag} from "ant-design-vue";
import ycCsApi from "@/api/ycCsApi";
const {baseColumnsExport, baseColumnsShow} = baseColumns()
const { cmbShowRender } = useColumnsRender()


export function getColumns() {

  const commColumns = reactive([
    'id',
    'businessType',
    'dataState',
    'versionNo',
    'tradeCode',
    'sysOrgCode',
    'parentId',
    'createBy',
    'createTime',
    'updateBy',
    'updateTime',
    'insertUserName',
    'updateUserName',
    'extend1',
    'extend2',
    'extend3',
    'extend4',
    'extend5',
    'extend6',
    'extend7',
    'extend8',
    'extend9',
    'extend10',
    'contractNo',
    'purchaseNo',
    'customer',
    'supplier',
    'invoiceNo',
    'portOfDeparture',
    'destination',
    'paymentMethod',
    'priceTerm',
    'priceTermPort',
    'vesselVoyage',
    'sailingDate',
    'expectedArrivalDate',
    'salesDate',
    'contractAmount',
    'insuranceRate',
    'insuranceMarkup',
    'documentCreator',
    'documentDate',
    'documentStatus',
    'confirmTime',
    'approvalStatus',
    'dateOfContract',
    'customsDeclarationNo',
    'declarationDate',
    'purchaseContractNo',
    'entryNo',
    'entryDate',
    'serialNo',
    'entryDate',
    'salesDocumentStatus',
    'note',
    'licenseNumber',
    'permitNumber',
    'sellInvoiceNo',
    'sellStatus'

  ])

  /* 导出字段设置 */
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])

  /* table表格字段设置 */
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  /* table表格字段属性设置 */
  const totalColumns = ref([
    {
      width: 150,
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      align: 'center',
      fixed: 'left',
      resizable: true
    },
    // {
    //   title: '主键ID',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'id',
    //   key: 'id',
    // },
    // {
    //   title: '业务类型',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'businessType',
    //   key: 'businessType',
    // },
    // {
    //   title: '数据状态',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'dataState',
    //   key: 'dataState',
    // },
    // {
    //   title: '版本号',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'versionNo',
    //   key: 'versionNo',
    // },
    // {
    //   title: '企业10位编码',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'tradeCode',
    //   key: 'tradeCode',
    // },
    // {
    //   title: '组织机构代码',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'sysOrgCode',
    //   key: 'sysOrgCode',
    // },
    // {
    //   title: '父级ID',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'parentId',
    //   key: 'parentId',
    // },
    // {
    //   title: '创建人',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'createBy',
    //   key: 'createBy',
    // },
    // {
    //   title: '创建时间',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'createTime',
    //   key: 'createTime',
    // },
    // {
    //   title: '更新人',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'updateBy',
    //   key: 'updateBy',
    // },
    // {
    //   title: '更新时间',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'updateTime',
    //   key: 'updateTime',
    // },
    // {
    //   title: '插入用户名',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'insertUserName',
    //   key: 'insertUserName',
    // },
    // {
    //   title: '更新用户名',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'updateUserName',
    //   key: 'updateUserName',
    // },
    // {
    //   title: '扩展字段1',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'extend1',
    //   key: 'extend1',
    // },
    // {
    //   title: '扩展字段2',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'extend2',
    //   key: 'extend2',
    // },
    // {
    //   title: '扩展字段3',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'extend3',
    //   key: 'extend3',
    // },
    // {
    //   title: '扩展字段4',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'extend4',
    //   key: 'extend4',
    // },
    // {
    //   title: '扩展字段5',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'extend5',
    //   key: 'extend5',
    // },
    // {
    //   title: '扩展字段6',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'extend6',
    //   key: 'extend6',
    // },
    // {
    //   title: '扩展字段7',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'extend7',
    //   key: 'extend7',
    // },
    // {
    //   title: '扩展字段8',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'extend8',
    //   key: 'extend8',
    // },
    // {
    //   title: '扩展字段9',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'extend9',
    //   key: 'extend9',
    // },
    // {
    //   title: '扩展字段10',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'extend10',
    //   key: 'extend10',
    // },
    // {
    //   title: '合同号',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'contractNo',
    //   key: 'contractNo',
    // },
    {
      title: '进货单号',
      minWidth: 200,
      align: 'center',
      dataIndex: 'purchaseNo',
      key: 'purchaseNo',
      resizable: true
    },
    {
      title: '供应商',
      minWidth: 200,
      align: 'center',
      dataIndex: 'supplier',
      resizable: true,
      flex:1,
    },
    {
      title: '进口发票号',
      minWidth: 200,
      align: 'center',
      dataIndex: 'invoiceNo',
      key: 'invoiceNo',
      resizable: true
    },
    {
      title: '许可证号',
      minWidth: 200,
      align: 'center',
      dataIndex: 'licenseNumber',
      key: 'licenseNumber',
      resizable: true
    },
    {
      title: '准运证编号',
      minWidth: 200,
      align: 'center',
      dataIndex: 'permitNumber',
      key: 'permitNumber',
      resizable: true
    },
    {
      title: '销售发票号',
      minWidth: 200,
      align: 'center',
      dataIndex: 'sellInvoiceNo',
      key: 'sellInvoiceNo',
      resizable: true
    },
    {
      title: '进货单据状态',
      minWidth: 120,
      align: 'center',
      dataIndex: 'dataState',
      key: 'dataState',
      resizable: true,
      customRender: ({ text }) => {
        const colors = ['green', 'blue', 'red','black'];
        return h(Tag,{
          color: colors[parseInt(text)],
          size: 'small',
        }, cmbShowRender(text,productClassify.orderIncomingStatus))
      },
    },
    {
      title: '销售单据状态',
      minWidth: 120,
      align: 'center',
      dataIndex: 'sellStatus',
      key: 'sellStatus',
      resizable: true,
      customRender: ({ text }) => {
        const colors = ['green', 'blue', 'red','black'];
        return h(Tag,{
          color: colors[parseInt(text)],
          size: 'small',
        }, cmbShowRender(text,productClassify.orderIncomingStatus))
      },
    },
    {
      title: '制单人',
      minWidth: 150,
      align: 'center',
      dataIndex: 'createBy',
      key: 'createBy',
      resizable: true
    },
    {
      title: '制单时间',
      minWidth: 200,
      align: 'center',
      dataIndex: 'createTime',
      key: 'createTime',
      resizable: true
    },

    // }
    // {
    //   title: '金额',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'contractAmount',
    //   key: 'contractAmount',
    // },
    // {
    //   title: '报关单号',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'customsDeclarationNo',
    //   key: 'customsDeclarationNo',
    // },
    // {
    //   title: '报关日期',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'declarationDate',
    //   key: 'declarationDate',
    // },

    // {
    //   title: '目的地/港',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'destination',
    //   key: 'destination',
    // },
    // {
    //   title: '制单人',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'documentCreator',
    //   key: 'documentCreator',
    // },
    // {
    //   title: '制单日期',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'documentDate',
    //   key: 'documentDate',
    // },
    // {
    //   title: '单据状态',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'dataState',
    //   key: 'dataState',
    // },
    // {
    //   title: '确认时间',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'confirmTime',
    //   key: 'confirmTime',
    // },
    // {
    //   title: '审批状态',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'approvalStatus',
    //   key: 'approvalStatus',
    // },
    // {
    //   title: '销售状态',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'salesDocumentStatus',
    //   key: 'salesDocumentStatus',
    //   customRender: ({ text }) => {
    //     const colors = ['green', 'blue', 'red','black'];
    //     return h(Tag,{
    //       color: colors[parseInt(text)],
    //       size: 'small',
    //     }, cmbShowRender(text,productClassify.orderIncomingStatus))
    //   },
    //   resizable: true
    // },

    // {
    //   title: '发票号',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'invoiceNo',
    //   key: 'invoiceNo',
    // },
    // {
    //   title: '启运港',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'portOfDeparture',
    //   key: 'portOfDeparture',
    // },
    //
    // {
    //   title: '付款方式',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'paymentMethod',
    //   key: 'paymentMethod',
    // },
    // {
    //   title: '价格条款',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'priceTerm',
    //   key: 'priceTerm',
    // },
    // {
    //   title: '价格条款对应港口',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'priceTermPort',
    //   key: 'priceTermPort',
    // },
    // {
    //   title: '船名航次',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'vesselVoyage',
    //   key: 'vesselVoyage',
    // },
    // {
    //   title: '开航日期',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'sailingDate',
    //   key: 'sailingDate',
    // },
    // {
    //   title: '预计到达日期',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'expectedArrivalDate',
    //   key: 'expectedArrivalDate',
    // },
    // {
    //   title: '做销日期',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'salesDate',
    //   key: 'salesDate',
    // },
    // {
    //   title: '合同金额',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'contractAmount',
    //   key: 'contractAmount',
    // },
    // {
    //   title: '保险费率%',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'insuranceRate',
    //   key: 'insuranceRate',
    // },
    // {
    //   title: '投保加成%',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'insuranceMarkup',
    //   key: 'insuranceMarkup',
    // },
    //
    // {
    //   title: '签约日期',
    //   width: 200,
    //   align: 'center',
    //   dataIndex: 'dateOfContract',
    //   key: 'dateOfContract'
    // }

  ])



  return{
    columnsConfig,
    excelColumnsConfig,
    totalColumns
  }
}
