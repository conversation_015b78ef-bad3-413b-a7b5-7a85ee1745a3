<template>
  <section class="cs-card-form">
    <div>
      <s-table
        style="width: 100%;height: 100%;min-height: 100%;overflow-y: auto;overflow-x:auto"
        ref="tableRef"
        size="small"
        class="cs-action-item-modal-table remove-table-border-add-bg"
        :height="500"
        bordered
        :pagination="false"
        :columns="showColumns"
        :data-source="dataSourceList"
        :loading="tableLoading"
        row-key="invoiceNo"
        column-drag
        :row-height="30"
        :range-selection="false"
      >
        <!-- 空数据 -->
        <template #emptyText v-if="!tableLoading">
          <a-empty description="暂无数据" />
        </template>
      </s-table>
    </div>


  </section>
</template>

<script setup>


/* 进口合同列表 */
import {onMounted, reactive, ref} from "vue";
import {getPurchaseInvoiceNoSumData} from "@/api/cs_api_constant";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import ycCsApi from "@/api/ycCsApi";
import {message} from "ant-design-vue";
const { formatNumber,formatSpecifiedNumber } = useColumnsRender();



/* 表头HeadId*/
const props = defineProps({
  headId:{
    type:String,
    default:null
  }
})

defineOptions({
  name: 'IncomingInvoiceTotalDialog'
})

const emit = defineEmits(['cancel'])





/* 数据源 */
const dataSourceList = ref([])

const tableLoading = ref(false)



/* 引入表单数据 */
const gridData = reactive({
  selectedRowKeys: [],
  selectedData:[],
  loading: false,
});



/* 获取发票号汇总数据 */
const getInvoiceTotalData =  async () => {
  tableLoading.value = true
  try {
    let params = {
      headId : props.headId
    }
    const res = await window.majesty.httpUtil.postAction(ycCsApi.bizInComingList.getListSumByInvoice,params)
    if(res.code !== 200){
      message.error(res.message)
    }
    if (res.data && res.data.length > 0) {
      dataSourceList.value = res.data
    }
  }catch (e) {
    console.log('获取发票号汇总数据失败',e)
  }finally {
    tableLoading.value = false
  }



  // getPurchaseInvoiceNoSumData({headId: props.headId}).then(res => {
  //     if (res.code === 200) {
  //       dataSourceList.value = res.data
  //     }else {
  //       message.error(res.message)
  //     }
  //   }
  // ).finally(() => {
  //   tableLoading.value = false
  // })
}





// 表格显示列信息 选择框+合同编号+供应商
const showColumns = [
  {
    title: '进口发票号',
    minWidth: 200,
    align: 'center',
    dataIndex: 'invoiceNo',
    key: 'invoiceNo',
    resizable: true
  },
  {
    title: '进口数量',
    minWidth: 120,
    align: 'center',
    dataIndex: 'inQuantity',
    key: 'inQuantity',
    resizable: true,
    customRender: ({ text }) => {
      return formatSpecifiedNumber(text,true,2)
    }
  },
  {
    title: '金额',
    minWidth: 120,
    align: 'center',
    dataIndex: 'amount',
    key: 'amount',
    resizable: true,
    customRender: ({ text }) => {
      return formatSpecifiedNumber(text,true,2)
    }
  }
]


onMounted(() => {
  getInvoiceTotalData()
})

defineExpose({
  gridData
})

</script>

<style lang="less" scoped>

.header-search{
  margin: 10px 0;
}

/* 弹框表格样式 */
.cs-action-item-modal-table {
  padding: 4px 0;
  margin: 2px 0;
  box-sizing: border-box;
  min-height: calc(100vh);
  height: auto;
  .surely-table-body{
    min-height: calc(100vh);
  }
}
.cs-action-item-modal-table-empty{
  padding: 4px 0;
  margin: 2px 0;
  box-sizing: border-box;
  min-height: 500px;
  line-height: 500px;
}


</style>
