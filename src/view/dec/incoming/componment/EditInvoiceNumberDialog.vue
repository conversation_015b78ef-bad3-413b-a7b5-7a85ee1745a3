<template>
  <a-modal
    v-model:open="props.open"
    title="维护进口发票号"
    :maskClosable="true"
    :width="800"
    :destroyOnClose="true"
    :footer="null"
    @cancel="handlerOnBack"
  >
    <div v-if="formLoading" class="cs-form" style="height:38vh;display: flex;align-items: center;justify-content: center;">
      <a-spin tip="数据加载中..."/>
    </div>
    <div class="cs-form" v-else>
      <a-form
        ref="formRef"
        labelAlign="right"
        :label-col="{ style: { width: '100px' } }"
        :rules="rules"
        :model="formData"
        class="grid-container"
        style="padding: 0px;"
        >

        <!-- 进口发票号码 -->
        <a-form-item name="invoiceNumber" :label="'进口发票号码'" class="grid-item" :colon="false">
          <a-input
            :disabled="showDisable"
            size="small"
            v-model:value="formData.invoiceNumber"
            :maxLength="60"
            placeholder="请输入进口发票号码" />
        </a-form-item>

        <div class="cs-submit-btn " style="display: flex;justify-content: flex-end;">
            <a-button
            style="margin-right: 8px;"
            size="small"
            class="cs-margin-right cs-warning"
            @click="handlerOnBack">返回
          </a-button>



          <a-button
            size="small"
            type="primary"
            @click="handlerSave"
            :loading="buttonLoadingMap.save"
            v-show="!showDisable">保存
          </a-button>

        </div>
      </a-form>
    </div>
  </a-modal>
</template>

<script setup>
import { editStatus } from '@/view/common/constant'
import { message } from "ant-design-vue"
import { onMounted, reactive, ref, watch } from "vue"
import { useButtonLoading } from "@/view/utils/useBtnLoading"
import ycCsApi from "@/api/ycCsApi";

const { setLoading, buttonLoadingMap } = useButtonLoading()

const props = defineProps({
  /* 选中的ID集合 */
  selectedIds: {
    type: Array,
    default: () => []
  },
  /* 编辑配置 */
  editConfig: {
    type: Object,
    default: () => ({
      editStatus: editStatus.SHOW,
      editData: {}
    })
  },
  /* 对话框显示状态 */
  open: {
    type: Boolean,
    default: false
  }
})

const handlerOnBack = () => {
  emit('update:open', false)
  emit('onEditBack')
  resetForm()
}

// 是否禁用
const showDisable = ref(false)

// 表单数据
const formData = reactive({
  invoiceNumber: ''
})

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  formData.invoiceNumber = ''
}

// 校验规则
const rules = {
  invoiceNumber: [
    { required: true, message: '请输入进口发票号码', trigger: 'blur' },
    { max: 60, message: '进口发票号码长度不能超过60位字节', trigger: 'blur' }
  ]
}

// 定义子组件事件
const emit = defineEmits(['update:open', 'success','onEditBack'])

// vue3中使用ref,需要先定义，然后在模板中使用
const formRef = ref(null)

// 保存
const handlerSave = async () => {
  try {
    await formRef.value.validate()
    setLoading("save", true)

    // TODO: 调用批量更新接口
    const res = await window.majesty.httpUtil.postAction(
      ycCsApi.bizInComingList.batchUpdateInvoiceNo,
      {
        ids: props.selectedIds,
        invoiceNo: formData.invoiceNumber
      }
    )
    if (res.code !== 200) {
      message.error(res.message)
      return
    }

    console.log('selectedIds:', props.selectedIds)
    console.log('invoiceNumber:', formData.invoiceNumber)

    message.success("保存成功！")
    emit('success')
    handlerOnBack()
  } catch (error) {
    console.error("保存失败:", error)
  } finally {
    setLoading("save", false)
  }
}

const formLoading = ref(false)

// 监听对话框打开状态
watch(() => props.open, (newVal) => {
  if (!newVal) {
    resetForm()
  }
})

// 初始化操作
onMounted(() => {
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    showDisable.value = true
  }
})
</script>

<style lang="less" scoped>
.grid-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
  padding: 24px;
}
.grid-item {
  margin-bottom: 0;
}
.cs-submit-btn {
  margin-top: 24px;
  text-align: center;
}
.cs-margin-right {
  margin-right: 18px;
}

 .ant-btn.ant-btn-sm {
    margin-right: 0px;
    font-size: 14px;
    height: 24px;
    padding: 0px 7px;
    border-radius: 4px;
}

</style>
