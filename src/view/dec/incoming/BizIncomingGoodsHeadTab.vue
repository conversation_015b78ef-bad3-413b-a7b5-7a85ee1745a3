<template>
  <section class="cs-action cs-action-tab">
    <div class="cs-tab">
      <a-tabs class="sticky-header"  v-model:activeKey="tabName" size="small" :tabBarStyle="tabBarStyle" >
        <a-tab-pane key="headTab" tab="进货信息" >
          <biz-incoming-goods-head-edit ref="headTab" :edit-config="editConfig" @onEditBack="editBack" :operation-status="editConfig.editStatus"></biz-incoming-goods-head-edit>
        </a-tab-pane>
        <a-tab-pane v-if="showBody" key="documentTab" tab="证件信息" >
          <biz-incoming-goods-document-edit ref="documentTab" :head-id="headId" :edit-config="editConfig" @onEditBack="editBack" :operation-status="editConfig.editStatus" :is-all-confirmed="isAllConfirmed"></biz-incoming-goods-document-edit>
        </a-tab-pane>
        <a-tab-pane v-if="showBodyReceiptSell"  key="sellHeadEdit" tab="销售信息" @onEditBack="editBack" >
          <!-- 确保子组件重新挂载 -->
          <!-- <bi-shipfrom-list ref="shipFrom" :head-id="headId" :operation-status="editConfig.editStatus"></bi-shipfrom-list> -->
          <biz-i-sell-head-edit ref="sellHeadEdit" :head-id="headId"  :edit-config="editConfig" :operation-status="editConfig.editStatus" @onEditBack="editBack"></biz-i-sell-head-edit>
        </a-tab-pane>
        <a-tab-pane key="insureTab" tab="投保信息"  v-if="false">
          <biz-incoming-goods-insure-edit ref="insureTab" :head-id="headId" :edit-config="editConfig" @onEditBack="editBack" :operation-status="editConfig.editStatus"></biz-incoming-goods-insure-edit>
        </a-tab-pane>
        <a-tab-pane v-if="showBody" key="attachTab" tab="归档附件" >
          <in-coming-attach :head-id="headId"  :operation-status="editConfig.editStatus" :edit-config="editConfig" :is-all-confirmed="false"></in-coming-attach>
        </a-tab-pane>
        <a-tab-pane v-if="showBody" key="auditTab" tab="审批记录" >
          <in-coming-audit></in-coming-audit>
        </a-tab-pane>

        <template #rightExtra>
          <div class="cs-tab-icon" @click="editBack">
            <GlobalIcon type="close-circle" style="color:#000"/>
          </div>
        </template>
      </a-tabs>
    </div>

  </section>
</template>

<script setup>

import {onMounted, reactive, ref, watch} from "vue";
import {editStatus} from "@/view/common/constant";
import BizIncomingGoodsHeadEdit from "@/view/dec/incoming/head/BizIncomingGoodsHeadEdit.vue";
import BizIncomingGoodsDocumentEdit from "@/view/dec/incoming/document/BizIncomingGoodsDocumentEdit.vue";
import InComingAttach from "@/view/dec/incoming/attach/InComingAttach.vue";
import BizIAttach from "@/view/dec/imported_cigarettes/imported_attach/BizIAttach.vue";
import InComingAudit from "@/view/dec/incoming/audit/InComingAudit.vue";
import BizIncomingGoodsInsureEdit from "@/view/dec/incoming/insure/BizIncomingGoodsInsureEdit.vue";
import BizISellHeadEdit from "./sell/head/BizISellHeadEdit.vue";
defineOptions({
  name:'BizIncomingGoodsHeadTab'
})



const emit = defineEmits(['onEditBack'])

/* 定义editConfig 用于向子组件传递 */
const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});

import useEventBus from "@/view/common/eventBus";
const {onEvent} = useEventBus()


/* 自定义样式 */
const tabBarStyle = {
  background:'#fff',
  position:'sticky',
  top:'0',
  zIndex:'100',
}

/* 激活Tab key */
const tabName = ref('headTab');

/* 总tab信息 */
const tabs = reactive({
  headTab:true,
  shipFrom:true,
})

/* 表头headId */
const headId = ref('')


/* 是否显示子模块 tab */
const showBody = ref(false)
/* 是否显示进货信息 */
const showBodyPurchaseHead = ref(false)
//是否显示销售回单tab
const showBodyReceiptSell = ref(false)

//是否显示入库回单tab
const showBodyWarehouseReceiptHead = ref(false)

/* 返回tab界面 */
const editBack = (val) => {
  // console.log('val', val)
  if (val.editStatus === editStatus.EDIT){
    showBody.value = val.showBody
    showBodyPurchaseHead.value = val.showBodyPurchaseHead
    showBodyReceiptSell.value = val.showBodyReceiptSell
    if(val.editData != null){
      headId.value =  val.editData.id
      props.editConfig.editStatus = val.editStatus
      props.editConfig.editData = val.editData
    }
  }else if (val.editStatus === editStatus.ADD){
    headId.value =  ''
    showBody.value = val.showBody
    showBodyPurchaseHead.value = val.showBodyPurchaseHead
    showBodyReceiptSell.value = val.showBodyReceiptSell
    props.editConfig.editStatus = val.editStatus
    props.editConfig.editData = val.editData
  } else {
    // 如果val === false 返回进行刷新界面
    emit('onEditBack', val)
  }
}


/* 初始化操作 */
onMounted(()=>{
  // console.log('props.editConfig', props.editConfig)
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    headId.value = ''
    if (props.editConfig.editData) {
      props.editConfig.editData = {}
    }
    showBody.value = false
    showBodyReceiptSell.value = false
  } else if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    showBody.value = true

    console.log('编辑：',props.editConfig.editData)
    headId.value = props.editConfig.editData.id
    if(props.editConfig.editData.sellStatus === '0'||props.editConfig.editData.sellStatus === '1'  || props.editConfig.editData.isNext === '1'){
      showBodyReceiptSell.value = true
    }else{
      showBodyReceiptSell.value = false
    }
  }else if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW ) {
    headId.value = props.editConfig.editData.id
    showBody.value = true
    if(props.editConfig.editData.sellStatus === '0'||props.editConfig.editData.sellStatus === '1'  || props.editConfig.editData.isNext === '1'){
      showBodyReceiptSell.value = true
    }else{
      showBodyReceiptSell.value = false
    }
  }

  // console.log("props.editConfig.editData.dataState",props.editConfig.editData.dataState)
  // console.log("props.editConfig.editData.sellStatus",props.editConfig.editData.sellStatus)
  isAllConfirmed.value =  (props.editConfig.editData.dataState !== '0' && (props.editConfig.editData.sellStatus !== null && props.editConfig.editData.sellStatus !== '0') )

  onEvent('update_incoming_all',()=>{
      isAllConfirmed.value = true
  })
})




const isAllConfirmed = ref(false)

/* 监控tabName变化 */
watch(tabName, (value) => {
  for (let t in tabs) {
    tabs[t] = false
  }
  tabs[value] = true



})

</script>

<style lang="less" scoped>

</style>
