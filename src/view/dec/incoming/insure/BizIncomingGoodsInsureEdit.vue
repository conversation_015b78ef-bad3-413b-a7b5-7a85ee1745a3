<template>
  <section>
    <a-card size="small" title="证件信息" class="cs-card-form">

      <div v-if="formLoading" class="cs-form" style="height:38vh;display: flex;align-items: center;justify-content: center;">
        <a-spin tip="数据加载中..."/>
      </div>
      <div class="cs-form" v-else>

        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData"   class=" grid-container">


          <!-- 编号 -->
          <a-form-item name="codeNo"   :label="'编号'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable"  size="small" v-model:value="formData.codeNo" />
          </a-form-item>

          <!-- 保险公司 -->
          <a-form-item name="insuranceCompany"   :label="'保险公司'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable"  size="small" v-model:value="formData.insuranceCompany" />
          </a-form-item>

          <!-- 被保险人 -->
          <a-form-item name="insuredPerson"   :label="'被保险人'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable"  size="small" v-model:value="formData.insuredPerson" />
          </a-form-item>

          <!-- 投保险别 -->
          <a-form-item name="insuranceType"   :label="'投保险别'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable"  size="small" v-model:value="formData.insuranceType" />
          </a-form-item>

          <!-- 币种 -->
          <a-form-item name="currency"   :label="'币种'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable"  size="small" v-model:value="formData.currency" />
          </a-form-item>

          <!-- 保险金额 -->
          <a-form-item name="insuranceAmount"   :label="'保险金额'" class="grid-item"  :colon="false">
            <a-input-number
              size="small"
              v-model:value="formData.insuranceAmount"
              style="width: 100%;height: 24px"
              :disabled="showDisable"
              :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
              :parser="value => inputParser(value)"
            />
          </a-form-item>

          <!-- 保险费率 -->
          <a-form-item name="insuranceRate"   :label="'保险费率'" class="grid-item"  :colon="false">
            <a-input-number
              size="small"
              v-model:value="formData.insuranceRate"
              style="width: 100%;height: 24px"
              :disabled="showDisable"
              :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
              :parser="value => inputParser(value)"
            />
          </a-form-item>

          <!-- 保费 -->
          <a-form-item name="premium"   :label="'保费'" class="grid-item"  :colon="false">
            <a-input-number
              size="small"
              v-model:value="formData.premium"
              style="width: 100%;height: 24px"
              :disabled="showDisable"
              :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
              :parser="value => inputParser(value)"
            />
          </a-form-item>

          <!-- 备注 -->
          <a-form-item name="remark"   :label="'备注'" class="grid-item merge-3"  :colon="false">
            <a-textarea :disabled="showDisable" size="small" v-model:value="formData.remark" :autoSize="{ minRows: 3, maxRows: 4 }"></a-textarea>
          </a-form-item>

          <div class="cs-submit-btn merge-3">
            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right"
                      :loading="buttonLoadingMap.save"
                      v-show="props.editConfig.editStatus !== 'SHOW' ">保存
            </a-button>
            <a-button size="small" class="cs-margin-right cs-warning" @click="handlerOnBack(false)">返回</a-button>
          </div>
        </a-form>
      </div>
    </a-card>


  </section>
</template>

<script setup>
import {editStatus} from '@/view/common/constant'
import {message} from "ant-design-vue";
import {onMounted, reactive, ref} from "vue";
import {usePCode} from "@/view/common/usePCode";
import ycCsApi from "@/api/ycCsApi";
import {useButtonLoading} from "@/view/utils/useBtnLoading";
import {isNullOrEmpty} from "@/view/utils/common";
import {useColumnsRender} from "@/view/common/useColumnsRender";
const { getPCode } = usePCode()
const { setLoading,buttonLoadingMap } = useButtonLoading()
const { inputFormatter,inputParser}  = useColumnsRender()
const props = defineProps({
  /* 表头HeadId*/
  headId: {
    type: String,
    default: () => ''
  },
  /* 编辑配置 */
  editConfig: {
    type: Object,
    default: () => ({
      editStatus: editStatus.SHOW,
      editData: {}
    })
  },
  /* 是否能编辑 */
  isEdit: {
    type: String,
    default: () => '0'
  },
  /* 是否查看 */
  operationStatus:{
    type: Boolean,
    default: () => false
  }
});

defineOptions({
  name:'BizIncomingGoodsInsureEdit'
})


const handlerOnBack = (val) => {
  emit('onEditBack', val);
};

// 是否禁用
const showDisable = ref(false)

// 表单数据
const formData = reactive({
  // 主键ID，系统自动生成
  id:'',
  // 业务类型
  businessType:'',
  // 数据状态
  dataState:'',
  // 版本号
  versionNo:'',
  // 交易代码
  tradeCode:'',
  // 系统机构代码
  sysOrgCode:'',
  // 父级ID
  parentId:props.headId,
  // 创建人
  createBy:'',
  // 创建时间
  createTime:'',
  // 更新人
  updateBy:'',
  // 更新时间
  updateTime:'',
  // 插入用户名
  insertUserName:'',
  // 更新用户名
  updateUserName:'',
  // 扩展字段1
  extend1:'',
  // 扩展字段2
  extend2:'',
  // 扩展字段3
  extend3:'',
  // 扩展字段4
  extend4:'',
  // 扩展字段5
  extend5:'',
  // 扩展字段6
  extend6:'',
  // 扩展字段7
  extend7:'',
  // 扩展字段8
  extend8:'',
  // 扩展字段9
  extend9:'',
  // 扩展字段10
  extend10:'',
  // 准运证编号
  permitNo:'',
  // 准运证申办日期
  permitApplyDate:'',
  // 报关单号
  customsDeclarationNo:'',
  // 申报日期
  declarationDate:'',
  // 放行日期
  releaseDate:'',
  // 编号
  codeNo:'',
  // 保险公司
  insuranceCompany:'',
  // 被保险人
  insuredPerson:'',
  // 投保险别
  insuranceType:'',
  // 币种
  currency:'',
  // 保险金额
  insuranceAmount:'',
  // 保险费率
  insuranceRate:'',
  // 保费
  premium:'',
  // 备注
  remark:'',
  // 表头head_id
  headId:props.headId
})
// 校验规则
const rules = {
  id:[
    {max: 40, message: '主键ID长度不能超过 40位字节', trigger: 'blur'}
  ],
  businessType:[
    {max: 60, message: '业务类型长度不能超过 60位字节', trigger: 'blur'}
  ],
  dataState:[
    {max: 10, message: '数据状态长度不能超过 10位字节', trigger: 'blur'}
  ],
  versionNo:[
    {max: 10, message: '版本号长度不能超过 10位字节', trigger: 'blur'}
  ],
  tradeCode:[
    {max: 10, message: '企业10位编码长度不能超过 10位字节', trigger: 'blur'}
  ],
  sysOrgCode:[
    {max: 10, message: '组织机构代码长度不能超过 10位字节', trigger: 'blur'}
  ],
  parentId:[
    {max: 40, message: '父级ID长度不能超过 40位字节', trigger: 'blur'}
  ],
  createBy:[
    {max: 50, message: '创建人长度不能超过 50位字节', trigger: 'blur'}
  ],
  createTime:[
  ],
  updateBy:[
    {max: 50, message: '更新人长度不能超过 50位字节', trigger: 'blur'}
  ],
  updateTime:[
  ],
  insertUserName:[
    {max: 50, message: '插入用户名长度不能超过 50位字节', trigger: 'blur'}
  ],
  updateUserName:[
    {max: 50, message: '更新用户名长度不能超过 50位字节', trigger: 'blur'}
  ],
  extend1:[
    {max: 200, message: '扩展字段1长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend2:[
    {max: 200, message: '扩展字段2长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend3:[
    {max: 200, message: '扩展字段3长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend4:[
    {max: 200, message: '扩展字段4长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend5:[
    {max: 200, message: '扩展字段5长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend6:[
    {max: 200, message: '扩展字段6长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend7:[
    {max: 200, message: '扩展字段7长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend8:[
    {max: 200, message: '扩展字段8长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend9:[
    {max: 200, message: '扩展字段9长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend10:[
    {max: 200, message: '扩展字段10长度不能超过 200位字节', trigger: 'blur'}
  ],
  permitNo:[
    {max: 60, message: '准运证编号长度不能超过 60位字节', trigger: 'blur'}
  ],
  permitApplyDate:[
  ],
  customsDeclarationNo:[
    {max: 18, message: '报关单号长度不能超过 18位字节', trigger: 'blur'}
  ],
  declarationDate:[
  ],
  releaseDate:[
  ],
  codeNo:[
    {max: 60, message: '编号长度不能超过 60位字节', trigger: 'blur'}
  ],
  insuranceCompany:[
    {max: 200, message: '保险公司长度不能超过 200位字节', trigger: 'blur'}
  ],
  insuredPerson:[
    {max: 200, message: '被保险人长度不能超过 200位字节', trigger: 'blur'}
  ],
  insuranceType:[
    {max: 200, message: '投保险别长度不能超过 200位字节', trigger: 'blur'}
  ],
  currency:[
    {required: true, message: '币种不能为空', trigger: 'blur'},
    {max: 10, message: '币种长度不能超过 10位字节', trigger: 'blur'}
  ],
  insuranceAmount:[
    {required: true, message: '保险金额不能为空', trigger: 'blur'},
    { type: 'number', message: '保险金额不是有效的数字!'},
  ],
  insuranceRate:[
    {required: true, message: '保险费率不能为空', trigger: 'blur'},
    { type: 'number', message: '保险费率不是有效的数字!'},
  ],
  premium:[
    { type: 'number', message: '保费不是有效的数字!'},
  ],
  remark:[
    {max: 200, message: '备注长度不能超过 200位字节', trigger: 'blur'}
  ],
  headId:[
    {max: 40, message: '表头head_id长度不能超过 40位字节', trigger: 'blur'}
  ]
}

const pCode = ref('')

// 定义子组件事件
const emit = defineEmits(['onEditBack']);







// vue3中使用ref,需要先定义，然后在模板中使用。因为在模板渲染之前，DOM 元素还不存在。
const formRef = ref(null);

// 保存
const handlerSave = async () => {
  try {
    // 走下面的抛出异常
    await formRef.value.validate();

    setLoading("save", true);
    const res =  await window.majesty.httpUtil.postAction(
      ycCsApi.bizIncomingGoodsInsurance.insertOrUpdate,
      formData
    );
    if (res.code === 200) {
      message.success("保存成功！");
    } else {
      console.log('res',res)
      message.error(res.message);
      throw new Error(res.message);
    }
  } catch (error) {
    console.error("保存失败:", error);
    // message.error(error.message || "保存失败，请稍后再试");
    // throw new Error(error.message || "保存失败，请稍后再试")
  } finally {
    setLoading("save", false);
  }
};


const formLoading = ref(false)

const getDocument = async () => {
  try {
    console.log('props.headId', props.headId)
    formLoading.value = true // 开始加载状态

    const url = `${ycCsApi.bizIncomingGoodsInsurance.getDocumentByHeadId}/${props.headId}`
    const res = await window.majesty.httpUtil.getAction(url) // 使用 await 等待结果

    if (res.code === 200) {
      Object.assign(formData, res.data) // 成功时赋值
    } else {
      message.error(res.message) // 业务错误提示
    }
  } catch (error) {
    message.error('请求失败，请稍后再试') // 网络错误或异常处理
    console.error('请求异常:', error)
  } finally {
    formLoading.value = false // 结束加载状态
  }
}





// 初始化操作
onMounted(() => {
  getPCode().then(res=>{
    console.log('res',res)
    pCode.value = res;
  })


  // 获取证件信息数据
  getDocument()


  // 初始化数据(初始化数据)
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    showDisable.value = true
  }
  formData.parentId=props.headId
  formData.headId=props.headId


});


</script>

<style lang="less" scoped>


</style>



