<template>
  <section>
    <a-card size="small" title="证件信息" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData"   class=" grid-container">
            <a-form-item name="licenseNumber"   :label="'许可证号'" class="grid-item"  :colon="false">
                <a-input :disabled="showDisable || props.isAllConfirmed"  size="small" v-model:value="formData.licenseNumber" />
            </a-form-item>

          <a-form-item name="applicationDate"   :label="'许可证申请日期'" class="grid-item"  :colon="false">
            <a-date-picker
              :disabled="showDisable || props.isAllConfirmed"
              v-model:value="formData.applicationDate"
              id="applicationDate"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>

          </a-form-item>
          <a-form-item name="effectiveDate"   :label="'许可证有效日期'" class="grid-item"  :colon="false">
            <a-date-picker
              :disabled="showDisable || props.isAllConfirmed"
              v-model:value="formData.effectiveDate"
              id="effectiveDate"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>          </a-form-item>
          <a-form-item name="permitNumber"   :label="'准运证编号'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable || props.isAllConfirmed"  size="small" v-model:value="formData.permitNumber" />
          </a-form-item>
          <a-form-item name="permitApplicationDate"   :label="'准运证申办日期'" class="grid-item"  :colon="false">
            <a-date-picker
              :disabled="showDisable || props.isAllConfirmed"
              v-model:value="formData.permitApplicationDate"
              id="permitApplicationDate"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""></a-date-picker>          </a-form-item>
          <a-form-item name="note"   :label="'备注'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable || props.isAllConfirmed"  size="small" v-model:value="formData.note" />
          </a-form-item>


          <div class="cs-submit-btn merge-3">
            <!-- v-show="props.editConfig.editStatus !== 'SHOW' " -->
            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right"  v-show="props.operationStatus !== editStatus.SHOW "
                      :disabled="props.isAllConfirmed"
                      >保存
            </a-button>
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
          </div>
        </a-form>
      </div>
    </a-card>



  </section>
</template>

<script setup>
import {editStatus, productClassify} from '@/view/common/constant'
import {message} from "ant-design-vue";
import {onMounted, reactive, ref} from "vue";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
import {insertBizIDocument,getBizIDocument} from "@/api/cs_api_constant";
const { getPCode } = usePCode()

defineOptions({
  name:'documentEdit'
})


const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  },
  headId:{
    type:String,
    default: () => ''
  },
  /* 表头传入状态 查看/编辑 */
  operationStatus: {
    type: String,
    default: ''
  },
  /* 判断是否已经全部确认 */
  isAllConfirmed: {
    type: Boolean,
    default: false
  }
});

// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack']);

const onBack = (val) => {
  emit('onEditBack', val);
};

// 是否禁用
const showDisable = ref(false)

// 表单数据
const formData = ref({
    // 主建SID
    sid:'',
    // 表头HEAD_ID
    parentId:'',
    // 许可证号
  licenseNumber:'',
    // 许可证申请日期
  applicationDate:'',
    // 许可证有效日期
  effectiveDate:'',
    // 准运证编号
  permitNumber:'',
    // 准运证申办日期
  permitApplicationDate:'',
    // 备注
  note:'',
})
// 校验规则
const rules = {
  licenseNumber:[
        {max: 60, message: '许可证号长度不能超过60位字节', trigger: 'blur'}
    ],
  permitNumber:[
    {max: 200, message: '准运证编号长度不能超过200位字节', trigger: 'blur'}
  ],
  note:[
    {max: 200, message: '备注长度不能超过200位字节', trigger: 'blur'}
  ],

}

const pCode = ref('')
// 初始化操作
onMounted(() => {
  getPCode().then(res=>{
    console.log('res',res)
    pCode.value = res;
  })
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    showDisable.value = true
  }
  formData.value.parentId=props.headId
  getBizIDocument(formData.value).then((res)=>{
    if (res.code === 200&&res.data!=null) {
      Object.assign(formData.value, res.data)

    }
  })
  // if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
  //   showDisable.value = false
  //   Object.assign(formData, {});
  // }
  // // 初始化数据
  // if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
  //   Object.assign(formData, props.editConfig.editData);
  //   showDisable.value = false
  // }
  // if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
  //   Object.assign(formData, props.editConfig.editData);
  //   showDisable.value = true
  // }
});



// vue3中使用ref,需要先定义，然后在模板中使用。因为在模板渲染之前，DOM 元素还不存在。
const formRef = ref(null);
// 保存
const handlerSave = () => {
  formRef.value
    .validate()
    .then(() => {
      // if (props.editConfig && props.editConfig.editStatus === 'ADD'){
      //   insertBizIPurchaseHead(formData).then((res)=>{
      //     if (res.code === 200){
      //       message.success('新增成功!')
      //       onBack(true)
      //     }
      //   })
      // }else if (props.editConfig && props.editConfig.editStatus === 'EDIT'){
      insertBizIDocument(formData.value).then((res)=>{
        if (res.code === 200){
          message.success('修改成功!')
          Object.assign(formData.value, res.data);
          // onBack({
          //   editData: props.editConfig,
          //   showBody: true,
          //   // showBodyPurchaseHead:true,
          //   // showBodyWarehouseReceiptHead:true,
          //   // showBodyReceiptSell:true,
          //   editStatus: editStatus.EDIT
          // })
        }
      })
      // }
    })
    .catch(error => {
      console.log('validate failed', error);
    })
};

</script>

<style lang="less" scoped>


</style>



