<template>
  <section class="cs-action cs-action-tab">
    <div class="cs-tab">
      <a-tabs class="sticky-header"  v-model:activeKey="tabName" size="small" :tabBarStyle="tabBarStyle" >
        <a-tab-pane key="headTab" tab="订单信息" >
          <biz-i-order-head-edit  ref="headTab"  :edit-config="editConfig"  @onEditBack="editBack"  :operation-status="editConfig.editStatus"></biz-i-order-head-edit>
        </a-tab-pane>
        <a-tab-pane v-if="showBodyPurchaseHead" key="purchaseHeadEdit" tab="进货信息" @onEditBack="editBack" >
          <biz-i-purchase-head-edit ref="purchaseHeadEdit" :head-id="headId"  :edit-config="editConfig" :operation-status="editConfig.editStatus" @onEditBack="editBack"></biz-i-purchase-head-edit>
        </a-tab-pane>

        <a-tab-pane v-if="showBody" key="documentEdit" tab="证件信息" @onEditBack="editBack" >

          <biz-i-document-edit ref="documentEdit" :head-id="headId" :operation-status="editConfig.editStatus"  @onEditBack="editBack" :edit-config="editConfig" :is-all-confirmed="isAllConfirmed"></biz-i-document-edit>

        </a-tab-pane>
        <a-tab-pane v-if="showBodyWarehouseReceiptHead" key="warehouseReceiptHeadEdit" tab="入库回单" @onEditBack="editBack" >
          <biz-i-warehouse-receipt-head ref="warehouseReceiptHeadEdit" :head-id="headId"  :edit-config="editConfig" :operation-status="editConfig.editStatus" @onEditBack="editBack" ></biz-i-warehouse-receipt-head>
        </a-tab-pane>

        <a-tab-pane v-if="showBodyReceiptSell" key="sellHeadEdit" tab="销售信息" @onEditBack="editBack" >
          <!-- 确保子组件重新挂载 -->
          <!-- <bi-shipfrom-list ref="shipFrom" :head-id="headId" :operation-status="editConfig.editStatus"></bi-shipfrom-list> -->
          <biz-i-sell-head-edit ref="sellHeadEdit" :head-id="headId"  :edit-config="editConfig" :operation-status="editConfig.editStatus" @onEditBack="editBack"></biz-i-sell-head-edit>
        </a-tab-pane>
        <a-tab-pane v-if="showBodyReceiptSell" key="receiptHeadEdit" tab="出库回单" @onEditBack="editBack" >
          <!-- 确保子组件重新挂载 -->
          <!-- <bi-shipfrom-list ref="shipFrom" :head-id="headId" :operation-status="editConfig.editStatus"></bi-shipfrom-list> -->
          <biz-i-receipt-head-edit ref="receiptHeadEdit" :head-id="headId"  :edit-config="editConfig" :operation-status="editConfig.editStatus" @onEditBack="editBack"></biz-i-receipt-head-edit>
        </a-tab-pane>
        <a-tab-pane v-if="showBody" key="attachTab" tab="归档附件" @onEditBack="editBack" >
          <biz-i-attach :head-id="headId"  :operation-status="editConfig.editStatus" :edit-config="editConfig" :is-all-confirmed="false"></biz-i-attach>
        </a-tab-pane>

        <!-- 审批记录 -->
        <a-tab-pane v-if="showBody" key="approvalTab" tab="审批记录" @onEditBack="editBack" >
          <biz-i-approval-list></biz-i-approval-list>
        </a-tab-pane>
        <template #rightExtra>
          <div class="cs-tab-icon" @click="editBack">
            <GlobalIcon type="close-circle" style="color:#000"/>
          </div>
        </template>
      </a-tabs>
    </div>

  </section>
</template>

<script setup>

import {onMounted, reactive, ref, watch} from "vue";
import {editStatus} from "@/view/common/constant";
import BizIOrderHeadEdit from "@/view/dec/imported_cigarettes/head/BizIOrderHeadEdit.vue";
import BizIPurchaseHeadEdit from "@/view/dec/imported_cigarettes/purchase/head/BizIPurchaseHeadEdit.vue";
import BizIDocumentEdit from "@/view/dec/imported_cigarettes/document/BizIDocumentEdit.vue";
import BizISellHeadEdit from "./sell/head/BizISellHeadEdit.vue";
import BizIReceiptHeadEdit from "./receipt/head/BizIReceiptHeadEdit.vue";
import BizIAttach from "@/view/dec/imported_cigarettes/imported_attach/BizIAttach.vue";
import BizIWarehouseReceiptHead from './warehouse-receipt/head/BizIWarehouseReceiptHeadEdit'
import BizIApprovalList from "@/view/dec/imported_cigarettes/approval/BizIApprovalList.vue";
import {checkOrderHeadIsNextModule} from "@/api/cs_api_constant";
import {message} from "ant-design-vue";
import {isNullOrEmpty} from "@/view/utils/common";
defineOptions({
  name:'BizIOrderHeadTab'
})

/* 判断是否状态是否全部已经 确认 */
const isAllConfirmed = ref(false)

const emit = defineEmits(['onEditBack'])

/* 定义editConfig 用于向子组件传递 */
const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});


/* 自定义样式 */
const tabBarStyle = {
  background:'#fff',
  position:'sticky',
  top:'0',
  zIndex:'100',
}

/* 激活Tab key */
const tabName = ref('headTab');

/* 总tab信息 */
const tabs = reactive({
  headTab:true,
  shipFrom:true,
})

/* 表头headId */
const headId = ref('')


/* 是否显示子模块 tab */
const showBody = ref(false)
/* 是否显示进货信息 */
const showBodyPurchaseHead = ref(false)
//是否显示销售回单tab
const showBodyReceiptSell = ref(false)

//是否显示入库回单tab
const showBodyWarehouseReceiptHead = ref(false)

/* 返回tab界面 */
const editBack = (val) => {
  // console.log('val', val)
  // console.log('val', val.editStatus)
  if (val.editStatus === editStatus.EDIT){
    showBody.value = val.showBody
    showBodyPurchaseHead.value = val.showBodyPurchaseHead
    if(val.editData != null){
      headId.value =  val.editData.sid
      props.editConfig.editStatus = val.editStatus
      props.editConfig.editData = val.editData
    }
    showBodyReceiptSell.value = val.showBodyReceiptSell
    showBodyWarehouseReceiptHead.value = val.showBodyWarehouseReceiptHead
  }else {
    if (val) {
      emit('onEditBack', val)
    }
  }
}


/* 初始化操作 */
onMounted(()=>{
  // console.log('props.editConfig', props.editConfig)
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    headId.value = ''
    showBody.value = false
    showBodyReceiptSell.value = false
    showBodyWarehouseReceiptHead.value=false
    showBodyPurchaseHead.value=false
  } else if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    headId.value = props.editConfig.editData.sid
    // +++++++++++++++++++ 废弃数据单独处理 ++++++++++++++++++++++++++++
    if (props.editConfig.editData.dataStatus === '2') {
      checkOrderHeadIsNextModule({sid:props.editConfig.editData.sid}).then((res) => {
        if (res.code === 200) {
          showBody.value = res.data.showBodyDocumentHeadCount > 0
          showBodyPurchaseHead.value = res.data.showBodyPurchaseHeadCount > 0;
          showBodyReceiptSell.value = res.data.showBodyReceiptSellCount > 0;
          showBodyWarehouseReceiptHead.value = res.data.showBodyWarehouseReceiptHeadCount > 0;
        }else {
          //
          message.error(res.message)
        }
      })
    }else {
      // 判断表头状态是否已经确认
      // *******.TAB2 进货信息表头
      // 原需求：订单信息数据点击保存成功保存时，将其表头、表体数据自动带入作为进货信息表头表体
      // 新改法：保存时先将数据流入进货信息，只有订单表头确认时，才将对应的tab界面显示出来
      if (props.editConfig.editData.isNext === '1') {
        headId.value = props.editConfig.editData.sid
        // showBody.value = true
        if (props.editConfig.editData.dataStatus === '1' || props.editConfig.editData.inboundReceiptStatus === '2') {
          showBodyPurchaseHead.value = true
        }
        if (props.editConfig.editData.inboundReceiptStatus === '1' || props.editConfig.editData.inboundReceiptStatus === '2') {
          showBodyReceiptSell.value = true
        }
        if (props.editConfig.editData.purchaseDataStatus === '1' || props.editConfig.editData.purchaseDataStatus === '2') {
          showBodyWarehouseReceiptHead.value = true
        }
      } else {
        headId.value = ''
        // showBody.value = false
        showBodyPurchaseHead.value = false
        showBodyReceiptSell.value = false
        showBodyWarehouseReceiptHead.value = false
      }
      console.log('headId.value', headId.value)
    }

    // 判断日期是否为空
    if (props.editConfig.editData.updateTime !== null) {
      showBody.value = true
    }else {
      showBody.value = false
    }
    console.log('showBody.value', showBody.value)
    console.log('props.editConfig.editData', props.editConfig.editData)

  }else if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW ) {
    headId.value = props.editConfig.editData.sid

    if (props.editConfig.editData.dataStatus === '2' ) {
      checkOrderHeadIsNextModule({sid:props.editConfig.editData.sid}).then((res) => {
        if (res.code === 200) {
          showBody.value = res.data.showBodyDocumentHeadCount > 0
          showBodyPurchaseHead.value = res.data.showBodyPurchaseHeadCount > 0;
          showBodyReceiptSell.value = res.data.showBodyReceiptSellCount > 0;
          showBodyWarehouseReceiptHead.value = res.data.showBodyWarehouseReceiptHeadCount > 0;
        }else {
          //
          message.error(res.message)
        }
      })
    }else {
      // 判断日期是否为空
      if (props.editConfig.editData.updateTime !== null) {
        showBody.value = true
      }
      if (props.editConfig.editData.dataStatus === '1'  ){
        showBodyPurchaseHead.value = true
      }
      if (props.editConfig.editData.inboundReceiptStatus === '1' ) {
        showBodyReceiptSell.value = true
      }
      if (props.editConfig.editData.purchaseDataStatus === '1'  ) {
        showBodyWarehouseReceiptHead.value = true
      }
    }
  }
})



/* 监控tabName变化 */
watch(tabName, (value) => {
  for (let t in tabs) {
    tabs[t] = false
  }
  tabs[value] = true

  // console.log('headId',headId.value)
  isAllConfirmed.value =  (props.editConfig.editData.dataStatus !== '0' &&  props.editConfig.editData.purchaseDataStatus !== '0' && props.editConfig.editData.salesDataStatus !== '0' && props.editConfig.editData.inboundReceiptStatus !== '0' &&  props.editConfig.editData.outboundReceiptStatus !== '0')
  // console.log('isAllConfirmed.value', props.editConfig.editData.dataStatus)
  // console.log('isAllConfirmed.value', props.editConfig.editData.purchaseDataStatus)
  // console.log('isAllConfirmed.value', props.editConfig.editData.salesDataStatus)
  // console.log('isAllConfirmed.value', props.editConfig.editData.inboundReceiptStatus)
  // console.log('isAllConfirmed.value', props.editConfig.editData.outboundReceiptStatus)
  // console.log('isAllConfirmed.value', isAllConfirmed.value)
})

</script>

<style lang="less" scoped>

</style>
