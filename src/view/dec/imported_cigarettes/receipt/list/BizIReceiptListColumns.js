import {baseColumns} from "@/view/common/baseColumns";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {h, reactive, ref} from "vue";
import {productClassify} from "@/view/common/constant";
import {Tag} from "ant-design-vue";
const {baseColumnsExport, baseColumnsShow} = baseColumns()
const { cmbShowRender } = useColumnsRender()


// 格式化数字为千分位分隔的工具函数
const formatNumber = (value) => {
  if (value === undefined || value === null || value === '') {
    return '';
  }

  // 转换为数字并检查有效性
  const number = Number(value);
  if (isNaN(number)) {
    return '';
  }

  // 配置 NumberFormat 选项
  return new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: 2,  // 至少两位小数
    maximumFractionDigits: 10
  }).format(number);
};

export function getColumns() {

  const commColumns = reactive([
    'sid',
    'tradeName',
    'shipmentQuantity',
    'actualQuantityIssued',
    'unit',
    'decPrice',
    'curr',
    'amount'
  ])

  /* 导出字段设置 */
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])

  /* table表格字段设置 */
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  const testAdd = (e) =>{
    console.log('新增了')
  }
  /* table表格字段属性设置 */
  const totalColumns = ref([
    {
      title: '商品名称',
      width: 200,
      align: 'center',
      dataIndex: 'tradeName',
      key: 'tradeName'
    },
    {
      title: '出货数量',
      width: 200,
      align: 'center',
      dataIndex: 'shipmentQuantity',
      key: 'shipmentQuantity',
      editable: 'cellEditorSlot',
      customRender: ({ text }) => {
        return formatNumber(text);
      }
    },
    {
      title: '实发数量',
      width: 200,
      align: 'center',
      dataIndex: 'actualQuantityIssued',
      key: 'actualQuantityIssued',
      editable: 'cellEditorSlot',
      customRender: ({ text }) => {
        return formatNumber(text);
      }
    },
    {
      title: '单位',
      width: 200,
      align: 'center',
      dataIndex: 'unit',
      key: 'unit',
    },
    {
      title: '单价',
      width: 200,
      align: 'center',
      dataIndex: 'decPrice',
      key: 'decPrice',
      customRender: ({ text }) => {
        return formatNumber(text);
      }
    },
    {
      title: '币种',
      width: 200,
      align: 'center',
      dataIndex: 'curr',
      key: 'curr',
    },
    {
      title: '金额',
      width: 200,
      align: 'center',
      dataIndex: 'amount',
      key: 'amount',
      customRender: ({ text }) => {
        return formatNumber(text);
      }
    }
  ])

  return{
    columnsConfig,
    excelColumnsConfig,
    totalColumns
  }
}
