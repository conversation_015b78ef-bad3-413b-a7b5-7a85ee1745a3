import {baseColumns} from "@/view/common/baseColumns";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {h, reactive, ref} from "vue";
import {productClassify} from "@/view/common/constant";
import {Tag} from "ant-design-vue";
import {useOrderColumnsCommon} from "@/view/dec/imported_cigarettes/head/useOrderColumnsCommon";
const {baseColumnsExport, baseColumnsShow} = baseColumns()
const { cmbShowRender } = useColumnsRender()

const { supplierList,getSupplierList } = useOrderColumnsCommon()
getSupplierList()

export function getColumns() {

  const commColumns = reactive([
    'sid',
    'insertUser',
    'insertTime',
    'insertUserName',
    'updateUser',
    'updateTime',
    'updateUserName',
    'tradeCode',
    'businessType',
    'orderDataStatus',
    'contractNo',
    'orderNo',
    'partyA',
    'partyB',
    'deliveryDate',
    'paymentMethod',
    'purchaseOrderNo',
    'importInvoiceNo',
    'licenseNo',
    'transportPermitNo',
    'salesInvoiceNo',
    'salesContractNo',
    'purchaseDataStatus',
    'salesDataStatus',
    'inboundReceiptStatus',
    'outboundReceiptStatus',
    'versionNo',
    'dataStatus',
    'extend1',
    'extend2',
    'extend3',
    'extend4',
    'extend5',
    'extend6',
    'extend7',
    'extend8',
    'extend9',
    'extend10',
    'dateOfSigning',
    'planNo',
    'orderConfirmationTime',
    'apprStatus'
  ])

  /* 导出字段设置 */
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])

  /* table表格字段设置 */
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  /* table表格字段属性设置 */
  const totalColumns = ref([
    {
      width: 150,
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      align: 'center',
      fixed: 'left',
    },
    // {
    //   title: '版本号',
    //   width: 150,
    //   align: 'center',
    //   dataIndex: 'versionNo',
    //   key: 'versionNo',
    //   resizable: true
    // },
    {
      title: '业务类型',
      minWidth: 200,
      align: 'center',
      dataIndex: 'businessType',
      key: 'businessType',
      customRender: ({ text }) => {
        return h(<span></span>,  cmbShowRender(text,productClassify.commonBusinessType))
      },
      resizable: true
    },
    {
      title: '订单单据状态',
      minWidth: 200,
      align: 'center',
      dataIndex: 'dataStatus',
      key: 'dataStatus',
      customRender: ({ text }) => {
        const colors = ['green', 'blue', 'red'];
        return h(Tag,{
          color: colors[parseInt(text)],
          size: 'small',
        }, cmbShowRender(text,productClassify.orderStatus))
      },
      resizable: true
    },
    {
      title: '合同号',
      minWidth: 200,
      align: 'center',
      dataIndex: 'contractNo',
      key: 'contractNo',
      resizable: true
    },
    {
      title: '订单号',
      minWidth: 200,
      align: 'center',
      dataIndex: 'orderNo',
      key: 'orderNo',
      resizable: true
    },
    {
      title: '客户',
      minWidth: 200,
      align: 'center',
      dataIndex: 'partyA',
      key: 'partyA',
      resizable: true
    },
    {
      title: '供应商',
      minWidth: 350,
      align: 'center',
      dataIndex: 'partyB',
      key: 'partyB',
      resizable: true,
      customRender: ({ text }) => {
        return h(<span></span>,  cmbShowRender(text,supplierList.value))
      }
    },
    {
      title: '交货日期',
      minWidth: 200,
      align: 'center',
      dataIndex: 'deliveryDate',
      key: 'deliveryDate',
      resizable: true
    },
    {
      title: '付款方式',
      minWidth: 200,
      align: 'center',
      dataIndex: 'paymentMethod',
      key: 'paymentMethod',
      resizable: true
    },
    {
      title: '制单人',
      minWidth: 200,
      align: 'center',
      dataIndex: 'insertUserName',
      key: 'insertUserName',
      resizable: true
    },
    {
      title: '订单制单时间',
      minWidth: 200,
      align: 'center',
      dataIndex: 'insertTime',
      key: 'insertTime',
      resizable: true
    },
    {
      title: '进货单号',
      minWidth: 200,
      align: 'center',
      dataIndex: 'purchaseOrderNo',
      key: 'purchaseOrderNo',
      resizable: true
    },
    {
      title: '进口发票号码',
      minWidth: 200,
      align: 'center',
      dataIndex: 'importInvoiceNo',
      key: 'importInvoiceNo',
      resizable: true
    },
    {
      title: '许可证号',
      minWidth: 200,
      align: 'center',
      dataIndex: 'licenseNo',
      key: 'licenseNo',
      resizable: true
    },
    {
      title: '准运证编号',
      minWidth: 200,
      align: 'center',
      dataIndex: 'transportPermitNo',
      key: 'transportPermitNo',
      resizable: true
    },
    {
      title: '销售发票号',
      minWidth: 200,
      align: 'center',
      dataIndex: 'salesInvoiceNo',
      key: 'salesInvoiceNo',
      resizable: true
    },
    {
      title: '销售合同号',
      minWidth: 200,
      align: 'center',
      dataIndex: 'salesContractNo',
      key: 'salesContractNo',
      resizable: true
    },
    {
      title: '进货单据状态',
      minWidth: 200,
      align: 'center',
      dataIndex: 'purchaseDataStatus',
      key: 'purchaseDataStatus',
      customRender: ({ text }) => {
        const colors = ['green', 'blue', 'red'];
        return h(Tag,{
          color: colors[parseInt(text)],
          size: 'small',
        }, cmbShowRender(text,productClassify.orderStatus))
      },
      resizable: true
    },
    {
      title: '入库回单状态',
      minWidth: 200,
      align: 'center',
      dataIndex: 'inboundReceiptStatus',
      key: 'inboundReceiptStatus',
      customRender: ({ text }) => {
        const colors = ['green', 'blue', 'red'];
        return h(Tag,{
          color: colors[parseInt(text)],
          size: 'small',
        }, cmbShowRender(text,productClassify.orderStatus))
      },
      resizable: true
    },
    {
      title: '销售单据状态',
      minWidth: 200,
      align: 'center',
      dataIndex: 'salesDataStatus',
      key: 'salesDataStatus',
      customRender: ({ text }) => {
        const colors = ['green', 'blue', 'red'];
        return h(Tag,{
          color: colors[parseInt(text)],
          size: 'small',
        }, cmbShowRender(text,productClassify.orderStatus))
      },
      resizable: true
    },
    {
      title: '出库回单状态',
      minWidth: 200,
      align: 'center',
      dataIndex: 'outboundReceiptStatus',
      key: 'outboundReceiptStatus',
      customRender: ({ text }) => {
        const colors = ['green', 'blue', 'red'];
        return h(Tag,{
          color: colors[parseInt(text)],
          size: 'small',
        }, cmbShowRender(text,productClassify.orderStatus))
      },
      resizable: true
    }
  ])

  return{
    columnsConfig,
    excelColumnsConfig,
    totalColumns
  }
}
