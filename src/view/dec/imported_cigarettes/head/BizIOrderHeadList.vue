<template>
  <section  class="dc-section">
    <div class="cs-action-container "  v-show="show">
      <!-- 查询列表区域 -->
      <div class="cs-search" ref="searchArea">
        <a-card :bordered="false">
          <bread-crumb>
            <div ref="area_head">
              <div class="search-btn">
                <a-button size="small" type="primary" class="cs-margin-right cs-refresh" @click="handlerRefresh" v-show="showSearch">
                  <template #icon>
                    <GlobalIcon type="redo" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" class="cs-margin-right" @click="handlerSearch">
                  查询
                  <template #icon>
                    <GlobalIcon type="search" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" danger class="cs-margin-right cs-warning" @click="handleShowSearch">
                  <template #icon>
                    <GlobalIcon v-show="!showSearch" type="down" style="color:#fff"/>
                    <GlobalIcon v-show="showSearch" type="up" style="color:#fff"/>
                  </template>
                </a-button>
              </div>
            </div>
          </bread-crumb>
          <div class="separateLine"></div>
          <!-- 查询按钮组件 -->
          <div ref="area_search">
            <div v-show="showSearch">
              <biz-i-order-head-search ref="headSearch" />
            </div>
          </div>
        </a-card>
      </div>
      <!-- 操作按钮区域 -->
      <div class="cs-action-btn">
          <div class="cs-action-btn-item" v-has="['yc-cs:importedCigarettes-order-head:add']">
            <a-button size="small" @click="handlerOpenAddModal" >
              <template #icon>
                <GlobalIcon type="plus" style="color:green"/>
              </template>
              {{localeContent('m.common.button.add')}}
            </a-button>
          </div>
          <div class="cs-action-btn-item" v-has="['yc-cs:importedCigarettes-order-head:edit']">
            <a-button  size="small"  @click="handlerEdit">
              <template #icon>
                <GlobalIcon type="form" style="color:orange"/>
              </template>
              {{localeContent('m.common.button.update')}}
            </a-button>
          </div>
          <div class="cs-action-btn-item" v-has="['yc-cs:importedCigarettes-order-head:delete']">
            <a-button  size="small" :loading="deleteLoading" @click="handlerDelete">
              <template #icon>
                <GlobalIcon type="delete" style="color:deeppink"/>
              </template>
              {{localeContent('m.common.button.delete')}}
            </a-button>
          </div>
          <div class="cs-action-btn-item" v-has="['yc-cs:importedCigarettes-order-head:export']">
            <a-button  size="small" :loading="exportLoading" @click="handlerExport">
              <template #icon>
                <GlobalIcon type="folder-open" style="color:orange"/>
              </template>
              {{localeContent('m.common.button.export')}}
            </a-button>
          </div>
          <div class="cs-action-btn-item"  v-has="['yc-cs:dec:import']">
            <a-button  size="small"  @click="handlerImport">
              <template #icon>
                <GlobalIcon type="file-excel" style="color:deepskyblue"/>
              </template>
              {{localeContent('m.common.button.import')}}
            </a-button>
          </div>

          <div class="cs-action-btn-item"  v-has="['yc-cs:importedCigarettes-order-head:copyVersion']">
            <a-button  size="small"  @click="handlerCopy">
              <template #icon>
                <GlobalIcon type="copyright-circle" style="color:deeppink"/>
              </template>
              版本复制
            </a-button>
          </div>


          <div class="cs-action-btn-item"  v-has="['yc-cs:importedCigarettes-order-head:sendAudit']">
            <a-button  size="small"  @click="sendOrderAudit" :loading="copyLoading">
              <template #icon>
                <GlobalIcon type="send" style="color:orangered"/>
              </template>
              发送审核
            </a-button>
          </div>


          <div class="cs-action-btn-item"  v-has="['yc-cs:importedCigarettes-order-head:cancelOrder']">
            <a-button  size="small"  @click="handlerCancel" :loading="handlerCancelLoading">
              <template #icon>
                <GlobalIcon type="frown" style="color:deeppink"/>
              </template>
              作废
            </a-button>
          </div>


        <div class="cs-action-btn-settings">
          <!-- 自定义显示组件 -->
          <CsTableColSettings
            :resId="tableKey"
            :tableKey="tableKey+'-suppler_code'"
            :initSettingColumns="originalColumns"
            :showColumnSettings="true"
            @customColumnChange="customColumnChange"
          >
          </CsTableColSettings>
        </div>
      </div>
      <!-- 表格区域 -->
      <div v-if="showColumns && showColumns.length > 0"  class="table-pagination-container" ref="containerRef">
        <s-table
          ref="tableRef"
          class="cs-action-table-item remove-table-border-add-bg"
          size="small"
          :height="tableScrollY"
          :scroll="{ y: tableScrollY, x: 400 }"
          bordered
          :pagination="false"
          :columns="showColumns.length > 0 ?showColumns:totalColumns"
          :data-source="dataSourceList"
          :row-selection="{  selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          row-key="sid"
          :custom-row="customRow"
          column-drag
          :row-height="30"
          :row-hover-delay="5"
          :header-height="30"
          :range-selection="false"
        >
          <!-- 空数据 -->
          <template #emptyText>
            <a-empty description="暂无数据" />
          </template>
          <!-- 操作 -->
          <template #bodyCell="{ column,record }">
            <template v-if="column.key === 'operation'">
              <div class="operation-container">
                <div >
                  <a-button
                    size="small"
                    type="link"
                    @click="handleEditByRow(record)"
                    :style="operationEdit('edit')"
                  >
                    <template #icon>
                      <GlobalIcon type="form" style="color:#e93f41"/>
                    </template>
                  </a-button>
                </div>

                <div >
                  <a-button
                    size="small"
                    type="link"
                    @click="handleViewByRow(record)"
                    :style="operationEdit('view')"
                  >
                    <template #icon>
                      <GlobalIcon type="search" style="color:#1677ff"/>
                    </template>
                  </a-button>
                </div>


              </div>
            </template>
          </template>
        </s-table>
      </div>

      <!-- 分页 -->
      <div class=cs-pagination  v-if="showColumns && showColumns.length > 0" ref="paginationRef">
        <!--
           【进口管理】主页面-去除“总数量、总金额”
           <div class="cs-margin-right cs-list-total-data ">
             总数量：{{sumTotal.qtyTotal}} ，总金额：{{sumTotal.decTotal}}
           </div>
        -->
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer  :page-size="page.pageSize" :total="page.total"  @change="onPageChange">
          <template #buildOptionText="props">
            <span >{{ props.value }}条/页</span>
          </template>
        </a-pagination>

      </div>
    </div>

    <!-- 新增 编辑数据 -->
    <div v-if="!show">
      <biz-i-order-head-tab :editConfig="editConfig" @onEditBack="handlerOnBack" />
    </div>


    <!-- 导入数据 -->
    <ImportIndex :importShow="importShow" :importConfig="importConfig"   @onImportSuccess="importSuccess"></ImportIndex>


    <!-- 新增弹框 -->
    <cs-modal :visible="showAddModal" :title="'新增'" :width="800" :footer="true" @cancel="handleCloseModal">
      <template #customContent>
         <biz-i-order-extract-head ref="extractRef"></biz-i-order-extract-head>
      </template>
      <template #footer>
        <div style="display: flex;justify-content: right;align-items: center">
          <a-button @click="handleCloseModal" size="small">返回</a-button>
          <a-button
            style="margin-left: 8px"
            size="small"
            type="primary"
            @click="extractContractData"
            :loading="confirmLoading"
          >保存</a-button>
        </div>
      </template>
    </cs-modal>


    <!-- 选择重启订单号弹框 -->
    <cs-modal :visible="showSelectModal" :title="'请选择启用订单号'" :width="800" :footer="false" @cancel="handleCloseSelectModal">
      <template #customContent>
          <biz-i-select-order-modal ref="selectOrderModalRef" :order-list="rebootOrderNoList" @cancel="handleCloseSelectModal" @save="handlerOrderNoSave"></biz-i-select-order-modal>
      </template>
    </cs-modal>


  </section>


</template>

<script setup>
/* 使用自定义 Hook 函数 */
import {useCommon} from '@/view/common/useCommon'
import {createVNode, nextTick, onMounted, provide, reactive, ref, watch} from "vue";
import {getColumns} from "@/view/dec/imported_cigarettes/head/BizIOrderHeadColumns";
import {message, Modal} from "ant-design-vue";
import BreadCrumb from "@/components/breadcrumb/BreadCrumb.vue";
import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
const { totalColumns } = getColumns()
import {ImportIndex} from 'yao-import'
import {localeContent} from "@/view/utils/commonUtil";
import { useImport } from "@/view/common/useImport";
import ycCsApi from "@/api/ycCsApi";
import CsTableColSettings from "@/components/settings/CsTableColSettings.vue";
import {useRoute} from "vue-router";
import {editStatus} from "@/view/common/constant";
import {
  checkNextModuleExistEffectiveData,
  checkOrderNoNotCancel,
  // confirmIOrderHead,
  copyOrderVersion,
  // copyVersion,
  deleteBizErpIOrderHead,
  generateIOrder,
  getOrderHeadTotal, orderCancelData, rebootOrderNo
} from "@/api/cs_api_constant";
import {deepClone, isNullOrEmpty} from "@/view/utils/common";
import BizIOrderHeadTab from "@/view/dec/imported_cigarettes/BizIOrderHeadTab.vue";
import BizIOrderHeadSearch from "@/view/dec/imported_cigarettes/head/BizIOrderHeadSearch.vue";
import CsModal from "@/components/modal/cs-modal.vue";
import BizIOrderExtractHead from "@/view/dec/imported_cigarettes/head/BizIOrderExtractHead.vue";
import {useOrderColumnsCommon} from "@/view/dec/imported_cigarettes/head/useOrderColumnsCommon";
import BizISelectOrderModal from "@/view/dec/imported_cigarettes/head/BizISelectOrderModal.vue";
const { importConfig } = useImport()
const { getSupplierList } = useOrderColumnsCommon()


/* 引入通用方法 */
const {
  editConfig,
  show,
  page,
  showSearch,
  headSearch,
  handleViewByRow,
  operationEdit,
  onPageChange,
  handleShowSearch,
  handlerSearch,
  dataSourceList,
  tableLoading,
  getTableScroll,
  exportLoading,
  getList,
  ajaxUrl,
  doExport,
  handlerRefresh,
  getSearchParams

} = useCommon()



defineOptions({
  name: 'BizIOrderHeadList',
});



const importShow = ref(false)



onMounted(fn => {


  ajaxUrl.selectAllPage = ycCsApi.bizIOrderHead.list
  ajaxUrl.exportUrl = ycCsApi.bizIOrderHead.export

  // tableHeight.value = getTableScroll(100,'');

  getList()


  initCustomColumn()

  nextTick(()=>{
    getSumTotal()
  })


})


/* 引入表单数据 */
const gridData = reactive({
  selectedRowKeys: [],
  selectedData:[],
  loading: false,
});



/* 选中事件(这里的事件，顺序不能乱，必须key,后面点跟实体，不然回显出问题。) */
const onSelectChange = (selectedRowKeys, rowSelectData) => {
  gridData.selectedData = rowSelectData;
  gridData.selectedRowKeys = selectedRowKeys;
};


/* 按钮loading */
const deleteLoading = ref(false)



/* 返回事件 */
const handlerOnBack = (flag) => {
  show.value = !show.value;
  // 返回清空选择数据
  gridData.selectedData = [];
  gridData.selectedRowKeys = [];
  editConfig.editData = {}
  if (flag){
    getList()
  }
}

/* 新增数据 */
const handlerAdd = ()=>{
  editConfig.value.editStatus = editStatus.ADD
  show.value = !show.value;
}


/* 编辑数据 */
const handlerEdit = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1){
    message.warning('只能选择一条数据')
    return
  }
  // 仅数据状态为0编制的允许操作删除。对状态不符的，提示：仅编制状态数据允许删除。
  // if(gridData.selectedData[0].dataStatus!== '0' &&
  //    gridData.selectedData[0].purchaseDataStatus !== '0' &&
  //    gridData.selectedData[0].salesDataStatus !== '0' &&
  //    gridData.selectedData[0].inboundReceiptStatus !== '0' &&
  //    gridData.selectedData[0].outboundReceiptStatus !== '0'){
  //   message.warning('仅状态为0编制的数据可以操作编辑!')
  //   return
  // }

  editConfig.value.editStatus = editStatus.EDIT
  editConfig.value.editData =  gridData.selectedData[0]

  show.value =!show.value;
}


/* 双击table进入编辑 */
const handleRowDblclick = (record) => {
  // 仅数据状态为0编制的允许操作删除。对状态不符的，提示：仅编制状态数据允许删除。
  // if(record.dataStatus!== '0' &&
  //    record.purchaseDataStatus !== '0' &&
  //    record.salesDataStatus !== '0' &&
  //    record.inboundReceiptStatus !== '0' &&
  //    record.outboundReceiptStatus !== '0'){
  //   message.warning('仅状态为0编制的数据可以操作编辑!')
  //   return
  // }
  editConfig.value.editStatus = editStatus.EDIT
  editConfig.value.editData =  record
  show.value =!show.value;
};

// 自定义行属性
const customRow = (record) => {
  return {
    onDblclick: () => {
      handleRowDblclick(record);
    },
    style: {
      cursor: 'pointer'
    }
  };
};
const handleEditByRow = (row)=> {
  // 仅数据状态为0编制的允许操作删除。对状态不符的，提示：仅编制状态数据允许删除。
  // 仅数据状态为0编制的允许操作删除。对状态不符的，提示：仅编制状态数据允许删除。
  // if(row.dataStatus!== '0' &&
  //    row.purchaseDataStatus !== '0' &&
  //    row.salesDataStatus !== '0' &&
  //    row.inboundReceiptStatus !== '0' &&
  //    row.outboundReceiptStatus !== '0'){
  //   message.warning('仅状态为0编制的数据可以操作编辑!')
  //   return
  // }
  // 在这里添加处理编辑行的逻辑
  show.value = !show.value
  editConfig.value.editStatus = editStatus.EDIT
  editConfig.value.editData = row
}

/* 删除数据 */
const handlerDelete = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1){
    message.warning('只能选择一条数据')
    return
  }
  // 仅数据状态为0编制的允许操作删除。对状态不符的，提示：仅编制状态数据允许删除。
  if(gridData.selectedData[0].dataStatus !== '0'){
    message.warning('仅编制状态数据允许删除')
    return
  }

  checkNextModuleExistEffectiveData(gridData.selectedData[0]).then(res=>{
    console.log('删除校验：',res)
    if (res.code !== 200){
      message.error("该单据在关联模块已产生数据，不允许删除")
    }else {
      // 弹出确认框
      Modal.confirm({
        title: '提醒?',
        icon: createVNode(ExclamationCircleOutlined),
        okText: '删除',
        cancelText: '取消',
        content: '确认删除所选项吗？',
        onOk() {
          deleteLoading.value = true
          deleteBizErpIOrderHead(gridData.selectedRowKeys).then(res => {
            if (res.code === 200) {
              message.success("删除成功！")
              getList()
            }else {
              message.error(res.message)
            }
          }).finally(() => {
            deleteLoading.value = false
          })
        },
        onCancel() {
        },
      });
    }
  })
}


/* 打开导入 */
const handlerImport = ()=>{
  importShow.value = !importShow.value
  // 参数外部重置 可以选择在onMounted里面重置 或者 打开时重置
  importConfig.taskCode = 'base_client_import'
}


/* 导入成功后事件 */
const importSuccess = ()=>{
  importShow.value =!importShow.value
  getList()
}


/* 导出事件 */
const handlerExport = () =>{
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  const timestamp = `${year}${month}${day}${hours}${minutes}${seconds}`
  doExport(`进口订单表头${timestamp}.xlsx`, totalColumns)
}



/* 自定义设置 */
const showColumns =  ref([])

/* 唯一键 */
const tableKey = ref('')
tableKey.value = window.$vueApp ? window.majesty.router.currentRoute.value.path : useRoute().path
const originalColumns = ref()


/* 自定义显示列初始化操作 */
const initCustomColumn = () => {
  // 这里是拷贝是属于
  let tempColumns = deepClone(totalColumns.value)
  let dealColumns = []
  // 使用map遍历会丢失customRender方法，所以使用forEach
  tempColumns.map((item) => {
    let newObj = Object.assign({}, item);
    newObj["visible"] = true;
    // 需要将customRender 方法追加到新对象中
    if (item.customRender) {
      newObj["customRender"] = item.customRender;
    }
    dealColumns.push(newObj);
  });
  //原始列信息
  originalColumns.value = dealColumns;
}


/* 选中visible为true的数据进行显示 */
const customColumnChange = (settingColumns)  => {
  totalColumns.value = settingColumns.filter((item) => item.visible === true);
  showColumns.value = [...totalColumns.value]
}


/* 初始化汇总数据 */
const sumTotal =  reactive({
  qtyTotal:0,
  decTotal:0,
})
const getSumTotal = ()=>{
  getOrderHeadTotal(getSearchParams()).then(res=>{
    if (res.code === 200){
      Object.assign(sumTotal,res.data)
    }
  })

}

/* 监控 dataSourceList */
watch(totalColumns.value, (newValue, oldValue) => {
  if(!window.$vueApp){
    showColumns.value = [...totalColumns.value];
  }else {
    // console.log('newValue',newValue)
    // console.log('[...totalColumns]', [...totalColumns.value])
    // console.log('[totalColumns]', totalColumns.value)
    if (newValue.length === 0) {
      showColumns.value = [...totalColumns.value];
    }else {
      showColumns.value = newValue.map((item) => {
        item.visible = true;
        return item;
      })
      totalColumns.value = newValue.map((item) => {
        item.visible = true;
        return item;
      })
      // console.log('totalColumns.value', totalColumns.value)
    }
  }
},{immediate:true,deep:true})



/* 打开新增弹框 */
const showAddModal = ref(false)
/* 新增弹框确认loading */
const confirmLoading = ref(false)
/* 提取弹框ref */
const extractRef = ref(null)
const handlerOpenAddModal = () => {
  showAddModal.value = true
}
/* 关闭新增弹框 */
const handleCloseModal = () => {
  showAddModal.value = false
  confirmLoading.value = false
}

/* 打开选择订单号弹框 */
const showSelectModal = ref(false)


/* 订单号数据 */
const rebootOrderNoList = reactive({})

/* 选择订单号 */
const selectOrderNoList = ref('')


/* 订单数据的的ref */


/* 关闭选择订单号弹框 */
const handleCloseSelectModal = ()=>{
  showSelectModal.value = false
  Object.assign(rebootOrderNoList,{})
}

/* 新增订单数据（使用选择的订单号 ）*/
const handlerOrderNoSave = (data) =>{
  // console.log('data',data)
  extract(data.selectOrderNo)
}

/* 提取数据 */
const extractContractData =  () =>{
  selectOrderNoList.value = ''
  // 判断是否存在作废单据号 未使用
  rebootOrderNo({sids:extractRef.value.gridData.selectedRowKeys}).then(res=>{
    if (!isNullOrEmpty(res.data)){
      // 打开去人框 提示用户是否使用作废单据号
      Modal.confirm({
        title: '提醒?',
        icon: createVNode(ExclamationCircleOutlined),
        okText: '确定',
        cancelText: '取消',
        content: '是否使用作废空号？',
        onOk() {
          showSelectModal.value = true
          Object.assign(rebootOrderNoList,res.data)
        },
        onCancel() {
          extract('')
        }
      })
    }else {
      Object.assign(rebootOrderNoList,{})
      extract('')
    }
  })

}

/* 提取合同数据 */
const extract = async (selectOrderNo)=> {
  // console.log('extractContractData',extractRef.value.gridData.selectedRowKeys)
  confirmLoading.value = true
  // 提取数据
  const res =  await generateIOrder({sids:extractRef.value.gridData.selectedRowKeys,orderNo:selectOrderNo});
  try {
    if (res.code === 200){
      message.success(res.message)
      confirmLoading.value = false
      showAddModal.value = false
      showSelectModal.value = false
      getList()
      // console.log('dataSourceList.value',dataSourceList.value)
      editConfig.value.editStatus = editStatus.EDIT
      editConfig.value.editData =  res.data
      show.value =!show.value

    }else{
      message.error(res.message)
      confirmLoading.value = false
    }
  }catch (err) {
    confirmLoading.value = false
  }
}

/* 版本复制 */
const copyLoading = ref(false)
const handlerCopy = () =>{
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1){
    message.warning('只能选择一条数据')
    return
  }
  // 如果状态是非2的，进行弹框提示
  if (gridData.selectedData[0].dataStatus !== '2'){
    Modal.confirm({
      title: '提醒?',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '确定',
      cancelText: '取消',
      content: '是否复制所选项？',
      onOk() {
        Modal.confirm({
          title: '提醒?',
          icon: createVNode(ExclamationCircleOutlined),
          okText: '确定',
          cancelText: '取消',
          content: '当前单据存在有效数据，是否将其作废并重新生成一份新数据？',
          onOk() {
            copy(gridData.selectedData[0])
          }
        })
      }
    })
  }else {
    //
    // console.log('gridData',gridData)
    Modal.confirm({
      title: '提醒?',
      icon: createVNode(ExclamationCircleOutlined),
      okText: '确定',
      cancelText: '取消',
      content: '是否复制所选项？',
      onOk() {
        // 二次弹框确认
        checkOrderNoNotCancel(gridData.selectedData[0]).then(res=>{
          if (res.code === 200 && res.data === 123){
            Modal.confirm({
              title: '提醒?',
              icon: createVNode(ExclamationCircleOutlined),
              okText: '确定',
              cancelText: '取消',
              content: '当前单据存在有效数据，是否将其作废并重新生成一份新数据？',
              onOk() {
                copy(gridData.selectedData[0])
              }
            })
          }else if( res.code === 200){
            // 直接进行复制
            copy(gridData.selectedData[0])
          }
        })
      },
    });
  }
}

const copy = (data)=>{
  // 版本复制
  copyOrderVersion(data).then(res=>{
    copyLoading.value = true
    if (res.code === 200){
      message.success(res.message)
      getList()
      copyLoading.value = false
    }else {
      copyLoading.value = false
    }
  })
}


/* 作废数据 */
const handlerCancelLoading = ref(false)
const handlerCancel = ()=>{
  // 先校验下游是否存在有效数据
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1){
    message.warning('只能选择一条数据')
    return
  }
  // 弹出确认框
  Modal.confirm({
    title: '提醒?',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: '确认将所选项发送作废吗？',
    onOk() {
      handlerCancelLoading.value = true
      checkNextModuleExistEffectiveData(gridData.selectedData[0]).then(res=>{
        if (res.code !== 200){
          message.error("进口费用存在有效数据，不允许作废!")
          handlerCancelLoading.value = false
        }else {
          orderCancelData(gridData.selectedData[0]).then(res=>{
            if (res.code === 200){
              message.success(res.message)
              handlerCancelLoading.value = false

              getList()
              getSumTotal()

            }else {
              message.error(res.message)
              handlerCancelLoading.value = false
            }
          })
        }
      })
    },
    onCancel() {
      handlerCancelLoading.value = false
    },
  });

}


const sendOrderAudit = ()=>{
  message.success('发送审核功能待开发！')
  getList()
  getSumTotal()
  // 取消选中状态
  gridData.selectedData = []
  gridData.selectedRowKeys = []

}

import { useTableScrollY } from '@/view/utils/useTableScrollY';
const { containerRef, paginationRef, tableScrollY } = useTableScrollY([showSearch, dataSourceList]);



</script>

<style lang="less" scoped>


</style>
