<template>
  <section class="cs-action" style="height:300px">
    <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '60px' } }" :rules="rules" :model="formData" >
        <div class="cs-form grid-container ">
          <a-form-item name="selectOrderNo" :label="'订单号'" class="grid-item merge-3" :colon="false">
            <cs-select    optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.selectOrderNo" id="businessType">
              <a-select-option class="cs-select-dropdown" v-for="item in props.orderList.rebootContractList"  :key="item" :value="item" :label=" item" style="height: 20px">
                {{item}}
              </a-select-option>
            </cs-select>
          </a-form-item>
        </div>
      <div class="cs-submit-btn merge-3">
        <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
        <a-button size="small" type="primary" @click="handlerSave"  :loading="handlerSaveLoading" class="cs-margin-right">保存</a-button>
      </div>
    </a-form>
  </section>
</template>

<script setup>


import {editStatus, productClassify} from "@/view/common/constant";

defineOptions({
  name: 'BizISelectOrderModal',
})


/* 进口合同列表 */
import {onMounted, ref, watch} from "vue";
import CsSelect from "@/components/select/CsSelect.vue";

const emit = defineEmits(['cancel','save'])


//         { minContractNo: '', rebootContractList:[] }
const props = defineProps({
  orderList:{
    type: Object,
  }
})



const formData = ref({
  selectOrderNo:''
})


/* 监控 props */
watch(() => props.orderList, (newVal, oldVal) => {
  // console.log('newVal', newVal)
  formData.value.selectOrderNo = newVal.minContractNo
},{immediate:true,deep:true})

onMounted(() => {

})

defineExpose({
  formData
})



const rules = {
  selectOrderNo: [
    {
      required: true,
      message: '订单号不能为空!',
      trigger: 'blur',
    },
  ],
}


const formRef = ref(null);
const handlerSaveLoading = ref(false)
const handlerSave = () => {
  console.log('formData',formData.value)
  // 校验表单数据是否通过校验

  formRef.value
    .validate()
    .then(() => {
      handlerSaveLoading.value = true
      emit('save',formData.value)
    })
    .catch(error => {
      console.log('validate failed', error);
    }).finally(() => {
      handlerSaveLoading.value = false
    })

}

const onBack = (isBack) => {
  console.log('isBack',isBack)
  emit('cancel',isBack)

}

</script>

<style lang="less" scoped>

.header-search{
  margin: 10px 0;
}

/* 弹框表格样式 */
.cs-action-item-modal-table {
  padding: 4px 0;
  margin: 2px 0;
  box-sizing: border-box;
  min-height: calc(100vh);
  height: auto;
  .surely-table-body{
    min-height: calc(100vh);
  }
}
.cs-action-item-modal-table-empty{

  padding: 4px 0;
  margin: 2px 0;
  box-sizing: border-box;
  min-height: 500px;
  line-height: 500px;
}


.cs-submit-btn {
  padding-top: 240px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 30px;
  margin: 20px 0;
  padding-bottom: 20px;
}

</style>
