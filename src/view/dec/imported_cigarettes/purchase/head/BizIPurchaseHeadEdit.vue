<template>
  <section>
    <a-card size="small" title="进货信息表头" class="cs-card-form">
      <a-spin :spinning="spinning"  >
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData"   class=" grid-container">

          <!-- 业务类型 -->
          <a-form-item name="businessType"   :label="'业务类型'" class="grid-item"  :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.businessType" id="businessType">
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.businessType"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>

          <!-- 进货单号 -->
          <a-form-item name="purchaseOrderNo"   :label="'进货单号'" class="grid-item"  :colon="false">
            <a-input :disabled="props.operationStatus === editStatus.SHOW || formData.dataStatus  !== '0'"  size="small" v-model:value="formData.purchaseOrderNo" allow-clear />
          </a-form-item>
          <!-- 占位 -->
          <a-form-item name="placeholder"  class="grid-item merge-1"  :colon="false"></a-form-item>
          <!-- 船名航次 -->
          <a-form-item name="vesselVoyage"   :label="'船名航次'" class="grid-item"  :colon="false">
            <a-input :disabled="props.operationStatus === editStatus.SHOW || formData.dataStatus  !== '0'"  size="small" v-model:value="formData.vesselVoyage" allow-clear />
          </a-form-item>
          <!-- 开航日期 -->
          <a-form-item name="sailingDate"   :label="'开航日期'" class="grid-item"  :colon="false">
            <a-date-picker
              :disabled="props.operationStatus === editStatus.SHOW || formData.dataStatus  !== '0'"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
              v-model:value="formData.sailingDate"
              allow-clear
            />
          </a-form-item>
          <!-- 预计抵达日期 -->
          <a-form-item name="expectedArrivalDate"   :label="'预计抵达日期'" class="grid-item"  :colon="false">
            <a-date-picker
              :disabled="props.operationStatus === editStatus.SHOW || formData.dataStatus  !== '0'"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
              v-model:value="formData.expectedArrivalDate"
              allow-clear
            />
          </a-form-item>
          <!-- 报关单号 -->
          <a-form-item name="entryNo"   :label="'报关单号'" class="grid-item"  :colon="false">
            <a-input :disabled="props.operationStatus === editStatus.SHOW || formData.dataStatus  !== '0'"  size="small" v-model:value="formData.entryNo" allow-clear />
          </a-form-item>
          <!-- 报关日期 -->
          <a-form-item name="entryDate"   :label="'报关日期'" class="grid-item"  :colon="false">
            <a-date-picker
              :disabled="props.operationStatus === editStatus.SHOW || formData.dataStatus  !== '0'"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
              v-model:value="formData.entryDate"
              allow-clear
            />
          </a-form-item>
          <!-- 备注 -->
          <a-form-item name="note" :label="'备注'" class="grid-item merge-3"  :colon="false">
            <a-textarea :disabled="props.operationStatus === editStatus.SHOW || formData.dataStatus  !== '0'" size="small" v-model:value="formData.note" :autoSize="{ minRows: 3, maxRows: 4 }" allow-clear></a-textarea>
          </a-form-item>
          <!-- 单据状态 -->
          <a-form-item name="dataStatus"   :label="'进货单据状态'" class="grid-item"  :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.dataStatus" id="dataStatus" >
              <a-select-option class="cs-select-dropdown" v-for="item in productClassify.orderStatus"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>


          <!-- 进货确认时间 -->
          <a-form-item name="purchaseConfirmationTime"   :label="'进货确认时间'" class="grid-item"  :colon="false">
            <a-date-picker
              :disabled="true"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
              v-model:value="formData.purchaseConfirmationTime"
              allow-clear
            />
          </a-form-item>

          <!-- 制单人 -->
          <a-form-item name="insertUserName"   :label="'制单人'" class="grid-item "  :colon="false">
            <a-input :disabled="true"  size="small" v-model:value="formData.insertUserName" allow-clear />
          </a-form-item>
          <!-- 订单制单时间 -->
          <a-form-item name="insertTime"   :label="'制单时间'" class="grid-item"  :colon="false">
            <a-date-picker
              :disabled="true"
              id="insertTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
              v-model:value="formData.insertTime"
              allow-clear
            />
          </a-form-item>






          <div class="cs-submit-btn merge-3">

            <a-button v-if="!(props.operationStatus === editStatus.SHOW)" size="small" type="primary" @click="handlerSave" class="cs-margin-right" :loading="headSaveLoading" :disabled="formData.dataStatus  !== '0'">保存
            </a-button>

            <!--
              去除保存关闭按钮
              <a-button v-if="!(props.operationStatus === editStatus.SHOW)" size="small" type="primary" @click="handlerSaveClose" class="cs-margin-right" :loading="headSaveLoadingClose" :disabled="formData.dataStatus  !== '0'">保存关闭</a-button>
            -->
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
            <!-- 确认 -->
            <a-button v-if="!(props.operationStatus === editStatus.SHOW)" size="small" type="ghost"
                      @click="confirmOrderHead" class="cs-margin-right"
                      v-show="props.editConfig.editStatus !== editStatus.SHOW " :loading="confirmOrderLoading">
              <template #icon>
                <GlobalIcon class="btn-icon" type="check"  style="font-size: 12px;"/>
              </template>
              <template #default>
                确认
              </template>
            </a-button>

            <!-- 更新报关信息 -->
            <a-button size="small" type="ghost" @click="updateCustomsInfo" class="cs-margin-right">
              <template #icon>
                <GlobalIcon class="btn-icon" type="edit"  style="font-size: 12px;"/>
              </template>
              <template #default>
                更新报关信息
              </template>
            </a-button>
          </div>
        </a-form>

        <!-- 报关信息更新弹窗 -->
        <a-modal v-model:visible="customsModalVisible" title="更新报关信息" @ok="handleCustomsModalOk" @cancel="handleCustomsModalCancel"
                 okText="确认"
                 cancelText="取消">
          <a-form :model="customsFormData" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
            <a-form-item label="报关单号">
              <a-input v-model:value="customsFormData.customsDeclarationNo" />
            </a-form-item>
            <a-form-item label="报关日期">
              <a-date-picker
                v-model:value="customsFormData.customsDeclarationDate"
                valueFormat="YYYY-MM-DD"
                format="YYYY-MM-DD"
                :locale="locale"
                style="width: 100%"
              />
            </a-form-item>
          </a-form>
        </a-modal>
      </div>
      </a-spin>
    </a-card>



    <!-- 操作按钮区域 -->
    <div class="cs-action-btn">
      <div class="cs-action-btn-item" v-has="['yc-cs:importedCigarettes-order-purchase:updateBoxNo']" v-if="!(props.operationStatus === editStatus.SHOW)">
        <a-button size="small" @click="handlerOpenUpdateBox"  :disabled="formData.dataStatus  !== '0'" >
          <template #icon>
            <GlobalIcon type="form" style="color:#5897fb"/>
          </template>
          维护箱号
        </a-button>
      </div>
      <div class="cs-action-btn-item" v-has="['yc-cs:importedCigarettes-order-purchase:invoiceTotal']">
        <a-button size="small" @click="handlerOpenInvoiceTotal" >
          <template #icon>
            <GlobalIcon type="cloud" style="color:#5897fb"/>
          </template>
          发票汇总显示
        </a-button>
      </div>
      <div class="cs-action-btn-item" v-has="['yc-cs:importedCigarettes-order-purchase:updateInvoiceNo']" v-if="!(props.operationStatus === editStatus.SHOW)">
        <a-button size="small" @click="handlerOpenUpdateInvoice"  :disabled="formData.dataStatus  !== '0'" >
          <template #icon>
            <GlobalIcon type="form" style="color:#5897fb"/>
          </template>
          维护进口发票号
        </a-button>
      </div>
    </div>
    <a-tabs v-model:activeKey="headInnerTabKey" v-if="formData.sid" >
      <a-tab-pane key="1" tab="商品子表">
        <a-card size="small" title="进货信息表体" class="cs-card-form">
          <biz-i-purchase-list-list  ref="purchaseListRef" :head-id="formData.sid" :operation-status="props.operationStatus" :is-edit="formData.dataStatus"></biz-i-purchase-list-list>
        </a-card>
      </a-tab-pane>
      <a-tab-pane key="2" tab="装箱子表">
        <a-card size="small" title="装箱表体" class="cs-card-form">
          <biz-i-purchase-list-box-list  :head-id="formData.sid"  :operation-status="props.operationStatus" :disabledStatus="formData.dataStatus  !== '0'"></biz-i-purchase-list-box-list>
        </a-card>
      </a-tab-pane>
    </a-tabs>


    <!-- 维护箱号 -->
    <cs-modal :visible="isShowUpdateBox" :title="'维护箱号'" :width="1000" :footer="false" @cancel="handlerCloseUpdateBox">
      <template #customContent>
        <biz-i-purchase-update-box-modal ref="updateBoxRef" :data-source-list="purchaseListRef.gridData.selectedData" @cancel="handlerCloseUpdateBox"></biz-i-purchase-update-box-modal>
      </template>
    </cs-modal>


    <!-- 发票汇总显示 -->
    <cs-modal :visible="isShowInvoiceTotal" :title="'发票汇总显示'" :width="1000" :footer="true" @cancel="handlerCloseInvoiceTotal">
      <template #customContent>
        <biz-i-purchase-invoice-total-modal ref="invoiceTotalRef" :head-id="formData.sid" @cancel="handlerCloseInvoiceTotal"></biz-i-purchase-invoice-total-modal>
      </template>
      <template #footer>
        <div style="display: flex;justify-content: right;align-items: center">
          <a-button @click="handlerCloseInvoiceTotal" size="small">返回</a-button>
        </div>
      </template>
    </cs-modal>

    <!-- 进口发票号 -->
    <cs-modal :visible="isShowUpdateInvoice" :title="'维护进口发票号'" :width="1000" :footer="false" @cancel="handlerCloseUpdateInvoice">
      <template #customContent>
        <biz-i-purchase-update-invoice-modal ref="updateInvoiceRef" :data-source-list="purchaseListRef.gridData.selectedData" @cancel="handlerCloseUpdateInvoice"></biz-i-purchase-update-invoice-modal>
      </template>
    </cs-modal>



  </section>
</template>

<script setup>

import {message,Modal,Select} from "ant-design-vue";
import {onMounted, reactive, ref,h,createVNode} from "vue";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
import BizIPurchaseHeadList from "@/view/dec/imported_cigarettes/purchase/list/BizIPurchaseListList.vue";
import {
  getPurchaseHeadByOrderSid,
  insertBizIPurchaseHead,
  updateBizIPurchaseHead,
  updateEntryInfo,
  getIWarehouseReceiptAbandonedData, generateBizIWarehouseReceipt, confirmIOrderHead,
} from "@/api/cs_api_constant";
import {editStatus, productClassify} from "@/view/common/constant";
import BizIPurchaseListList from "@/view/dec/imported_cigarettes/purchase/list/BizIPurchaseListList.vue";
import BizIPurchaseListBoxList from "@/view/dec/imported_cigarettes/purchase/box/BizIPurchaseListBoxList.vue";
import BizIPurchaseUpdateBoxModal from "@/view/dec/imported_cigarettes/purchase/head/BizIPurchaseUpdateBoxModal.vue";
import BizIPurchaseUpdateInvoiceModal from "@/view/dec/imported_cigarettes/purchase/head/BizIPurchaseUpdateInvoiceModal";
import CsModal from "@/components/modal/cs-modal.vue";
import BizIPurchaseInvoiceTotalModal
  from "@/view/dec/imported_cigarettes/purchase/head/BizIPurchaseInvoiceTotalModal.vue";
import ycCsApi from "@/api/ycCsApi";
import {isNullOrEmpty} from "@/view/utils/common";
import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
import useEventBus from "@/view/common/eventBus";
const { getPCode } = usePCode()

defineOptions({
  name:'BizIPurchaseHeadEdit'
})


const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  },
  headId:{
    type:String,
    default: () => ''
  },
  /* 表头传入状态 查看/编辑 */
  operationStatus: {
    type: String,
    default: ''
  }
});

// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack']);

const onBack = (val) => {
  emit('onEditBack', val);
};

// 是否禁用
const showDisable = ref(false)

/* 进货信息 - 表体+装箱子表 */
const headInnerTabKey = ref('1')

// 表单数据
const formData = ref({
  // 业务类型
  businessType:'',
  // 主建SID
  sid:'',
  // 制单人
  insertUser:'',
  // 订单制单时间
  insertTime:'',
  // 创建人姓名
  insertUserName:'',
  // 更新人
  updateUser:'',
  // 更新时间
  updateTime:'',
  // 更新人姓名
  updateUserName:'',
  // 企业代码
  tradeCode:'',
  // 进货单号
  purchaseOrderNo:'',
  // 表头HEAD_ID
  headId:'',
  // 船名航次
  vesselVoyage:'',
  // 开航日期
  sailingDate:'',
  // 预计抵达日期
  expectedArrivalDate:'',
  // 报关单号
  entryNo:'',
  // 报关日期
  entryDate:'',
  // 进货数据状态
  purchasingDataStatus:'',
  // 进货确认时间
  purchaseConfirmationTime:'',
  // 版本号
  versionNo:'',
  // 数据状态
  dataStatus:'',
  // 拓展字段1
  extend1:'',
  // 拓展字段2
  extend2:'',
  // 拓展字段3
  extend3:'',
  // 拓展字段4
  extend4:'',
  // 拓展字段5
  extend5:'',
  // 拓展字段6
  extend6:'',
  // 拓展字段7
  extend7:'',
  // 拓展字段8
  extend8:'',
  // 拓展字段9
  extend9:'',
  // 拓展字段10
  extend10:'',
  // 备注
  note:'',
  // 计划编号
  planNo:'',
  // 序号
  serialNo:'',
  // 订单信息表头-订单号
  orderNo:'',
  // 是否流入下一个节点
  isNext:'',
  // 进货信息表体数据
  purchaseListParams:[],
  // 报关单号
  customsDeclarationNo: '',
  // 报关日期
  customsDeclarationDate: '',
})
// 校验规则
const rules = {
  businessType:[
    {
      required: true,
      message: '业务类型不能为空',
      trigger: 'blur'
    }
  ],
  sid:[
    {max: 50, message: '主建SID长度不能超过 50位字节', trigger: 'blur'}
  ],
  insertUser:[
    {max: 50, message: '制单人长度不能超过 50位字节', trigger: 'blur'}
  ],
  insertTime:[
    {required: true, message: '制单时间不能为空', trigger: 'blur'},
  ],
  insertUserName:[
    {required: true, message: '制单人不能为空', trigger: 'blur'},
    {max: 50, message: '制单人长度不能超过 50位字节', trigger: 'blur'}
  ],
  updateUser:[
    {max: 50, message: '更新人长度不能超过 50位字节', trigger: 'blur'}
  ],
  updateTime:[
  ],
  updateUserName:[
    {max: 50, message: '更新人姓名长度不能超过 50位字节', trigger: 'blur'}
  ],
  tradeCode:[
    {max: 50, message: '企业代码长度不能超过 50位字节', trigger: 'blur'}
  ],
  purchaseOrderNo:[
    {required: true, message: '进货单号不能为空', trigger: 'blur'},
    {max: 60, message: '进货单号长度不能超过 60位字节', trigger: 'blur'}
  ],
  headId:[
    {max: 50, message: '表头HEAD_ID长度不能超过 50位字节', trigger: 'blur'}
  ],
  vesselVoyage:[
    {max: 60, message: '船名航次长度不能超过 60位字节', trigger: 'blur'}
  ],
  sailingDate:[
  ],
  expectedArrivalDate:[
  ],
  entryNo:[
    {max: 18, message: '报关单号长度不能超过 18位字节', trigger: 'blur'}
  ],
  entryDate:[
  ],
  purchasingDataStatus:[
    {max: 10, message: '进货数据状态长度不能超过 10位字节', trigger: 'blur'}
  ],
  purchaseConfirmationTime:[
  ],
  customsDeclarationNo: [
    {max: 60, message: '报关单号长度不能超过 60位字节', trigger: 'blur'}
  ],
  versionNo:[
    {max: 10, message: '版本号长度不能超过 10位字节', trigger: 'blur'}
  ],
  dataStatus:[
    {max: 10, message: '进货数据状态状态长度不能超过 10位字节', trigger: 'blur'}
  ],
  extend1:[
    {max: 200, message: '拓展字段1长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend2:[
    {max: 200, message: '拓展字段2长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend3:[
    {max: 200, message: '拓展字段3长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend4:[
    {max: 200, message: '拓展字段4长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend5:[
    {max: 200, message: '拓展字段5长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend6:[
    {max: 200, message: '拓展字段6长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend7:[
    {max: 200, message: '拓展字段7长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend8:[
    {max: 200, message: '拓展字段8长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend9:[
    {max: 200, message: '拓展字段9长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend10:[
    {max: 200, message: '拓展字段10长度不能超过 200位字节', trigger: 'blur'}
  ],
  note:[
    {max: 200, message: '备注长度不能超过 200位字节', trigger: 'blur'}
  ],
  planNo:[
    {max: 60, message: '计划编号长度不能超过 60位字节', trigger: 'blur'}
  ],
  serialNo:[
    { type: 'number', message: '序号不是有效的数字!'},
  ],
  orderNo:[
    {max: 60, message: '订单信息表头-订单号长度不能超过 60位字节', trigger: 'blur'}
  ],
  isNext:[
    {max: 10, message: '是否流入下一个节点长度不能超过 10位字节', trigger: 'blur'}
  ]
}

const pCode = ref('')

/* 加载loading */
const spinning = ref(true);

// vue3中使用ref,需要先定义，然后在模板中使用。因为在模板渲染之前，DOM 元素还不存在。
const formRef = ref(null);
// 保存
const headSaveLoading = ref(false)
const headSaveLoadingClose = ref(false)

const {emitEvent} = useEventBus()

const handlerSave = () => {


  console.log('选择进货信息表体数据为：',purchaseListRef.value.dataSourceList)

  // console.log('订单表体ref:',orderListRef.value.dataSourceList)
  Object.assign(formData.value, {
    purchaseListParams:purchaseListRef.value.dataSourceList
  })
  formRef.value
    .validate()
    .then(() => {
      headSaveLoading.value = true
      purchaseListRef.isisEditLoading = true
      updateBizIPurchaseHead(formData.value.sid,formData.value).then((res)=>{
        if (res.code === 200){
          // 定义事件，父组件去触发子组件的方法
          message.success('修改成功!')
          Object.assign(formData.value, res.data);
          formData.value.insertTime = res.data.updateTime
          formData.value.insertUserName =  res.data.updateUserName
          emitEvent('refreshPurchaseListBox')
          emitEvent('refreshPurchaseList')
          // console.log('res',res.data)
          onBack({
            editData: null,
            showBody: true,
            showBodyPurchaseHead:true,
            editStatus: editStatus.EDIT
          })
          headSaveLoading.value = false

        }else {
          message.error(res.message)
          headSaveLoading.value = false
        }
      }).finally(() => {
        purchaseListRef.isisEditLoading = false
      })
      // }
    })
    .catch(error => {
      console.log('validate failed', error);
    })
};

const handlerSaveClose = ()=>{
  formRef.value
    .validate()
    .then(() => {
      headSaveLoadingClose.value = true
      updateBizIPurchaseHead(formData.value.sid,formData.value).then((res)=>{
        if (res.code === 200){
          // 定义事件，父组件去触发子组件的方法
          message.success('修改成功!')
          Object.assign(formData.value, res.data);
          onBack(true)
          headSaveLoadingClose.value = false

        }else {
          message.error(res.message)
          headSaveLoadingClose.value = false
        }
      })
      // }
    })
    .catch(error => {
      console.log('validate failed', error);
    })
}


/* 根据进货订单信息表头sid 获取进货信息数据 */
const initPurchaseHeadEdit = async () => {
  const res = await getPurchaseHeadByOrderSid({"headId":props.headId})
    if (res.code === 200){

      Object.assign(formData.value, res.data);
      console.log(formData.value.insertTime)
      spinning.value = false
    }else {
      message.error(res.message)
    }
}



/* 是否显示维护箱号 */
const isShowUpdateBox = ref(false)
const updateBoxRef = ref()
/* 选择的数据源 */
const selectedDataSource = ref([])
/* 装箱表体 ref */
const purchaseListRef = ref()
/* 打开维护箱号 */
const handlerOpenUpdateBox = ()=>{

  // 判断是否选择了数据
  if (purchaseListRef.value.gridData.selectedData.length === 0){
    message.warning('请选择数据!')
    return
  }
  isShowUpdateBox.value = true
  // console.log('purchaseListRef',purchaseListRef.value.gridData.selectedData)
}
/* 关闭维护箱号 */
const handlerCloseUpdateBox = (val)=>{
  isShowUpdateBox.value = false
  purchaseListRef.value.gridData.selectedData = []
  purchaseListRef.value.gridData.selectedRowKeys = []
  if (val === true){
    purchaseListRef.value.getList()
  }
}




/* 发票号汇总显示 */
const invoiceTotalRef = ref()
const isShowInvoiceTotal = ref(false)
const handlerOpenInvoiceTotal = ()=>{
  isShowInvoiceTotal.value = true
}
const handlerCloseInvoiceTotal = (val)=>{
  isShowInvoiceTotal.value = false
}
const isShowUpdateInvoice = ref(false)
const updateInvoiceRef = ref()
const handlerOpenUpdateInvoice = ()=>{

  // 判断是否选择了数据
  if (purchaseListRef.value.gridData.selectedData.length === 0){
    message.warning('请选择数据!')
    return
  }
  isShowUpdateInvoice.value = true
  console.log('purchaseListRef',purchaseListRef.value.gridData.selectedData)
}
/* 关闭维护箱号 */
const handlerCloseUpdateInvoice = (val)=>{
  isShowUpdateInvoice.value = false
  purchaseListRef.value.gridData.selectedData = []
  purchaseListRef.value.gridData.selectedRowKeys = []
  if (val === true){
    purchaseListRef.value.getList()
  }
}


// 初始化操作
onMounted(() => {
  initPurchaseHeadEdit()
  getPCode().then(res=>{
    console.log('res',res)
    pCode.value = res;
  })

});
/* 订单表头确认 */
const confirmOrderLoading = ref(false)
const confirmOrderHead = ()=>{
  handleConfirm()

}
// 请求接口查询报废数据
const fetchAbandonedData = () => {
  const status = "2"
  window.majesty.httpUtil.postAction(
    `${ycCsApi.bizIWarehouseReceiptHead.abandonedData}/${status}`
  ).then(res => {
    return res.data;
  }).finally(() => {
  })
}


// 点击确定按钮的逻辑
const handleConfirm = () => {
  if (formData.value.dataStatus === '2'){
    message.warning('该数据已作废，不允许进行确认操作！')
    return
  }
  if (formData.value.dataStatus === '1'){
    message.warning('该数据已经确认，无需重复操作！')
    confirmOrderLoading.value = false
    return
  }
  // 弹出确认框
  Modal.confirm({
    title: '提醒?',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: '是否确认所选项？',
    onOk() {
      confirmOrderLoading.value = true
      let data  =null;
      const status = "2"
      window.majesty.httpUtil.postAction(
        `${ycCsApi.bizIWarehouseReceiptHead.abandonedData}/${status}`
      ).then(res => {
        data  = res.data;
        console.log("废弃号码"+data)
        const warehouseEntryNumber = data.warehouseEntryNumber; // 获取 warehouseEntryNumber
        const ladingNumber = data.ladingNumber;
        console.log("废弃号码datawarehouseEntryNumber:"+warehouseEntryNumber)
        console.log("废弃号码ladingNumber:"+ladingNumber)

        if ((warehouseEntryNumber&&warehouseEntryNumber.length>0)||(ladingNumber&&ladingNumber.length>0)) {
          Modal.confirm({
            title: '提示',
            content: '有废弃号码，是否重新使用？',
            okText: '确认',
            cancelText: '取消',
            onOk: () => {
              showSelectModal(warehouseEntryNumber,ladingNumber);
            },
            onCancel: () => {
              generateBizIWarehouse(null)
            },
          });
        } else {
          generateBizIWarehouse(null)
        }
      }).finally(() => {confirmOrderLoading.value = false});
    },
    onCancel() {
      confirmOrderLoading.value = false
    },
  });







};

///显示选择废弃号码的弹窗
const showSelectModal = (warehouseEntryNumber, ladingNumber) => {
  let selectedWarehouseEntry = null; // 存储选中的 warehouseEntryNumber
  let selectedLadingNumber = null; // 存储选中的 ladingNumber

  Modal.confirm({
    title: '选择废弃号码',
    okText: '确认',
    cancelText: '取消',
    content: () =>
      h('div', [
        // 第一个下拉框：warehouseEntryNumber
        h('div', { style: { marginBottom: '16px' } }, [
          h('span', { style: { marginRight: '8px' } }, '进仓编号:'),
          h(
            Select,
            {
              style: { width: '200px' },
              placeholder: '请选择仓库入库号',
              onChange: (value) => {
                selectedWarehouseEntry = value; // 更新选中的值
              },
            },
            warehouseEntryNumber.map((item) =>
              h(Select.Option, { key: item, value: item }, item)
            ),
          ),
        ]),
        // 第二个下拉框：ladingNumber
        h('div', [
          h('span', { style: { marginRight: '8px' } }, '提货单号:'),
          h(
            Select,
            {
              style: { width: '200px' },
              placeholder: '请选择提单号',
              onChange: (value) => {
                selectedLadingNumber = value; // 更新选中的值
              },
            },
            ladingNumber.map((item) =>
              h(Select.Option, { key: item, value: item }, item)
            ),
          ),
        ]),
      ]),
    onOk: async () => {
      generateBizIWarehouse(selectedWarehouseEntry,selectedLadingNumber)
    },
  });
};


/* 生成入库信息 */
const generateBizIWarehouse = async (warehouseEntryNumber,selectedLadingNumber)=>{
  const res = await generateBizIWarehouseReceipt({parentId:props.headId,warehouseEntryNumber:warehouseEntryNumber,ladingNumber:selectedLadingNumber})
  if (res.code === 200) {
    Object.assign(formData.value, res.data)
    message.success(res.message);
    onBack({
      editData:null,
      showBody: true,
      editStatus: editStatus.EDIT,
      showBodyWarehouseReceiptHead:true,
      showBodyPurchaseHead:true
    })
  }else {
    message.error(res.message)
  }

}

// 报关信息弹窗相关
const customsModalVisible = ref(false)
const customsFormData = ref({
  customsDeclarationNo: '',
  customsDeclarationDate: ''
})

// 打开报关信息更新弹窗
const updateCustomsInfo = () => {
  customsFormData.value.customsDeclarationNo = formData.value.entryNo
  customsFormData.value.customsDeclarationDate = formData.value.entryDate
  customsModalVisible.value = true
}

// 确认更新报关信息
const handleCustomsModalOk = () => {
  formData.value.entryNo = customsFormData.value.customsDeclarationNo
  formData.value.entryDate = customsFormData.value.customsDeclarationDate

  let params = {
    entryNo: customsFormData.value.customsDeclarationNo,
    entryDate: customsFormData.value.customsDeclarationDate
  }

  updateEntryInfo(formData.value.sid, params).then((res) => {
    if (res.code === 200){
      customsModalVisible.value = false
      message.success('报关信息更新成功!')
    } else {
      message.error(res.message)
    }
  })
}

// 取消更新报关信息
const handleCustomsModalCancel = () => {
  customsModalVisible.value = false
}

</script>

<style lang="less" scoped>


</style>



