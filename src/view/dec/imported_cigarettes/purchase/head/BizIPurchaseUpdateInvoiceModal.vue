<template>
  <section class="cs-card-form">
    <div>
      <a-form  ref="formRef" layout="inline"  label-align="right"  :label-col="{ style: { width: '75px' } }" :rules="rules" :model="formData"   class="grid-container" >
        <a-form-item name="invoiceNo"  label="进口发票号" class="grid-item-search merge-3"  :colon="false" >
          <div style="display: flex">
            <a-input  size="small" v-model:value="formData.invoiceNo" allow-clear />
            <a-button class="cs-margin-left" type="primary" size="small" @click="handlerSave" :loading="saveLoading">保存</a-button>
            <a-button class="cs-margin-left" type="primary" size="small" @click="handlerBack(false)">取消</a-button>
          </div>
        </a-form-item>
      </a-form>
    </div>
    <div>
<!--      <s-table-->
<!--        style="width: 100%;height: 100%;min-height: 100%;overflow-y: auto;overflow-x:auto"-->
<!--        ref="tableRef"-->
<!--        size="small"-->
<!--        class="cs-action-item-modal-table"-->
<!--        bordered-->
<!--        height="400px"-->
<!--        :scroll="{ y: 400,x:400 }"-->
<!--        :pagination="false"-->
<!--        :columns="showColumns"-->
<!--        :data-source="originalData"-->
<!--        :row-selection="{  selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange,-->
<!--         getCheckboxProps: (record) => ({-->
<!--              disabled: true-->
<!--         }),-->
<!--        }"-->
<!--        row-key="sid"-->
<!--      >-->
        <!-- 空数据 -->
<!--        <template #emptyText>-->
<!--          <a-empty description="暂无数据" />-->
<!--        </template>-->

<!--        <template #cellEditor="{ column, modelValue, save, closeEditor, editorRef, getPopupContainer,record,recordIndex }">-->
<!--          <template v-if="column.dataIndex === 'quantity'">-->
<!--            <a-input-number-->
<!--              :ref="editorRef"-->
<!--              v-model:value="modelValue.value"-->
<!--              @blur="handleBlur(save, closeEditor,column,record)"-->
<!--              @keydown.enter="handleEnter(column,save, closeEditor,record)"-->
<!--              @keydown.esc="closeEditor"-->
<!--              style="width: 100%"-->
<!--            />-->
<!--          </template>-->
<!--        </template>-->
<!--      </s-table>-->



    </div>

  </section>
</template>

<script setup>

import {addPushListInvoice} from "@/api/cs_api_constant";
/* 进口合同列表 */
import {onMounted, reactive, ref, watch} from "vue";
import {message} from "ant-design-vue";
import {deepClone, isNullOrEmpty} from "@/view/utils/common";
import useEventBus from "@/view/common/eventBus";
 const { emitEvent } = useEventBus()



const props = defineProps({
  dataSourceList: {
    type: Array,
    default: () => []
  }
})

defineOptions({
  name: 'BizIPurchaseUpdateInvoiceModal'
})

const emit = defineEmits(['cancel'])







/* 引入表单数据 */
const gridData = reactive({
  selectedRowKeys: [],
  selectedData:[],
  loading: false,
});

/* 关闭弹框 */
const handlerBack = (val) => {
  emit('cancel',val);
}



/* 表单信息 */
const formData = reactive({
  invoiceNo:''
})

const rules = {
  invoiceNo: [
    { required: false, message: '请输入发票号', trigger: 'blur' },
  ],
}


/* 选中事件(这里的事件，顺序不能乱，必须key,后面点跟实体，不然回显出问题。) */
const onSelectChange = (selectedRowKeys, rowSelectData) => {
  gridData.selectedData = rowSelectData;
  gridData.selectedRowKeys = selectedRowKeys;
};


/* 获取进口合同列表  */
const formRef = ref(null);
const saveLoading = ref(false);
const handlerSave = () => {
  formRef.value
    .validate()
    .then(() => {
      saveLoading.value = true;
      addPushListInvoice({
        invoiceNo:formData.invoiceNo,
        purchaseList:originalData.value
      }).then(res => {
        if (res.code === 200) {
          message.success("保存成功！")
          handlerBack(true)
          emitEvent('refreshPurchaseListBox')
        }else {
          message.error(res.message)
        }
        saveLoading.value = false;
      })
    })
    .catch(error => {
      console.log('validate failed', error);
    })

}

const originalData = ref([])
watch(props.dataSourceList, (newVal, oldVal) => {
  let temp = deepClone(newVal)
  temp.forEach(item => {
    item.quantity = null
    item.invoiceNo = null
  })
  // 深拷贝newVal
  originalData.value = temp
},{immediate:true,deep:true})

// 表格显示列信息 选择框+合同编号+供应商
const showColumns = [
  {
    title: '商品名称',
    width: 200,
    align: 'center',
    dataIndex: 'productGrade',
    key: 'productGrade',
  },
  {
    title: '进口数量',
    width: 200,
    align: 'center',
    dataIndex: 'qty',
    key: 'qty',
  },
  {
    title: '单位',
    width: 200,
    align: 'center',
    dataIndex: 'unit',
    key: 'unit',
  },
  {
    title: '装箱数量（件数）',
    width: 200,
    align: 'center',
    dataIndex: 'quantity',
    key: 'quantity',
    editable: 'cellEditorSlot'
  },
]


/* 双击关闭行内编辑触发事件 */
const handleBlur = (save, closeEditor,column,record) => {
  // 这里不要做修改 逻辑直接西在下面写
  save();
  closeEditor();

};

const handleEnter = (column,save, closeEditor,record) => {
  // 这里不要做修改 逻辑直接西在下面写
  save();
  closeEditor();

  console.log('editorRef',record)
  if (column.dataIndex === 'quantity') {
    originalData.value.forEach(item => {
      if (item.sid === record.sid) {
        item.quantity  = record.quantity;
        console.log('item.quantity',item.quantity)
      }
    })
  }

  // updateBizErpIOrderList(record.sid,record).then(res => {
  //   if (res.code === 200) {
  //     message.success("修改成功！")
  //     getList()
  //
  //   }
  // })


};

onMounted(() => {

})

defineExpose({
  gridData
})

</script>

<style lang="less" scoped>

.header-search{
  margin: 10px 0;
}

/* 弹框表格样式 */
.cs-action-item-modal-table {
  padding: 4px 0;
  margin: 2px 0;
  box-sizing: border-box;
  min-height: calc(100vh);
  height: auto;
  .surely-table-body{
    min-height: calc(100vh);
  }
}
.cs-action-item-modal-table-empty{
  padding: 4px 0;
  margin: 2px 0;
  box-sizing: border-box;
  min-height: 500px;
  line-height: 500px;
}


</style>
