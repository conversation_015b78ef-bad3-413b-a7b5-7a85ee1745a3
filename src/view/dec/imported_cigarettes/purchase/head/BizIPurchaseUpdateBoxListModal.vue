<template>
  <section class="cs-card-form">
    <div>
      <s-table
        style="width: 100%;height: 100%;min-height: 100%;overflow-y: auto;overflow-x:auto"
        ref="tableRef"
        size="small"
        class="cs-action-item-modal-table remove-table-border-add-bg"
        bordered
        height="400px"
        :scroll="{ y: 400,x:400 }"
        :pagination="false"
        :columns="showColumns"
        :data-source="originalData"
        :row-selection="{  selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange,
         getCheckboxProps: (record) => ({
              disabled: true
         }),
        }"
        column-drag
        row-key="sid"
        :row-height="30"
      >
        <!-- 空数据 -->
        <template #emptyText>
          <a-empty description="暂无数据" />
        </template>

        <template #bodyCell="{text, record, index, column, key }">
          <template v-if="column.dataIndex === 'boxNo'">
            <a-input
              size="small"
              :placeholder="record.boxNo"
              v-model:value="originalData[index].boxNo"
              style="width: 100%;height: 24px"
            />
          </template>


          <template v-if="column.dataIndex === 'quantity'">
            <a-input-number
              size="small"
              v-model:value="originalData[index].quantity"
              style="width: 100%;height: 24px"
              :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
              :parser="value => inputParser(value)"
            />
          </template>
        </template>

      </s-table>
    </div>


    <div>
        <div style="display: flex;justify-content: right;align-items: center;">
          <a-button class="cs-margin-left"  size="small" @click="handlerBack(false)">取消</a-button>
          <a-button class="cs-margin-left" type="primary" size="small" @click="handlerSave" :loading="saveLoading">保存</a-button>
        </div>
    </div>

  </section>
</template>

<script setup>

import {addPushListBox, batchUpdateBoxList} from "@/api/cs_api_constant";
/* 进口合同列表 */
import {onMounted, reactive, ref, watch} from "vue";
import {message} from "ant-design-vue";
import {deepClone, isNullOrEmpty} from "@/view/utils/common";
import useEventBus from "@/view/common/eventBus";

import {useColumnsRender} from "@/view/common/useColumnsRender";
const { inputFormatter,inputParser}  = useColumnsRender()
 const { emitEvent } = useEventBus()



const props = defineProps({
  dataSourceList: {
    type: Array,
    default: () => []
  }
})

defineOptions({
  name: 'BizIPurchaseUpdateBoxListModal'
})

const emit = defineEmits(['cancel'])







/* 引入表单数据 */
const gridData = reactive({
  selectedRowKeys: [],
  selectedData:[],
  loading: false,
});

/* 关闭弹框 */
const handlerBack = (val) => {
  emit('cancel',val);
}



/* 表单信息 */
const formData = reactive({
  boxNo:''
})

const rules = {
  boxNo: [
    { required: false, message: '请输入箱号', trigger: 'blur' },
  ],
}


/* 选中事件(这里的事件，顺序不能乱，必须key,后面点跟实体，不然回显出问题。) */
const onSelectChange = (selectedRowKeys, rowSelectData) => {
  gridData.selectedData = rowSelectData;
  gridData.selectedRowKeys = selectedRowKeys;
};


/* 获取进口合同列表  */
const formRef = ref(null);
const saveLoading = ref(false);
const handlerSave = () => {
  saveLoading.value = true;
  batchUpdateBoxList(originalData.value).then(res => {
    if (res.code === 200) {
      message.success("保存成功！")
      handlerBack(true)
      emitEvent('refreshPurchaseListBox')
      emitEvent('refreshPurchaseList')
    }else {
      message.error(res.message)
    }
    saveLoading.value = false;
  }).finally(() => {
    saveLoading.value = false;
  })

}

const originalData = ref([])
watch(props.dataSourceList, (newVal, oldVal) => {
  // console.log('111111111111112props.dataSourceList', props.dataSourceList);
  let temp = deepClone(newVal)
  // temp.forEach(item => {
  //   item.sid = item.id
  // })
  // 深拷贝newVal
  originalData.value = temp
},{immediate:true,deep:true})

// 表格显示列信息 选择框+合同编号+供应商
const showColumns = [
  {
    title: '商品牌号',
    minWidth: 200,
    align: 'center',
    dataIndex: 'productGrade',
    key: 'productGrade',
    resizable: true
  },
  {
    title: '单位',
    minWidth: 200,
    align: 'center',
    dataIndex: 'unit',
    key: 'unit',
    resizable: true
  },
  {
    title: '数量',
    minWidth: 200,
    align: 'center',
    dataIndex: 'qty',
    key: 'qty',
    resizable: true
  },
  {
    title: '进口发票号',
    minWidth: 200,
    align: 'center',
    dataIndex: 'invoiceNo',
    key: 'invoiceNo',
    editable: 'cellEditorSlot',
    resizable: true
  },
  {
    title: '箱号',
    minWidth: 200,
    align: 'center',
    dataIndex: 'boxNo',
    key: 'boxNo',
    resizable: true
  },
  {
    title: '件数',
    minWidth: 200,
    align: 'center',
    dataIndex: 'quantity',
    key: 'quantity',
    resizable: true
  }
]


/* 双击关闭行内编辑触发事件 */
const handleBlur = (save, closeEditor,column,record) => {
  // 这里不要做修改 逻辑直接西在下面写
  save();
  closeEditor();

};

const handleEnter = (column,save, closeEditor,record) => {
  // 这里不要做修改 逻辑直接西在下面写
  save();
  closeEditor();

  console.log('editorRef',record)
  if (column.dataIndex === 'quantity') {
    originalData.value.forEach(item => {
      if (item.sid === record.sid) {
        item.quantity  = record.quantity;
        console.log('item.quantity',item.quantity)
      }
    })
  }

  // updateBizErpIOrderList(record.sid,record).then(res => {
  //   if (res.code === 200) {
  //     message.success("修改成功！")
  //     getList()
  //
  //   }
  // })


};

onMounted(() => {

})

defineExpose({
  gridData
})

</script>

<style lang="less" scoped>

.header-search{
  margin: 10px 0;
}

/* 弹框表格样式 */
.cs-action-item-modal-table {
  padding: 4px 0;
  margin: 2px 0;
  box-sizing: border-box;
  min-height: calc(100vh);
  height: auto;
  .surely-table-body{
    min-height: calc(100vh);
  }
}
.cs-action-item-modal-table-empty{
  padding: 4px 0;
  margin: 2px 0;
  box-sizing: border-box;
  min-height: 500px;
  line-height: 500px;
}


</style>
