 <template>
  <section  class="dc-section">
    <!-- 操作按钮区域 -->
    <div class="cs-action-btn">
      <div class="cs-action-btn-item" v-has="['yc-cs:importedCigarettes-order-purchase-box:delete']" v-if="!(props.operationStatus === editStatus.SHOW)">
        <a-button size="small"   @click="handlerDeleteBoxList"  :loading="deletePurchaseLoad" :disabled="props.disabledStatus">
          <template #icon>
            <GlobalIcon type="delete" style="color:#e93f41"/>
          </template>
          删除
        </a-button>
      </div>
      <div class="cs-action-btn-item" v-has="['yc-cs:importedCigarettes-order-purchase-box:update']" v-if="!(props.operationStatus === editStatus.SHOW)">
        <a-button size="small"  @click="handlerOpenUpdateBoxList"  :disabled="props.disabledStatus">
          <template #icon>
            <GlobalIcon type="form" style="color:deepskyblue"/>
          </template>
          修改箱号
        </a-button>
      </div>
    </div>



      <!-- 表格区域 -->
      <div class="cs-action" >
        <s-table
          ref="tableRef"
          class="cs-action-item-modal-table remove-table-border-add-bg"
          size="small"
          :height="430"
          bordered
          :pagination="false"
          :columns="totalColumns"
          :data-source="dataSourceList"
          :row-selection="{  selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          row-key="sid"
          column-drag
          :row-height="30"
          :range-selection="false"
        >
          <!-- 操作 -->
          <template #bodyCell="{ column,record }">
            <template v-if="column.key === 'operation'">
              <div class="operation-container">
                <a-button
                  size="small"
                  type="link"
                  @click="handleEditByRow(record)"
                  :style="operationEdit('edit')"
                >
                  编辑
                </a-button>
                <a-button
                  size="small"
                  type="link"
                  @click="handleViewByRow(record)"
                  :style="operationEdit('view')"
                >
                  查看
                </a-button>

              </div>
            </template>
          </template>
          <!-- 空数据 -->
          <template #emptyText v-if="!tableLoading">
            <a-empty description="暂无数据" />
          </template>
        </s-table>
      </div>

      <!-- 分页 总数量：{{sumData.qty}}  -->
      <div class=cs-pagination>
        <div class="cs-margin-right cs-list-total-data ">
          总件数：{{formatSpecifiedNumber(sumData.quantity,true,2)}}
        </div>
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer  :page-size="page.pageSize" :total="page.total"  @change="onPageChange">
          <template #buildOptionText="props">
            <span >{{ props.value }}条/页</span>
          </template>
        </a-pagination>

      </div>


        <!-- 修改装箱子表 -->
      <cs-modal :visible="isShowUpdateBoxList" :title="'修改箱号'" :width="1500" :footer="false" @cancel="handlerCloseUpdateBoxList">
        <template #customContent>
          <biz-i-purchase-update-box-list-modal ref="updateBoxListRef" :data-source-list="selectBoxList" @cancel="handlerCloseUpdateBoxList"></biz-i-purchase-update-box-list-modal>
        </template>
      </cs-modal>

  </section>


</template>

<script setup>
/* 使用自定义 Hook 函数 */
import {useCommon} from '@/view/common/useCommon'
import {createVNode, nextTick, onMounted, provide, reactive, ref, watch} from "vue";
import {getColumns} from "@/view/dec/imported_cigarettes/purchase/box/BizIPurchaseListBoxColumns";
const { totalColumns } = getColumns()
import ycCsApi from "@/api/ycCsApi";
import useEventBus from '@/view/common/eventBus';
import {deletePurchaseList, getBizIPurchaseListBoxSumData} from "@/api/cs_api_constant";
import {isNullOrEmpty} from "@/view/utils/common";
import {message, Modal} from "ant-design-vue";
import {editStatus} from "@/view/common/constant";
import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
import CsModal from "@/components/modal/cs-modal.vue";
import BizIPurchaseUpdateBoxModal from "@/view/dec/imported_cigarettes/purchase/head/BizIPurchaseUpdateBoxModal.vue";
import BizIPurchaseUpdateBoxListModal
  from "@/view/dec/imported_cigarettes/purchase/head/BizIPurchaseUpdateBoxListModal.vue";


/* 引入通用方法 */
const {
  editConfig,
  show,
  page,
  handleEditByRow,
  handleViewByRow,
  operationEdit,
  dataSourceList,
  tableLoading,
  getTableScroll,
  ajaxUrl,
} = useCommon()

/* 引入bus  */
const { onEvent ,emitEvent  } = useEventBus();

import {useColumnsRender} from "@/view/common/useColumnsRender";
const { formatNumber,formatSpecifiedNumber }  = useColumnsRender()

defineOptions({
  name: 'BizIPurchaseListBoxList',
});


const props = defineProps({
  headId: {
    type: String,
    default: ''
  },
  operationStatus:{
    type: String,
    default: ''
  },
  disabledStatus:{
    type: Boolean,
    default: false
  }
})


/* 查询数据 */
const getList = () => {
  console.log('执行查询')
  tableLoading.value = true
  window.majesty.httpUtil.postAction(`${ycCsApi.bizIPurchaseListBox.list}?page=${page.current}&limit=${page.pageSize}`,
    {headId:props.headId}
  ).then(res => {
    dataSourceList.value = res.data
    page.total = res.total
    // 获取汇总数据
    setTimeout(()=>{
      getSumData()
    },100)
  }).finally(() => {
    tableLoading.value = false
  })
}

const onPageChange = (pageNumber, pageSize)=> {
  page.current = pageNumber
  page.pageSize = pageSize
  // 在这里添加处理页码变化的逻辑
  getList()
}



defineExpose({
  getList
})




const sumData = reactive({
  qty:0,
  quantity:0
})
/* 获取装箱子表汇总数据 */
const getSumData = async () =>{
  if (isNullOrEmpty(props.headId)){
    return
  }
  const res = await getBizIPurchaseListBoxSumData({headId:props.headId})
  if (res.code === 200) {
    Object.assign(sumData,res.data)
  }else {
    message.error(res.message)
  }
}

onMounted(fn => {

  tableHeight.value = getTableScroll(730,'');
  nextTick(()=>{
    getList()
  })

})

const tableHeight = ref('')

/* 引入表单数据 */
const gridData = reactive({
  selectedRowKeys: [],
  selectedData:[],
  loading: false,
});



/* 选中事件(这里的事件，顺序不能乱，必须key,后面点跟实体，不然回显出问题。) */
const onSelectChange = (selectedRowKeys, rowSelectData) => {
  gridData.selectedData = rowSelectData;
  gridData.selectedRowKeys = selectedRowKeys;
};


/* 接收bus事件 进行刷新 */
onEvent('refreshPurchaseListBox', () => {
  getList()
})



/* 删除装箱子表数据 */
const deletePurchaseLoad = ref(false)
const handlerDeleteBoxList = ()=>{
  if (gridData.selectedRowKeys.length === 0) {
    message.warning("请选择要删除的数据！")
    return
  }
  // 弹出确认框
  Modal.confirm({
    size: 'small',
    title: '提醒',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '删除',
    cancelText: '取消',
    content: '确定要删除装箱子表吗？',
    okButtonProps: {
      size: 'small'
    },
    cancelButtonProps: {
      size:'small',
    },
    onOk() {
      deletePurchaseLoad.value = true
      /* 删除进货信息表体，并且删除对应的装箱子表 */
      deletePurchaseList({sids:gridData.selectedRowKeys}).then(res=>{
        if (res.code === 200) {
          message.success("删除成功！")
          getList()

          emitEvent('refreshPurchaseList')
        }else {
          message.error(res.message)
        }
      }).finally(()=>{
        deletePurchaseLoad.value = false
      })
    },
    onCancel() {
      // 取消操作
    },
  });


}




// 打开修改箱号弹框
const isShowUpdateBoxList = ref(false)

// 选择装箱子表问题
const selectBoxList = ref([])

const handlerOpenUpdateBoxList = ()=>{

  // 判断是否选择了数据
  if (gridData.selectedRowKeys.length === 0) {
    message.warning("请选择要修改箱号的数据！")
    return
  }


  isShowUpdateBoxList.value = true

  // 从选择的数据中区中过滤出箱号
  let tempBox = gridData.selectedData.map(item=>{
    return {
      boxNo:item.boxNo,
    }
  })

  // 从dataSourceList中过滤出箱号一致的数据
  let tempData = dataSourceList.value.filter(item=>{
    return tempBox.some(box=>{
      return box.boxNo === item.boxNo
    })
  })

  selectBoxList.value = tempData

}
const handlerCloseUpdateBoxList = ()=>{
  isShowUpdateBoxList.value = false
  // 清空选择的数据
  gridData.selectedRowKeys = []
  gridData.selectedData = []
  getList()
}










</script>

<style lang="less" scoped>
/* 弹框表格样式 */
.cs-action-item-modal-table {
  padding: 4px 0;
  margin: 2px 0;
  background: #fff;
  box-sizing: border-box; /* 确保 padding 不会撑大容器 */
  // min-height: calc(100vh - 300px);
  min-height: calc(40vh);
  height: auto;
  .surely-table-body{
    // min-height: calc(100vh - 300px);
    min-height: calc(40vh);
  }
}

</style>
