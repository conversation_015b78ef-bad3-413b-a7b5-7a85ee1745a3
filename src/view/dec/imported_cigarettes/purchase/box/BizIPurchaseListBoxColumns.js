import {baseColumns} from "@/view/common/baseColumns";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {h, reactive, ref} from "vue";
import {productClassify} from "@/view/common/constant";
import {Tag} from "ant-design-vue";
const {baseColumnsExport, baseColumnsShow} = baseColumns()
const { cmbShowRender,formatNumber,formatSpecifiedNumber } = useColumnsRender()




export function getColumns() {

  const commColumns = reactive([
      'sid',
    'insertUser',
    'insertTime',
    'insertUserName',
    'updateUser',
    'updateTime',
    'updateUserName',
    'tradeCode',
    'productGrade',
    'unit',
    'qty',
    'curr',
    'decPrice',
    'decTotal',
    'productType',
    'headId',
    'discountRate',
    'discountAmount',
    'paymentAmount',
    'invoiceNo',
    'boxNo',
    'quantity',
    'versionNo',
    'dataStatus',
    'extend1',
    'extend2',
    'extend3',
    'extend4',
    'extend5',
    'extend6',
    'extend7',
    'extend8',
    'extend9',
    'extend10',
    'listHeadSid',
  ])

  /* 导出字段设置 */
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])

  /* table表格字段设置 */
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  /* table表格字段属性设置 */
  const totalColumns = ref([
    {
      title: '商品牌号',
      minWidth: 200,
      align: 'center',
      dataIndex: 'productGrade',
      key: 'productGrade',
      resizable: true
    },
    {
      title: '单位',
      minWidth: 200,
      align: 'center',
      dataIndex: 'unit',
      key: 'unit',
      resizable: true
    },
    {
      title: '数量',
      minWidth: 200,
      align: 'center',
      dataIndex: 'qty',
      key: 'qty',
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '进口发票号',
      minWidth: 200,
      align: 'center',
      dataIndex: 'invoiceNo',
      key: 'invoiceNo',
      editable: 'cellEditorSlot',
      resizable: true
    },
    {
      title: '箱号',
      minWidth: 200,
      align: 'center',
      dataIndex: 'boxNo',
      key: 'boxNo',
      resizable: true
    },
    {
      title: '件数',
      minWidth: 200,
      align: 'center',
      dataIndex: 'quantity',
      key: 'quantity',
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    }
  ])

  return{
    columnsConfig,
    excelColumnsConfig,
    totalColumns
  }
}
