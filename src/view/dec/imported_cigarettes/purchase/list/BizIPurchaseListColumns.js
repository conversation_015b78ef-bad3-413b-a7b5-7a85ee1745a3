import {baseColumns} from "@/view/common/baseColumns";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {h, reactive, ref} from "vue";
import {productClassify} from "@/view/common/constant";
import {Tag} from "ant-design-vue";
import {isNullOrEmpty} from "@/view/utils/common";
const {baseColumnsExport, baseColumnsShow} = baseColumns()
const { cmbShowRender ,formatNumber,formatSpecifiedNumber} = useColumnsRender()




export function getColumns() {

  const commColumns = reactive([
      'sid',
    'insertUser',
    'insertTime',
    'insertUserName',
    'updateUser',
    'updateTime',
    'updateUserName',
    'tradeCode',
    'productGrade',
    'unit',
    'qty',
    'curr',
    'decPrice',
    'decTotal',
    'productType',
    'headId',
    'discountRate',
    'discountAmount',
    'paymentAmount',
    'invoiceNo',
    'boxNo',
    'boxNoTemp',
    'quantity',
    'quantityTemp',
    'versionNo',
    'dataStatus',
    'extend1',
    'extend2',
    'extend3',
    'extend4',
    'extend5',
    'extend6',
    'extend7',
    'extend8',
    'extend9',
    'extend10'
  ])

  /* 导出字段设置 */
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])

  /* table表格字段设置 */
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  /* table表格字段属性设置 */
  const totalColumns = ref([
    // {
    //   width: 150,
    //   title: '操作',
    //   dataIndex: 'operation',
    //   key: 'operation',
    //   align: 'center',
    //   fixed: 'left',
    // },
    {
      title: '商品名称',
      minWidth: 250,
      align: 'center',
      dataIndex: 'productGrade',
      key: 'productGrade',
      resizable: true
    },
    {
      title: '单位',
      width: 120,
      align: 'center',
      dataIndex: 'unit',
      key: 'unit',
      resizable: true
    },
    {
      title: '数量',
      width: 120,
      align: 'center',
      dataIndex: 'qty',
      key: 'qty',
      editable: 'cellEditorSlot',
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '币种',
      width: 120,
      align: 'center',
      dataIndex: 'curr',
      key: 'curr',
      resizable: true,
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,[],'CURR_OUTDATED'))
      }
    },
    {
      title: '单价',
      width: 120,
      align: 'center',
      dataIndex: 'decPrice',
      key: 'decPrice',
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '总值',
      width: 120,
      align: 'center',
      dataIndex: 'decTotal',
      key: 'decTotal',
      resizable: true,
      editable: 'cellEditorSlot',
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '进口发票号',
      minWidth: 200,
      align: 'center',
      dataIndex: 'invoiceNo',
      key: 'invoiceNo',
      editable: 'cellEditorSlot',
      resizable: true
    },
    {
      title: '折扣率%',
      width: 120,
      align: 'center',
      dataIndex: 'discountRate',
      key: 'discountRate',
      customRender: ({ text }) => {
        if(!isNullOrEmpty(text)){
          return h(<div></div>, text+'%')
        }
      },
      resizable: true
    },
    {
      title: '折扣金额',
      width: 120,
      align: 'center',
      dataIndex: 'discountAmount',
      key: 'discountAmount',
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '货款金额',
      width: 120,
      align: 'center',
      dataIndex: 'paymentAmount',
      key: 'paymentAmount',
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },

    {
      title: '箱号',
      minWidth: 200,
      align: 'center',
      dataIndex: 'boxNo',
      key: 'boxNo',
      // editable: 'cellEditorSlot'
      ellipsis: { showTitle: true },
      resizable: true,
    },
    {
      title: '装箱数量（件数）',
      width: 200,
      align: 'center',
      dataIndex: 'quantity',
      key: 'quantity',
      // editable: 'cellEditorSlot'
      resizable: true,
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    }
  ])

  return{
    columnsConfig,
    excelColumnsConfig,
    totalColumns
  }
}
