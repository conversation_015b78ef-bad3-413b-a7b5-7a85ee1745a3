 <template>
  <section  class="dc-section">

    <div class="cs-action"  v-show="show">

        <s-table
          ref="tableRef"
          class="cs-action-item-modal-table remove-table-border-add-bg"
          size="small"
          :heigh="530"
          column-drag
          bordered
          :pagination="false"
          :columns="totalColumns"
          :data-source="dataSourceList"
          :row-selection="{  selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          row-key="sid"
          :row-height="30"
          :range-selection="false"
        >
          <!-- 空数据 -->
          <template #emptyText>
            <a-empty description="暂无数据" />
          </template>

          <!-- 行内编辑 -->
          <template #bodyCell="{text, record, index, column, key }">
            <template v-if=" (!(editStatus.SHOW === props.operationStatus)  &&   props.isEdit === '0')  && column.dataIndex === 'qty'">
              <a-input-number
                size="small"
                v-model:value="dataSourceList[index].qty"
                style="width: 100%;height: 24px"
                :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                :parser="value => inputParser(value)"
                @blur="() => {
                  handleQuantityChange(record,column);
                }"
                @keydown.enter="() => {
                  handleQuantityChange(record,column);
                }"
              />
            </template>
            <template v-if="(!(editStatus.SHOW === props.operationStatus)  &&   props.isEdit === '0')  && column.dataIndex === 'decTotal'">
              <a-input-number
                size="small"
                v-model:value="dataSourceList[index].decTotal"
                style="width: 100%;height: 24px"
                :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                :parser="value => inputParser(value)"
                @blur="() => {
                  handleQuantityChange(record,column);
                }"
                @keydown.enter="() => {
                  handleQuantityChange(record,column);
                }"
              />
            </template>
            <template v-if="(!(editStatus.SHOW === props.operationStatus)  &&   props.isEdit === '0')  && column.dataIndex === 'invoiceNo'">
              <a-input
                size="small"
                v-model:value="dataSourceList[index].invoiceNo"
                style="width: 100%;height: 24px"
                @blur="() => {
                  handleQuantityChange(record,column);
                }"
                @keydown.enter="() => {
                  handleQuantityChange(record,column);
                }"
              />
            </template>
          </template>
        </s-table>
      </div>

      <!-- 分页 -->
      <div class=cs-pagination>
        <div class="cs-margin-right cs-list-total-data ">
          总数量：{{formatSpecifiedNumber(sumData.qty,true,2)}} ，总金额：{{formatSpecifiedNumber(sumData.decTotal,true,2)}}，总折扣金额：{{formatSpecifiedNumber(sumData.discountAmount,true,2)}}，总货款金额：{{formatSpecifiedNumber(sumData.paymentAmount,true,2)}}
        </div>
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer  :page-size="page.pageSize" :total="page.total"  @change="onPageChange">
          <template #buildOptionText="props">
            <span >{{ props.value }}条/页</span>
          </template>
        </a-pagination>

      </div>
  </section>


</template>

<script setup>
/* 使用自定义 Hook 函数 */
import {useCommon} from '@/view/common/useCommon'
import {createVNode, nextTick, onMounted, provide, reactive, ref, watch} from "vue"
import {getColumns} from "@/view/dec/imported_cigarettes/purchase/list/BizIPurchaseListColumns"
const { totalColumns } = getColumns()
import ycCsApi from "@/api/ycCsApi"
import {
  deletePurchaseList,
  deletePurchaseListBox,
  getBizIPurchaseListSumData, getOrderListBySid, getPurchaseListBySid, innerDecTotalUpdatePurchaseList,
  innerUpdatePurchaseList,
  innerUpdatePurchaseListInvoiceNo,
  updateBizErpIOrderList
} from "@/api/cs_api_constant";
import {message, Modal} from "ant-design-vue";
import useEventBus from '@/view/common/eventBus';
import {isNullOrEmpty} from "@/view/utils/common";
import {editStatus} from "@/view/common/constant";
import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
import {confirmContract} from "@/api/payment/payment_info";
import {useColumnsRender} from "@/view/common/useColumnsRender";
const { inputFormatter,inputParser,formatNumber,formatSpecifiedNumber}  = useColumnsRender()

const props =  defineProps({
  /* 进口信息表头sid */
  headId:{
    type:String,
    default:()=>''
  },
  /* 查看状态 */
  operationStatus:{
    type:Boolean,
    default:()=>''
  },
  /* 是否可编辑 */
  isEdit:{
    type:String,
    default:()=>''
  },
})

defineOptions({
  name:'BizIPurchaseListList'
})



/* 引入通用方法 */
const {
  editConfig,
  show,
  page,
  handleEditByRow,
  handleViewByRow,
  operationEdit,
  dataSourceList,
  tableLoading,
  getTableScroll,
  ajaxUrl,

} = useCommon()

/* 引入bus */
const {emitEvent,onEvent} = useEventBus()



const sourceData = ref([])

/* 查询数据 */
const getList = () => {
  tableLoading.value = true
  console.log('props', props)
  window.majesty.httpUtil.postAction(`${ycCsApi.bizIPurchaseList.list}?page=${page.current}&limit=${page.pageSize}`,
    {headId:props.headId}
  ).then(res => {
    dataSourceList.value = res.data
    Object.assign(sourceData.value, res.data)
    page.total = res.total
    // 获取汇总数据
    setTimeout(()=>{
      getSumData()
    },100)
  }).finally(() => {
    tableLoading.value = false
  })
}



/* 进货信息汇总数据 */
const sumData = reactive({
  qty:0,
  decTotal:0,
  discountAmount:0,
  paymentAmount:0,
})
/* 获取进货信息表体汇总数据 */
const getSumData = async ()=>{
  if (isNullOrEmpty(props.headId)){
    console.log('headId',props.headId)
    return
  }
  const res = await getBizIPurchaseListSumData({headId:props.headId})
  if (res.code === 200) {
    Object.assign(sumData,res.data)
  }else {
    message.error(res.message)
  }

}

const onPageChange = (pageNumber, pageSize)=> {
  page.current = pageNumber
  page.pageSize = pageSize
  // 在这里添加处理页码变化的逻辑
  getList()
}

const tableHeight = ref('')
onMounted(fn => {

  // 535
  tableHeight.value = getTableScroll(730,'');
  nextTick(() => {
    getList()
  })

})



/* 引入表单数据 */
const gridData = reactive({
  selectedRowKeys: [],
  selectedData:[],
  loading: false,
});


/* 选中事件(这里的事件，顺序不能乱，必须key,后面点跟实体，不然回显出问题。) */
const onSelectChange = (selectedRowKeys, rowSelectData) => {
  gridData.selectedData = rowSelectData;
  gridData.selectedRowKeys = selectedRowKeys;
};




/* 定义自定义编辑列属性 */
const cellEditorColumn = ref([
  'qty',
  'invoiceNo',
  'boxNo',
  'quantity',
])

/* 双击关闭行内编辑触发事件 */
const handleBlur = (save, closeEditor) => {
  // 这里不要做修改 逻辑直接西在下面写
  save();
  closeEditor();
  getList()
};



const handleEnter = (column,save, closeEditor,record) => {
  // 这里不要做修改 逻辑直接西在下面写
  save();
  closeEditor();

  console.log('editorRef',record)
  if (column.dataIndex === 'qty') {

    // 更新数量
    innerUpdatePurchaseList(record).then(res=>{
      if (res.code === 200) {
        message.success("修改成功！")
      }else {
        message.error(res.message)
      }
    }).finally(()=>{
      getList()
    })
  }else if ( column.dataIndex === 'decTotal') {
    // 更新总价值
    innerDecTotalUpdatePurchaseList(record).then(res=>{
      if (res.code === 200) {
        message.success("修改成功！")
      }else {
        message.error(res.message)
      }
    }).finally(()=>{
      getList()
    })
} else if (column.dataIndex === 'invoiceNo') {
    // 更新进口发票号
    innerUpdatePurchaseListInvoiceNo(record).then(res=>{
      // console.log('ress',res)
      if (res.code === 200) {
        message.success("修改成功！")
        emitEvent('refreshPurchaseListBox')
      }else {
        message.error(res.message)
      }
    }).finally(()=>{
      getList()
    })
  }

};

const isEditLoading = ref(false)
// 处理数量变更
const handleQuantityChange = (record,column) => {
  console.log('record',record,'column',column)
  if (isEditLoading.value === true){
    console.log('回车，失焦同时触发！')
    return
  }
  isEditLoading.value = true
  if (record && record.sid) {
    console.log('column.dataIndex',column.dataIndex)
    getPurchaseListBySid(record.sid, record).then(res => {
      if (res.code === 200) {
        let dataTemp = res.data
        // 比较数量
        if (column.dataIndex === 'qty') {
          // 判断值是否发生变化 qty
          if (isNullOrEmpty(res.data) || res.data.qty === record.qty) {
            return
          }
          // 更新数量
          innerUpdatePurchaseList(record).then(res => {
            if (res.code === 200) {
              message.success("修改成功！")
            } else {
              // 恢复原先的值
              record.qty = dataTemp.qty
              message.error(res.message)
            }
          }).finally(() => {
            getSumData()
          })

        }else if ( column.dataIndex === 'decTotal'){
          // 判断值是否发生变化 qty
          if (isNullOrEmpty(res.data) || res.data.decTotal === record.decTotal) {
            return
          }
          // 更新总价值
          innerDecTotalUpdatePurchaseList(record).then(res=>{
            if (res.code === 200) {
              message.success("修改成功！")
            }else {
              // 恢复原先的值
              record.decTotal = dataTemp.decTotal
              message.error(res.message)
            }
          }).finally(()=>{
            getSumData()
          })
        }else if (column.dataIndex === 'invoiceNo'){
          // 判断值是否发生变化 qty
          if (isNullOrEmpty(res.data) || res.data.invoiceNo === record.invoiceNo) {
            return
          }
          // 更新发票号
          innerUpdatePurchaseListInvoiceNo(record).then(res=>{
            // console.log('ress',res)
            if (res.code === 200) {
              message.success("修改成功！")
              emitEvent('refreshPurchaseListBox')
            }else {
              record.invoiceNo = dataTemp.invoiceNo
              message.error(res.message)
            }
          }).finally(()=>{
            getSumData()
          })
        }
      }
    })
  }
  setTimeout(() => {
    isEditLoading.value = false
  }, 100)
}





/* 接收bus事件 进行刷新 */
onEvent('refreshPurchaseList', () => {

  getList()
})






/* 将gridData */
defineExpose({gridData,getList,dataSourceList,isEditLoading})

</script>

<style lang="less" scoped>
/* 弹框表格样式 */
.cs-action-item-modal-table {
  padding: 4px 0;
  margin: 2px 0;
  background: #fff;
  box-sizing: border-box; /* 确保 padding 不会撑大容器 */
  // min-height: calc(100vh - 300px);
  min-height: calc(40vh);
  height: auto;
  .surely-table-body{
    // min-height: calc(100vh - 300px);
    min-height: calc(40vh);
  }
}


</style>
