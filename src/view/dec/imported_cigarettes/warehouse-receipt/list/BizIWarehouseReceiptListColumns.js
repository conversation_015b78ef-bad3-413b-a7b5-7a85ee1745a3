import {baseColumns} from "@/view/common/baseColumns";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {h, reactive, ref} from "vue";
import {productClassify} from "@/view/common/constant";
import {Tag} from "ant-design-vue";
const {baseColumnsExport, baseColumnsShow} = baseColumns()
const { formatNumber,formatSpecifiedNumber }  = useColumnsRender()



export function getColumns() {

  const commColumns = reactive([
    'sid',
    'qty',
    'unit',
    'invoiceNumber',
    'foreignUnitPrice',
    'rmbUnitPrice',
    'foreignPrices',
    'rmbPrices',
    'tariff',

    'consumptionTax',
    'valueAddedTax',
    'producAmount',
    'taxAmount',
    'costAmount',
    'totalAmount'

  ])

  /* 导出字段设置 */
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])

  /* table表格字段设置 */
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  const testAdd = (e) =>{
    console.log('新增了')
  }
  /* table表格字段属性设置 */
  const totalColumns = ref([
    {
      title: '商品名称',
      width: 200,
      align: 'center',
      dataIndex: 'goodsName',
      key: 'goodsName'
    },
    {
      title: '数量',
      width: 200,
      align: 'center',
      dataIndex: 'qty',
      key: 'qty',
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '单位',
      width: 200,
      align: 'center',
      dataIndex: 'unit',
      key: 'unit',
    },
    {
      title: '进口发票号码',
      width: 200,
      align: 'center',
      dataIndex: 'invoiceNumber',
      key: 'invoiceNumber',
    },
    {
      title: '外币单价',
      width: 200,
      align: 'center',
      dataIndex: 'foreignUnitPrice',
      key: 'foreignUnitPrice',
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }

    },
    {
      title: '人民币单价',
      width: 200,
      align: 'center',
      dataIndex: 'rmbUnitPrice',
      key: 'rmbUnitPrice',
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '外币货价',
      width: 200,
      align: 'center',
      dataIndex: 'foreignPrices',
      key: 'foreignPrices',
      editable: 'cellEditorSlot',
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '人民币货价',
      width: 200,
      align: 'center',
      dataIndex: 'rmbPrices',
      key: 'rmbPrices',
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '关税',
      width: 200,
      align: 'center',
      dataIndex: 'tariff',
      key: 'tariff',
      editable: 'cellEditorSlot',
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '消费税',
      width: 200,
      align: 'center',
      dataIndex: 'consumptionTax',
      key: 'consumptionTax',
      editable: 'cellEditorSlot',
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '增值税',
      width: 200,
      align: 'center',
      dataIndex: 'valueAddedTax',
      key: 'valueAddedTax',
      editable: 'cellEditorSlot',
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '商品金额小计',
      width: 200,
      align: 'center',
      dataIndex: 'producAmount',
      key: 'producAmount',
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '税金金额小计',
      width: 200,
      align: 'center',
      dataIndex: 'taxAmount',
      key: 'taxAmount',
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '成本金额小计',
      width: 200,
      align: 'center',
      dataIndex: 'costAmount',
      key: 'costAmount',
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    },
    {
      title: '合计金额',
      width: 200,
      align: 'center',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      customRender: ({ text }) => {
        return formatSpecifiedNumber(text,true,2)
      }
    }
  ])

  return{
    columnsConfig,
    excelColumnsConfig,
    totalColumns
  }
}
