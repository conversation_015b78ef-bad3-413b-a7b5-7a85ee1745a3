 <template>
  <section  class="dc-section">
    <div class="cs-action"  v-show="show">

      <!-- 表格区域 -->
      <div>
        <s-table
          ref="tableRef"
          class="cs-action-item-modal-table remove-table-border-add-bg"
          size="small"
          :height="530"
          bordered
          :pagination="false"
          :columns="totalColumns"
          :data-source="dataSourceList"
          :row-selection="{  selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          row-key="sid"
          :row-height="30"
        >
          <!-- 看下面注释 非查看状态且为0 可以编辑-->
<!--          <template v-if=" (!(editStatus.SHOW === props.operationStatus)  &&   props.status === '0') "  #cellEditor="{ column, modelValue, save, closeEditor, editorRef, getPopupContainer,record,recordIndex }">-->
<!--            <template v-if="column.dataIndex === 'foreignPrices' || column.dataIndex === 'tariff'|| column.dataIndex === 'consumptionTax'-->
<!--             || column.dataIndex === 'valueAddedTax'">-->
<!--              <a-input-number-->
<!--                :ref="editorRef"-->
<!--                size="small"-->
<!--                v-model:value="modelValue.value"-->
<!--                style="width: 100%"-->
<!--                :disabled="showDisable"-->
<!--              />-->
<!--            </template>-->
<!--          </template>-->
          <template #bodyCell="{text, record, index, column, key }">
            <template v-if=" (!(editStatus.SHOW === props.operationStatus)  &&   props.status === '0')  && column.dataIndex === 'foreignPrices'">
              <a-input-number
                size="small"
                v-model:value="dataSourceList[index].foreignPrices"
                style="width: 100%;height: 24px"
                :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                :parser="value => inputParser(value)"
                @blur="() => {
                  handleQuantityChange(record,column);
                }"
                @keydown.enter="() => {
                  handleQuantityChange(record,column);
                }"
              />
            </template>

            <template v-if=" (!(editStatus.SHOW === props.operationStatus)  &&   props.status === '0')  && column.dataIndex === 'tariff'">
              <a-input-number
                size="small"
                v-model:value="dataSourceList[index].tariff"
                style="width: 100%;height: 24px"
                :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                :parser="value => inputParser(value)"
                @blur="() => {
                  handleQuantityChange(record,column);
                }"
                @keydown.enter="() => {
                  handleQuantityChange(record,column);
                }"
              />
            </template>


            <template v-if=" (!(editStatus.SHOW === props.operationStatus)  &&   props.status === '0')  && column.dataIndex === 'consumptionTax'">
              <a-input-number
                size="small"
                v-model:value="dataSourceList[index].consumptionTax"
                style="width: 100%;height: 24px"
                :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                :parser="value => inputParser(value)"
                @blur="() => {
                  handleQuantityChange(record,column);
                }"
                @keydown.enter="() => {
                  handleQuantityChange(record,column);
                }"

              />
            </template>


            <template v-if=" (!(editStatus.SHOW === props.operationStatus)  &&   props.status === '0')  && column.dataIndex === 'valueAddedTax'">
              <a-input-number
                size="small"
                v-model:value="dataSourceList[index].valueAddedTax"
                style="width: 100%;height: 24px"
                :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                :parser="value => inputParser(value)"
                @blur="() => {
                  handleQuantityChange(record,column);
                }"
                @keydown.enter="() => {
                  handleQuantityChange(record,column);
                }"
              />
            </template>
          </template>
        </s-table>
      </div>

      <!-- 分页 -->
      <div class=cs-pagination>
        <div class="cs-margin-right cs-list-total-data ">
          总外币货价：{{formatSpecifiedNumber(sumData.foreignPrices,true,2)}} ，总人民币货价：{{formatSpecifiedNumber(sumData.rmbPrices,true,2)}}，总关税：{{formatSpecifiedNumber(sumData.tariff,true,2)}}，总消费税：{{formatSpecifiedNumber(sumData.consumptionTax,true,2)}}，
          总增值税：{{formatSpecifiedNumber(sumData.valueAddedTax,true,2)}}，总税金金额小计：{{formatSpecifiedNumber(sumData.taxAmount,true,2)}}，总成本金额小计：{{formatSpecifiedNumber(sumData.costAmount,true,2)}}，总合计金额：{{formatSpecifiedNumber(sumData.totalAmount,true,2)}}

        </div>
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer  :page-size="page.pageSize" :total="page.total"  @change="onPageChange">
          <template #buildOptionText="props">
            <span >{{ props.value }}条/页</span>
          </template>
        </a-pagination>

      </div>
    </div>


  </section>


</template>

<script setup>

import {useCommon} from '@/view/common/useCommon'
import {createVNode, onMounted, provide, reactive, ref} from "vue";
import {message, Modal} from "ant-design-vue";
import {localeContent} from "@/view/utils/commonUtil";
import {getColumns} from "@/view/dec/imported_cigarettes/warehouse-receipt/list/BizIWarehouseReceiptListColumns";
import ycCsApi from "@/api/ycCsApi";
import {editStatus} from "@/view/common/constant";
import {
  getIWarehouseReceiptListSumData,
  getIWarehouseReceiptListUpdate,
  getPurchaseListBySid,
  getWarehouseReceiptListBySid,
  innerDecTotalUpdatePurchaseList,
  innerUpdatePurchaseList,
  innerUpdatePurchaseListInvoiceNo
} from "@/api/cs_api_constant";
import {deepClone, isNullOrEmpty} from "@/view/utils/common";
import {FormItem} from "view-ui-plus";
const { totalColumns } = getColumns()
import useEventBus from '@/view/common/eventBus';

import {useColumnsRender} from "@/view/common/useColumnsRender";
const { inputFormatter,inputParser,formatNumber,formatSpecifiedNumber}  = useColumnsRender()
/* 引入bus  */
const { onEvent } = useEventBus();


/* 引入通用方法 */
const {
  editConfig,
  show,
  page,
  dataSourceList,
  tableLoading,
  getTableScroll,
  doExport,
} = useCommon()



defineOptions({
  name: 'BizIReceiptListList',
});


const props = defineProps({
  /* 表头传入状态 查看/编辑 */
  operationStatus: {
    type: String,
    default: ''
  },
  headId:{
    type:String,
    default:()=>''
  },

  status:{
    type:String,
    default:()=>''
  },

  editConfig: {
    type: Object,
    default: () => {
    }
  },
})


const editableData  = reactive({});
// 是否禁用
const showDisable = ref(false)

/* 定义汇总数据 */
const totalData = ref({
  qtyTotal:0,
  decTotal:0
})

const edit = (key) => {
  console.log('key',key)
  editableData[key] = true;
};


const entry = (key) => {
  console.log('key',key)

};

const save = (key) => {
  console.log('key',key)
  delete editableData[key];
};

/* 进货信息汇总数据 */
const sumData = reactive({
  foreignPrices:0,
  rmbPrices:0,
  tariff:0,
  consumptionTax:0,

  valueAddedTax:0,
  taxAmount:0,
  costAmount:0,
  totalAmount:0,
})
/* 获取进货信息表体汇总数据 */
const getSumData = async ()=>{
  if (isNullOrEmpty(props.headId)){
    return
  }
  const res = await getIWarehouseReceiptListSumData({parentId:props.headId})
  if (res.code === 200) {
    Object.assign(sumData,res.data)
  }

}
/* 查询数据 */
const getList = () => {
  tableLoading.value = true
  window.majesty.httpUtil.postAction(`${ycCsApi.bizIWarehouseReceiptList.selectList}?page=${page.current}&limit=${page.pageSize}`,
    {parentId:props.headId}
  ).then(res => {
    dataSourceList.value = res.data
    // console.log('获取数据',res.data[0].foreignPrices)
    page.total = res.total
    // 获取汇总数据
    getSumData()
  }).finally(() => {
    tableLoading.value = false
  })
}
const onPageChange = (pageNumber, pageSize)=> {
  page.current = pageNumber
  page.pageSize = pageSize
  // 在这里添加处理页码变化的逻辑
  getList()
}


onMounted(fn => {
  tableHeight.value = getTableScroll(100,'');
  getList()

  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    showDisable.value = true
  }
  if (props.status=='1'||props.status=='2'){
    showDisable.value = true
  }

})

const tableHeight = ref('')

/* 引入表单数据 */
const gridData = reactive({
  selectedRowKeys: [],
  selectedData:[],
  loading: false,
});



/* 选中事件(这里的事件，顺序不能乱，必须key,后面点跟实体，不然回显出问题。) */
const onSelectChange = (selectedRowKeys, rowSelectData) => {
  gridData.selectedData = rowSelectData;
  gridData.selectedRowKeys = selectedRowKeys;
};


/* 按钮loading */
const deleteLoading = ref(false)



/* 返回事件 */
const handlerOnBack = (flag) => {
  show.value = !show.value;
  // 返回清空选择数据
  gridData.selectedData = [];
  gridData.selectedRowKeys = [];
  editConfig.editData = {}
  if (flag){
    getList()
  }
}

/* 新增数据 */
const handlerAdd = ()=>{
  editConfig.value.editStatus = editStatus.ADD
  show.value = !show.value;
}


/* 编辑数据 */
const handlerEdit = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1){
    message.warning('只能选择一条数据')
    return
  }
  editConfig.value.editStatus = editStatus.EDIT
  editConfig.value.editData =  gridData.selectedData[0]

  show.value =!show.value;
}






/* 双击关闭行内编辑触发事件 */
const handleBlur = (save, closeEditor) => {
  // 这里不要做修改 逻辑直接西在下面写
  save();
  closeEditor();
};


/*
  点击回车触发行内编辑事件

  export interface EditableValueParams<RecordType = DefaultRecordType, TValue = any> {
    value: TValue;
    record: RecordType;
    recordIndexs: number[];
    column: ColumnType<RecordType>;
  }

  export interface ValueParserParams<RecordType = DefaultRecordType, TValue = any> {
    newValue: TValue;
    oldValue: TValue;
    record: RecordType;
    recordIndexs: number[];
    column: ColumnType<RecordType>;
  }

  export interface ValueParserFunc<T = any, TValue = any> {
    (params: ValueParserParams<T, TValue>): TValue | null | undefined;
  }

  export interface ValueGetterFunc<T = any, TValue = any> {
    (params: EditableValueParams<T, TValue>): string | null | undefined;
  }
  export interface CellEditorArgs {
    modelValue: Ref<any>;
    save: () => void;
    onInput: (event: Event, value: any) => void;
    closeEditor: () => void;
    column: ColumnType;
    editorRef: Ref<any>;
    getPopupContainer: () => HTMLElement;
    record: DefaultRecordType; // 4.2.0
    recordIndexs: number[]; // 4.2.0
  }

  export type EditableTrigger = 'click' | 'dblClick' | 'contextmenu';

 */
const handleEnter = (save, closeEditor,record) => {
  console.log('--------------');
  // 这里不要做修改 逻辑直接西在下面写
  save();
  closeEditor();

  console.log('editorRef',record)

  getIWarehouseReceiptListUpdate(record.sid,record).then(res => {
    if (res.code === 200) {
      message.success("修改成功！")
      getList()
    }else {
      message.error(res.message)
      getList()
    }
  })
};


const isEditLoading = ref(false)
// 处理数量变更
const handleQuantityChange = (record,column) => {
  console.log('record',record,'column',column)
  if (isEditLoading.value === true){
    console.log('回车，失焦同时触发！')
    return
  }
  isEditLoading.value = true
  if (record && record.sid) {
    console.log('column.dataIndex',column.dataIndex)
    getWarehouseReceiptListBySid(record.sid, record).then(res => {
      if (res.code === 200) {
        // 比较数量
        if (column.dataIndex === 'foreignPrices') {
          // 判断值是否发生变化 qty
          if (isNullOrEmpty(res.data) || res.data.foreignPrices === record.foreignPrices) {
            return
          }
          getIWarehouseReceiptListUpdate(record.sid,record).then(res => {
            if (res.code === 200) {
              message.success("修改成功！")
              // record.foreignPrices = res.data.foreignPrices
              // record.tariff = res.data.tariff
              // record.producAmount = res.data.producAmount
              // record.taxAmount = res.data.taxAmount
              // record.costAmount = res.data.costAmount
              // record.totalAmount = res.data.totalAmount
              getList()
            }else {
              message.error(res.message)
            }
          }).finally(()=>{
            getSumData()
          })
        }else if (column.dataIndex === 'tariff') {
          // 判断值是否发生变化 tariff
          if (isNullOrEmpty(res.data) || res.data.tariff === record.tariff) {
            return
          }
          getIWarehouseReceiptListUpdate(record.sid,record).then(res => {
            if (res.code === 200) {
              message.success("修改成功！")
              // record.tariff = res.data.tariff
              // record.producAmount = res.data.producAmount
              // record.taxAmount = res.data.taxAmount
              // record.costAmount = res.data.costAmount
              // record.totalAmount = res.data.totalAmount
              getList()
            }else {
              message.error(res.message)
            }
          }).finally(()=>{
            getSumData()
          })
        }else if (column.dataIndex === 'consumptionTax') {
          // 判断值是否发生变化 consumptionTax
          if (isNullOrEmpty(res.data) || res.data.consumptionTax === record.consumptionTax) {
            return
          }
          getIWarehouseReceiptListUpdate(record.sid,record).then(res => {
            if (res.code === 200) {
              message.success("修改成功！")
              // record.consumptionTax = res.data.consumptionTax
              // record.tariff = res.data.tariff
              // record.producAmount = res.data.producAmount
              // record.taxAmount = res.data.taxAmount
              // record.costAmount = res.data.costAmount
              // record.totalAmount = res.data.totalAmount
              getList()
            }else {
              message.error(res.message)
            }
          }).finally(()=>{
            getSumData()
          })
        }else if (column.dataIndex === 'valueAddedTax') {
          // 判断值是否发生变化 valueAddedTax
          if (isNullOrEmpty(res.data) || res.data.valueAddedTax === record.valueAddedTax) {
            return
          }
          getIWarehouseReceiptListUpdate(record.sid,record).then(res => {
            if (res.code === 200) {
              message.success("修改成功！")
              // record.valueAddedTax = res.data.valueAddedTax
              // record.tariff = res.data.tariff
              // record.producAmount = res.data.producAmount
              // record.taxAmount = res.data.taxAmount
              // record.costAmount = res.data.costAmount
              // record.totalAmount = res.data.totalAmount
              getList()
            }else {
              message.error(res.message)
            }
          }).finally(()=>{
            getSumData()
          })
        }
      }
    })
  }
  setTimeout(() => {
    isEditLoading.value = false
  }, 100)
}




/* 导出事件 */
const handlerExport = () =>{
  doExport('测试文件导出.xlsx',totalColumns)
}


defineExpose({
  reloadData: getList, // 暴露刷新方法供父组件调用
  dataSourceList
});

/* 接收bus事件 进行刷新 */
onEvent('refreshBizIWarehouseReceiptList', () => {
    getList()
})


</script>

<style lang="less" scoped>
.editable-cell {
  position: relative;
  .editable-cell-input-wrapper,
  .editable-cell-text-wrapper {
    padding-right: 24px;
  }

  .editable-cell-text-wrapper {
    padding: 5px 24px 5px 5px;
  }

  .editable-cell-icon,
  .editable-cell-icon-check {
    position: absolute;
    right: 0;
    width: 20px;
    cursor: pointer;
  }

  .editable-cell-icon {
    margin-top: 4px;
    display: inline-block;
    // display: none;
  }

  .editable-cell-icon-check {
    margin-top: 8px;
  }

  .editable-cell-icon:hover,
  .editable-cell-icon-check:hover {
    color: #108ee9;
  }

  .editable-add-btn {
    margin-bottom: 8px;
  }
}
.editable-cell:hover .editable-cell-icon {
  display: inline-block;
}

/* 弹框表格样式 */
.cs-action-item-modal-table {
  padding: 4px 0;
  margin: 2px 0;
  background: #fff;
  box-sizing: border-box; /* 确保 padding 不会撑大容器 */
  // min-height: calc(100vh - 300px);
  min-height: calc(40vh);
  height: auto;
  .surely-table-body{
    // min-height: calc(100vh - 300px);
    min-height: calc(40vh);
  }
}


</style>
