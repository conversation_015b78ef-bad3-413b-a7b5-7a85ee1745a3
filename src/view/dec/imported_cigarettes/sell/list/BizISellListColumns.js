import {baseColumns} from "@/view/common/baseColumns";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {h, reactive, ref} from "vue";
import {productClassify} from "@/view/common/constant";
import {Tag} from "ant-design-vue";
const {baseColumnsExport, baseColumnsShow} = baseColumns()
const { cmbShowRender } = useColumnsRender()


// 格式化数字为千分位分隔的工具函数
const formatNumber = (value) => {
  if (value === undefined || value === null || value === '') {
    return '';
  }
  // 将数字转换为字符串并添加千分位分隔符
  return new Intl.NumberFormat('zh-CN').format(value);
};

export function getColumns() {

  const commColumns = reactive([
    'sid',
    'salesContractNumber',
    'salesInvoiceNumber',
    'tradeName',
    'unit',
    'quantity',
    'unitPriceExcludingTax',
    'amountOfTax',
    'taxNotIncluded',
    'totalValueTax'
  ])

  /* 导出字段设置 */
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])

  /* table表格字段设置 */
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  const testAdd = (e) =>{
    console.log('新增了')
  }
  /* table表格字段属性设置 */
  const totalColumns = ref([
    {
      title: '销售合同号',
      width: 200,
      align: 'center',
      dataIndex: 'salesContractNumber',
      key: 'salesContractNumber',
      editable: 'cellEditorSlot'
    },
    {
      title: '销售发票号',
      width: 200,
      align: 'center',
      dataIndex: 'salesInvoiceNumber',
      key: 'salesInvoiceNumber',
      editable: 'cellEditorSlot'
    },
    {
      title: '商品名称',
      width: 200,
      align: 'center',
      dataIndex: 'tradeName',
      key: 'tradeName'
    },
    {
      title: '单位',
      width: 200,
      align: 'center',
      dataIndex: 'unit',
      key: 'unit',
    },
    {
      title: '数量',
      width: 200,
      align: 'center',
      dataIndex: 'quantity',
      key: 'quantity',
      customRender: ({ text }) => {
        return formatNumber(text);
      }
    },
    {
      title: '不含税单价',
      width: 200,
      align: 'center',
      dataIndex: 'unitPriceExcludingTax',
      key: 'unitPriceExcludingTax',
      customRender: ({ text }) => {
        return formatNumber(text);
      }
    },
    {
      title: '税额',
      width: 200,
      align: 'center',
      dataIndex: 'amountOfTax',
      key: 'amountOfTax',
      editable: 'cellEditorSlot',
      customRender: ({ text }) => {
        return formatNumber(text);
      }
    },
    {
      title: '不含税金额',
      width: 200,
      align: 'center',
      dataIndex: 'taxNotIncluded',
      key: 'taxNotIncluded',
      editable: 'cellEditorSlot',
      customRender: ({ text }) => {
        return formatNumber(text);
      }
    },
    {
      title: '价税合计',
      width: 200,
      align: 'center',
      dataIndex: 'totalValueTax',
      key: 'totalValueTax',
      customRender: ({ text }) => {
        return formatNumber(text);
      }
    }
  ])

  return{
    columnsConfig,
    excelColumnsConfig,
    totalColumns
  }
}
