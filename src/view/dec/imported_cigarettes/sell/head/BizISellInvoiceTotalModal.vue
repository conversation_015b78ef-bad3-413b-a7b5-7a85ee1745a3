<template>
  <section class="cs-card-form">
    <div>
      <s-table
        style="width: 100%;height: 100%;min-height: 100%;overflow-y: auto;overflow-x:auto"
        ref="tableRef"
        size="small"
        class="cs-action-item-modal-table remove-table-border-add-bg"
        :height="500"
        bordered
        :pagination="false"
        :columns="showColumns"
        :data-source="dataSourceList"
        :loading="tableLoading"
        row-key="invoiceNo"
        :row-height="30"
      >
        <!-- 空数据 -->
        <template #emptyText v-if="!tableLoading">
          <a-empty description="暂无数据" />
        </template>
      </s-table>
    </div>


  </section>
</template>

<script setup>


/* 进口合同列表 */
import {onMounted, reactive, ref} from "vue";
import {getSumDataByInvoiceSummary} from "@/api/cs_api_constant";


/* 表头HeadId*/
const props = defineProps({
  headId:{
    type:String,
    default:null
  }
})

defineOptions({
  name: 'BizISellInvoiceTotalModal'
})

const emit = defineEmits(['cancel'])





/* 数据源 */
const dataSourceList = ref([])

const tableLoading = ref(false)



/* 引入表单数据 */
const gridData = reactive({
  selectedRowKeys: [],
  selectedData:[],
  loading: false,
});



/* 获取发票号汇总数据 */
const getInvoiceTotalData =  () => {
  tableLoading.value = true
  getSumDataByInvoiceSummary({'headId': props.headId}).then(res => {
      if (res.code === 200) {
        dataSourceList.value = res.data
      }
    }
  ).finally(() => {
    tableLoading.value = false
  })
}





// 表格显示列信息 选择框+合同编号+供应商
const showColumns = [
  {
    title: '销售发票号',
    width: 200,
    align: 'center',
    dataIndex: 'salesInvoiceNumber',
    key: 'salesInvoiceNumber',
  },
  {
    title: '税额',
    width: 120,
    align: 'center',
    dataIndex: 'amountOfTax',
    key: 'amountOfTax',
  },
  {
    title: '不含税金额',
    width: 120,
    align: 'center',
    dataIndex: 'taxNotIncluded',
    key: 'taxNotIncluded',
  },
  {
    title: '价税合计',
    width: 120,
    align: 'center',
    dataIndex: 'totalValueTax',
    key: 'totalValueTax',
  },
]


onMounted(() => {
  getInvoiceTotalData()
})

defineExpose({
  gridData
})

</script>

<style lang="less" scoped>

.header-search{
  margin: 10px 0;
}

/* 弹框表格样式 */
.cs-action-item-modal-table {
  padding: 4px 0;
  margin: 2px 0;
  box-sizing: border-box;
  min-height: calc(100vh);
  height: auto;
  .surely-table-body{
    min-height: calc(100vh);
  }
}
.cs-action-item-modal-table-empty{
  padding: 4px 0;
  margin: 2px 0;
  box-sizing: border-box;
  min-height: 500px;
  line-height: 500px;
}


</style>
