<template>
  <a-form layout="inline"  label-align="right"  :label-col="{ style: { width: '100px' } }" :model="searchParam"   class="pw-form grid-container" >



    <!--  主建SID  -->
    <a-form-item name="sid"  label="主建SID" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.sid" />
    </a-form-item>



    <!--  制单人  -->
    <a-form-item name="insertUser"  label="制单人" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.insertUser" />
    </a-form-item>





    <!--  订单制单时间   -->
    <a-form-item name="insertTime" label="订单制单时间"  class="grid-item" :colon="false">
      <!-- Warning: [ant-design-vue: Form.Item] FormItem can only collect one field item,
            you haved set ASelect, ASelect, AInputNumber, AInputNumber, AInput 5 field items. You can set not need to be collected fields into a-form-item-rest
      -->
      <a-form-item-rest>
        <a-row>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.insertTimeForm"
              id="insertTimeForm"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
            />
          </a-col>
          <a-col :span="2" style="text-align: center">
            -
          </a-col>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.insertTimeTo"
              id="insertTimeTo"
              size="small"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD"
              :locale="locale"
              style="width: 100%"
              placeholder=""
            />
          </a-col>
        </a-row>
      </a-form-item-rest>
    </a-form-item>

    <!--  创建人姓名  -->
    <a-form-item name="insertUserName"  label="创建人姓名" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.insertUserName" />
    </a-form-item>



    <!--  更新人  -->
    <a-form-item name="updateUser"  label="更新人" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.updateUser" />
    </a-form-item>





    <!--  更新时间   -->
    <a-form-item name="updateTime" label="更新时间"  class="grid-item" :colon="false">
      <!-- Warning: [ant-design-vue: Form.Item] FormItem can only collect one field item,
            you haved set ASelect, ASelect, AInputNumber, AInputNumber, AInput 5 field items. You can set not need to be collected fields into a-form-item-rest
      -->
      <a-form-item-rest>
        <a-row>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.updateTimeForm"
              id="updateTimeForm"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
            />
          </a-col>
          <a-col :span="2" style="text-align: center">
            -
          </a-col>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.updateTimeTo"
              id="updateTimeTo"
              size="small"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD"
              :locale="locale"
              style="width: 100%"
              placeholder=""
            />
          </a-col>
        </a-row>
      </a-form-item-rest>
    </a-form-item>

    <!--  更新人姓名  -->
    <a-form-item name="updateUserName"  label="更新人姓名" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.updateUserName" />
    </a-form-item>



    <!--  企业代码  -->
    <a-form-item name="tradeCode"  label="企业代码" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.tradeCode" />
    </a-form-item>



    <!--  商品牌号  -->
    <a-form-item name="productGrade"  label="商品牌号" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.productGrade" />
    </a-form-item>



    <!--  单位  -->
    <a-form-item name="unit"  label="单位" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.unit" />
    </a-form-item>






    <!--  币种  -->
    <a-form-item name="curr"  label="币种" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.curr" />
    </a-form-item>









    <!--  表头IO  -->
    <a-form-item name="headId"  label="表头IO" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.headId" />
    </a-form-item>



    <!--  商品类别  -->
    <a-form-item name="productType"  label="商品类别" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.productType" />
    </a-form-item>



    <!--  版本号  -->
    <a-form-item name="versionNo"  label="版本号" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.versionNo" />
    </a-form-item>



    <!--  数据状态  -->
    <a-form-item name="dataStatus"  label="数据状态" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.dataStatus" />
    </a-form-item>



    <!--  拓展字段1  -->
    <a-form-item name="extend1"  label="拓展字段1" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.extend1" />
    </a-form-item>



    <!--  拓展字段2  -->
    <a-form-item name="extend2"  label="拓展字段2" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.extend2" />
    </a-form-item>



    <!--  拓展字段3  -->
    <a-form-item name="extend3"  label="拓展字段3" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.extend3" />
    </a-form-item>



    <!--  拓展字段4  -->
    <a-form-item name="extend4"  label="拓展字段4" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.extend4" />
    </a-form-item>



    <!--  拓展字段5  -->
    <a-form-item name="extend5"  label="拓展字段5" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.extend5" />
    </a-form-item>



    <!--  拓展字段6  -->
    <a-form-item name="extend6"  label="拓展字段6" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.extend6" />
    </a-form-item>



    <!--  拓展字段7  -->
    <a-form-item name="extend7"  label="拓展字段7" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.extend7" />
    </a-form-item>



    <!--  拓展字段8  -->
    <a-form-item name="extend8"  label="拓展字段8" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.extend8" />
    </a-form-item>



    <!--  拓展字段9  -->
    <a-form-item name="extend9"  label="拓展字段9" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.extend9" />
    </a-form-item>



    <!--  拓展字段10  -->
    <a-form-item name="extend10"  label="拓展字段10" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.extend10" />
    </a-form-item>









  </a-form>
</template>

<script setup>
import {inject, onMounted, reactive} from 'vue'

defineOptions({
  name: "BizIOrderListSearch",
})

/* 定义重置方法(注意前后顺序) */
const resetSearch = () => {
  Object.keys(searchParam).forEach(key => {
    searchParam[key] = '';
  });
}


const searchParam = reactive({
  sid:'',
  insertUser:'',
  insertTime:'',
  insertUserName:'',
  updateUser:'',
  updateTime:'',
  updateUserName:'',
  tradeCode:'',
  productGrade:'',
  unit:'',
  qty:'',
  curr:'',
  decPrice:'',
  decTotal:''
})

defineExpose({searchParam,resetSearch});



onMounted(() => {

});


</script>

<style lang='less' scoped>

</style>
