<template>
  <section>
    <a-card size="small" title="客户基础信息" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData"   class=" grid-container">
          <!-- 主建SID -->
          <a-form-item name="sid"   :label="'主建SID'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable"  size="small" v-model:value="formData.sid" />
          </a-form-item>
          <!-- 制单人 -->
          <a-form-item name="insertUser"   :label="'制单人'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable"  size="small" v-model:value="formData.insertUser" />
          </a-form-item>
          <!-- 订单制单时间 -->
          <a-form-item name="insertTime"   :label="'订单制单时间'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable"  size="small" v-model:value="formData.insertTime" />
          </a-form-item>
          <!-- 创建人姓名 -->
          <a-form-item name="insertUserName"   :label="'创建人姓名'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable"  size="small" v-model:value="formData.insertUserName" />
          </a-form-item>
          <!-- 更新人 -->
          <a-form-item name="updateUser"   :label="'更新人'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable"  size="small" v-model:value="formData.updateUser" />
          </a-form-item>
          <!-- 更新时间 -->
          <a-form-item name="updateTime"   :label="'更新时间'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable"  size="small" v-model:value="formData.updateTime" />
          </a-form-item>
          <!-- 更新人姓名 -->
          <a-form-item name="updateUserName"   :label="'更新人姓名'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable"  size="small" v-model:value="formData.updateUserName" />
          </a-form-item>
          <!-- 企业代码 -->
          <a-form-item name="tradeCode"   :label="'企业代码'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable"  size="small" v-model:value="formData.tradeCode" />
          </a-form-item>
          <!-- 商品牌号 -->
          <a-form-item name="productGrade"   :label="'商品牌号'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable"  size="small" v-model:value="formData.productGrade" />
          </a-form-item>
          <!-- 单位 -->
          <a-form-item name="unit"   :label="'单位'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable"  size="small" v-model:value="formData.unit" />
          </a-form-item>
          <!-- 数量 -->
          <a-form-item name="qty"   :label="'数量'" class="grid-item"  :colon="false">
            <a-input-number style="width: 100%"  :disabled="showDisable"  size="small" v-model:value="formData.qty" />
          </a-form-item>
          <!-- 币种 -->
          <a-form-item name="curr"   :label="'币种'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable"  size="small" v-model:value="formData.curr" />
          </a-form-item>
          <!-- 单价 -->
          <a-form-item name="decPrice"   :label="'单价'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable"  size="small" v-model:value="formData.decPrice" />
          </a-form-item>
          <!-- 总价 -->
          <a-form-item name="decTotal"   :label="'总价'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable"  size="small" v-model:value="formData.decTotal" />
          </a-form-item>
          <!-- 表头IO -->
          <a-form-item name="headId"   :label="'表头IO'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable"  size="small" v-model:value="formData.headId" />
          </a-form-item>
          <!-- 商品类别 -->
          <a-form-item name="productType"   :label="'商品类别'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable"  size="small" v-model:value="formData.productType" />
          </a-form-item>
          <!-- 版本号 -->
          <a-form-item name="versionNo"   :label="'版本号'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable"  size="small" v-model:value="formData.versionNo" />
          </a-form-item>
          <!-- 数据状态 -->
          <a-form-item name="dataStatus"   :label="'数据状态'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable"  size="small" v-model:value="formData.dataStatus" />
          </a-form-item>
          <!-- 拓展字段1 -->
          <a-form-item name="extend1"   :label="'拓展字段1'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable"  size="small" v-model:value="formData.extend1" />
          </a-form-item>
          <!-- 拓展字段2 -->
          <a-form-item name="extend2"   :label="'拓展字段2'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable"  size="small" v-model:value="formData.extend2" />
          </a-form-item>
          <!-- 拓展字段3 -->
          <a-form-item name="extend3"   :label="'拓展字段3'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable"  size="small" v-model:value="formData.extend3" />
          </a-form-item>
          <!-- 拓展字段4 -->
          <a-form-item name="extend4"   :label="'拓展字段4'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable"  size="small" v-model:value="formData.extend4" />
          </a-form-item>
          <!-- 拓展字段5 -->
          <a-form-item name="extend5"   :label="'拓展字段5'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable"  size="small" v-model:value="formData.extend5" />
          </a-form-item>
          <!-- 拓展字段6 -->
          <a-form-item name="extend6"   :label="'拓展字段6'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable"  size="small" v-model:value="formData.extend6" />
          </a-form-item>
          <!-- 拓展字段7 -->
          <a-form-item name="extend7"   :label="'拓展字段7'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable"  size="small" v-model:value="formData.extend7" />
          </a-form-item>
          <!-- 拓展字段8 -->
          <a-form-item name="extend8"   :label="'拓展字段8'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable"  size="small" v-model:value="formData.extend8" />
          </a-form-item>
          <!-- 拓展字段9 -->
          <a-form-item name="extend9"   :label="'拓展字段9'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable"  size="small" v-model:value="formData.extend9" />
          </a-form-item>
          <!-- 拓展字段10 -->
          <a-form-item name="extend10"   :label="'拓展字段10'" class="grid-item"  :colon="false">
            <a-input :disabled="showDisable"  size="small" v-model:value="formData.extend10" />
          </a-form-item>
          <div class="cs-submit-btn merge-3">
            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right"
                      v-show="props.editConfig.editStatus !== 'SHOW' ">保存
            </a-button>
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(false)">返回</a-button>
          </div>
        </a-form>
      </div>
    </a-card>


  </section>
</template>

<script setup>
import {editStatus, productClassify} from '@/view/common/constant'
import {message} from "ant-design-vue";
import {onMounted, reactive, ref} from "vue";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
import {insertBizErpIOrderList, updateBizErpIOrderList} from "@/api/cs_api_constant";
const { getPCode } = usePCode()

defineOptions({
  name: "BizIOrderListEdit",
})

const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});

// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onBack']);

const onBack = (val) => {
  emit('onBack', val);
};

// 是否禁用
const showDisable = ref(false)

// 表单数据
const formData = reactive({
  // 主建SID
  sid:'',
  // 制单人
  insertUser:'',
  // 订单制单时间
  insertTime:'',
  // 创建人姓名
  insertUserName:'',
  // 更新人
  updateUser:'',
  // 更新时间
  updateTime:'',
  // 更新人姓名
  updateUserName:'',
  // 企业代码
  tradeCode:'',
  // 商品牌号
  productGrade:'',
  // 单位
  unit:'',
  // 数量
  qty:'',
  // 币种
  curr:'',
  // 单价
  decPrice:'',
  // 总价
  decTotal:'',
  // 表头IO
  headId:'',
  // 商品类别
  productType:'',
  // 版本号
  versionNo:'',
  // 数据状态
  dataStatus:'',
  // 拓展字段1
  extend1:'',
  // 拓展字段2
  extend2:'',
  // 拓展字段3
  extend3:'',
  // 拓展字段4
  extend4:'',
  // 拓展字段5
  extend5:'',
  // 拓展字段6
  extend6:'',
  // 拓展字段7
  extend7:'',
  // 拓展字段8
  extend8:'',
  // 拓展字段9
  extend9:'',
  // 拓展字段10
  extend10:''
})
// 校验规则
const rules = {
  sid:[
    {max: 50, message: '主建SID长度不能超过 50位字节', trigger: 'blur'}
  ],
  insertUser:[
    {max: 50, message: '制单人长度不能超过 50位字节', trigger: 'blur'}
  ],
  insertTime:[
  ],
  insertUserName:[
    {max: 50, message: '创建人姓名长度不能超过 50位字节', trigger: 'blur'}
  ],
  updateUser:[
    {max: 50, message: '更新人长度不能超过 50位字节', trigger: 'blur'}
  ],
  updateTime:[
  ],
  updateUserName:[
    {max: 50, message: '更新人姓名长度不能超过 50位字节', trigger: 'blur'}
  ],
  tradeCode:[
    {max: 50, message: '企业代码长度不能超过 50位字节', trigger: 'blur'}
  ],
  productGrade:[
    {max: 80, message: '商品牌号长度不能超过 80位字节', trigger: 'blur'}
  ],
  unit:[
    {max: 9, message: '单位长度不能超过 9位字节', trigger: 'blur'}
  ],
  qty:[
    { type: 'number', message: '数量不是有效的数字!'},
  ],
  curr:[
    {max: 10, message: '币种长度不能超过 10位字节', trigger: 'blur'}
  ],
  decPrice:[
    { type: 'number', message: '单价不是有效的数字!'},
  ],
  decTotal:[
    { type: 'number', message: '总价不是有效的数字!'},
  ],
  headId:[
    {max: 50, message: '表头IO长度不能超过 50位字节', trigger: 'blur'}
  ],
  productType:[
    {max: 80, message: '商品类别长度不能超过 80位字节', trigger: 'blur'}
  ],
  versionNo:[
    {max: 10, message: '版本号长度不能超过 10位字节', trigger: 'blur'}
  ],
  dataStatus:[
    {max: 10, message: '数据状态长度不能超过 10位字节', trigger: 'blur'}
  ],
  extend1:[
    {max: 200, message: '拓展字段1长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend2:[
    {max: 200, message: '拓展字段2长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend3:[
    {max: 200, message: '拓展字段3长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend4:[
    {max: 200, message: '拓展字段4长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend5:[
    {max: 200, message: '拓展字段5长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend6:[
    {max: 200, message: '拓展字段6长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend7:[
    {max: 200, message: '拓展字段7长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend8:[
    {max: 200, message: '拓展字段8长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend9:[
    {max: 200, message: '拓展字段9长度不能超过 200位字节', trigger: 'blur'}
  ],
  extend10:[
    {max: 200, message: '拓展字段10长度不能超过 200位字节', trigger: 'blur'}
  ]
}

const pCode = ref('')
// 初始化操作
onMounted(() => {
  getPCode().then(res=>{
    console.log('res',res)
    pCode.value = res;
  })
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    showDisable.value = false
    Object.assign(formData, {});
  }
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = true
  }
});



// vue3中使用ref,需要先定义，然后在模板中使用。因为在模板渲染之前，DOM 元素还不存在。
const formRef = ref(null);
// 保存
const handlerSave = () => {
  formRef.value
    .validate()
    .then(() => {
      if (props.editConfig && props.editConfig.editStatus === editStatus.ADD){
        insertBizErpIOrderList(formData).then((res)=>{
          if (res.code === 200){
            message.success('新增成功!')
            onBack(true)
          }else {
            message.error(res.message)
          }
        })
      }else if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT){
        updateBizErpIOrderList(formData.sid,formData).then((res)=>{
          if (res.code === 200){
            message.success('修改成功!')
            onBack(true)
          }else {
            message.error(res.message)
          }
        })
      }
    })
    .catch(error => {
      console.log('validate failed', error);
    })
};

</script>

<style lang="less" scoped>


</style>



