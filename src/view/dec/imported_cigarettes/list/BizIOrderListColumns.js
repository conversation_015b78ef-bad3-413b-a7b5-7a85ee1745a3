import {baseColumns} from "@/view/common/baseColumns";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {h, reactive, ref} from "vue";
import {productClassify} from "@/view/common/constant";
import {Tag} from "ant-design-vue";
const {baseColumnsExport, baseColumnsShow} = baseColumns()
const { cmbShowRender,formatNumber } = useColumnsRender()




export function getColumns() {

  const commColumns = reactive([
    'sid',
    'insertUser',
    'insertTime',
    'insertUserName',
    'updateUser',
    'updateTime',
    'updateUserName',
    'tradeCode',
    'productGrade',
    'unit',
    'qty',
    'curr',
    'decPrice',
    'decTotal',
    'headId',
    'productType',
    'versionNo',
    'dataStatus',
    'extend1',
    'extend2',
    'extend3',
    'extend4',
    'extend5',
    'extend6',
    'extend7',
    'extend8',
    'extend9',
    'extend10',
    'contractNo'
  ])

  /* 导出字段设置 */
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])

  /* table表格字段设置 */
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  const testAdd = (e) =>{
    console.log('新增了')
  }
  /* table表格字段属性设置 */
  const totalColumns = ref([


    {
      title: '商品牌号',
      minWidth: 200,
      align: 'center',
      dataIndex: 'productGrade',
      key: 'productGrade',
      resizable: true
    },
    {
      title: '单位',
      minWidth: 200,
      align: 'center',
      dataIndex: 'unit',
      key: 'unit',
      resizable: true
    },
    {
      title: '数量',
      minWidth: 200,
      align: 'center',
      dataIndex: 'qty',
      key: 'qty',
      editable: 'cellEditorSlot',
      resizable: true,
      customRender({text}){
        return formatNumber(text);
      }
    },
    {
      title: '币种',
      minWidth: 200,
      align: 'center',
      dataIndex: 'curr',
      key: 'curr',
      resizable: true,
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,[],'CURR_OUTDATED'))
      }
    },
    {
      title: '单价',
      minWidth: 200,
      align: 'center',
      dataIndex: 'decPrice',
      key: 'decPrice',
      resizable: true,
      customRender({text}){
        return formatNumber(text);
      }
    },
    {
      title: '总值',
      minWidth: 200,
      align: 'center',
      dataIndex: 'decTotal',
      key: 'decTotal',
      resizable: true,
      customRender({text}){
        return formatNumber(text);
      }
    },
    {
      title: '合同号',
      minWidth: 200,
      align: 'center',
      dataIndex: 'contractNo',
      key: 'contractNo',
      resizable: true
    }
    // {
    //   title: '表头IO',
    //   minWidth: 200,
    //   align: 'center',
    //   dataIndex: 'headId',
    //   key: 'headId',
    // },
    // {
    //   title: '商品类别',
    //   minWidth: 200,
    //   align: 'center',
    //   dataIndex: 'productType',
    //   key: 'productType',
    // },
    // {
    //   title: '版本号',
    //   minWidth: 200,
    //   align: 'center',
    //   dataIndex: 'versionNo',
    //   key: 'versionNo',
    // },
    // {
    //   title: '数据状态',
    //   minWidth: 200,
    //   align: 'center',
    //   dataIndex: 'dataStatus',
    //   key: 'dataStatus',
    // }

  ])

  return{
    columnsConfig,
    excelColumnsConfig,
    totalColumns
  }
}
