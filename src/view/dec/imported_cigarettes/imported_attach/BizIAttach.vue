<template>
  <div class="cs-action">
    <a-table
      class="cs-action-item"
      style="width:800px;"
      size="small"
      :columns="columns"
      :data-source="dataSource"
      bordered
      :pagination="false"
      row-key="sid"
    >
      <template #bodyCell="{ column, record }">
        <!-- 文件列的自定义渲染 -->
        <template v-if="column.key === 'action'">
          <cs-upload :head-id="props.headId" :b-type="record.businessType"  :is-show="props.operationStatus !== editStatus.SHOW && props.isAllConfirmed !== true " ></cs-upload>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { message } from 'ant-design-vue';
import CsUpload from "@/components/upload/CsUpload.vue";
import {docType, editStatus} from "@/view/common/constant";
defineOptions({
  name:'BizIAttach'
})
const props = defineProps({
  headId: {
    type: String,
    default: ''
  },
  operationStatus:{
    type:String,
    default:''
  },
  editConfig: {
    type: Object,
    default: () => {
    }
  },
  /* 是否已经全部确认 */
  isAllConfirmed:{
    type:Boolean,
    default:false
  }
});

/* 数据列 */
const dataSource = reactive([
  {
    id: 1,
    type: '清关环节文件',
    businessType: docType.orderAttachType,
    files: {
      contract: []
    }
  },
  {
    id: 2,
    type: '销售环节文件',
    businessType: docType.salesAttachType,
    files: {
      order: []
    }
  }
]);

/* 列表显示列信息 */
const columns = [
  {
    title: '文件类型',
    dataIndex: 'type',
    align: 'center',
    width: 200
  },
  {
    title: '文件',
    key: 'action',
    align: 'center',
    flex:1
  }
];



</script>

<style lang="less" scoped>

</style>
