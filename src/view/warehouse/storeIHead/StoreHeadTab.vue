<template>
  <section class="cs-action cs-action-tab">
    <div class="cs-tab">
      <a-tabs class="sticky-header"  v-model:activeKey="tabName" size="small" :tabBarStyle="tabBarStyle" >
        <a-tab-pane key="iheadTab" tab="入库回单" >
          <BizStoreIHeadEdit  ref="iheadTab"  :edit-config="editConfig"  @onEditBack="editBack"  :operation-status="editConfig.editStatus"></BizStoreIHeadEdit>
        </a-tab-pane>
        <a-tab-pane v-if="showBodyPurchaseHead" key="eHeadEdit" tab="出库回单" @onEditBack="editBack" >
          <BizStoreEHeadEdit ref="eHeadEdit" :head-id="headId" :e-head-id="eHeadId"  :edit-config="editConfig" :operation-status="editConfig.editStatus" @onEditBack="editBack"></BizStoreEHeadEdit>
        </a-tab-pane>
        <a-tab-pane v-if="showBody" key="attachTab" tab="归档附件" @onEditBack="editBack" >
          <biz-i-attach :head-id="headId"  :operation-status="editConfig.editStatus" :edit-config="editConfig" :is-all-confirmed="false"></biz-i-attach>
        </a-tab-pane>

        <!-- 审批记录 -->
        <a-tab-pane v-if="showBody" key="approvalTab" tab="审批记录" @onEditBack="editBack" >
          <biz-i-approval-list></biz-i-approval-list>
        </a-tab-pane>
        <template #rightExtra>
          <div class="cs-tab-icon" @click="editBack">
            <GlobalIcon type="close-circle" style="color:#000"/>
          </div>
        </template>
      </a-tabs>
    </div>

  </section>
</template>

<script setup>

import {onMounted, reactive, ref, watch} from "vue";
import {editStatus} from "@/view/common/constant";
import BizIAttach from "@/view/warehouse/components/BizIAttach.vue";
import BizIApprovalList from "@/view/warehouse/approval/BizIApprovalList.vue";
import {checkIsNextModule, checkOrderHeadIsNextModule, getStoreEHeadByHeadSid} from "@/api/cs_api_constant";
import {message} from "ant-design-vue";
import {isNullOrEmpty} from "@/view/utils/common";
import BizStoreIHeadEdit from "@/view/warehouse/storeIHead/BizStoreIHeadEdit";
import BizStoreEHeadEdit from "@/view/warehouse/storeEHead/BizStoreEHeadEdit";
defineOptions({
  name:'StoreHeadTab'
})

/* 判断是否状态是否全部已经 确认 */
const isAllConfirmed = ref(false)

const emit = defineEmits(['onEditBack'])

/* 定义editConfig 用于向子组件传递 */
const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});


/* 自定义样式 */
const tabBarStyle = {
  background:'#fff',
  position:'sticky',
  top:'0',
  zIndex:'100',
}

/* 激活Tab key */
const tabName = ref('iheadTab');

/* 总tab信息 */
const tabs = reactive({
  iheadTab:true,
  shipFrom:true,
})

/* 表头headId */
const headId = ref('')
const eHeadId = ref('')


/* 是否显示子模块 tab */
const showBody = ref(false)
/* 是否显示出库信息 */
const showBodyPurchaseHead = ref(false)


/* 返回tab界面 */
const editBack = (val) => {
  if (val.editStatus === editStatus.EDIT){
    showBody.value = val.showBody
    showBodyPurchaseHead.value = val.showBodyPurchaseHead
    if(val.editData != null){
      headId.value =  val.editData.sid
      props.editConfig.editStatus = val.editStatus
      props.editConfig.editData = val.editData
      console.log('val.editData', val.editData)
      if(val.editData.eheadId !== null && val.editData.eheadId !== ''){
        eHeadId.value = val.editData.eheadId
      }else {
        getStoreEHeadByHeadSid({headId:val.editData.sid}).then((res) => {
          if (res.code === 200) {
            if(res.data !== null && res.data !== ''){
              eHeadId.value = res.data.sid
            }
          }
        })
      }
      console.log('eHeadId.value', eHeadId.value)
    }
  }else {
    if (val) {
      emit('onEditBack', val)
    }
  }
}


/* 初始化操作 */
onMounted(()=>{
  console.log('props.editConfig', props.editConfig)
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    headId.value = ''
    showBody.value = false
    showBodyPurchaseHead.value=false
    eHeadId.value = ''
  } else if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    headId.value = props.editConfig.editData.sid
    getStoreEHeadByHeadSid({headId:props.editConfig.editData.sid}).then((res) => {
      if (res.code === 200) {
        if(res.data !== null && res.data !== ''){
          eHeadId.value = res.data.sid
        }
      }
    })
    // +++++++++++++++++++ 废弃数据单独处理 ++++++++++++++++++++++++++++
    if (props.editConfig.editData.status === '2') {
      checkIsNextModule({sid:props.editConfig.editData.sid}).then((res) => {
        if (res.code === 200) {
          showBody.value = res.data.showBody > 0
          showBodyPurchaseHead.value = res.data.showBodyStoreEHead > 0;
        }else {
          message.error(res.message)
        }
      })
    }else {
      // 判断表头状态是否已经确认
      // 1.1.4.4.TAB2 进货信息表头
      // 原需求：订单信息数据点击保存成功保存时，将其表头、表体数据自动带入作为进货信息表头表体
      // 新改法：保存时先将数据流入进货信息，只有订单表头确认时，才将对应的tab界面显示出来
      if (props.editConfig.editData.isNext === '1') {
        headId.value = props.editConfig.editData.sid
        // showBody.value = true
        if (props.editConfig.editData.status === '1' || props.editConfig.editData.storeEStatus === '1') {
          showBodyPurchaseHead.value = true
        }
      } else {
        headId.value = ''
        // showBody.value = false
        showBodyPurchaseHead.value = false
      }
      console.log('headId.value', headId.value)
      console.log('showBodyPurchaseHead.value', showBodyPurchaseHead.value)
    }

    // 判断日期是否为空
    if (props.editConfig.editData.updateTime !== null) {
      showBody.value = true
    }else {
      showBody.value = false
    }
    console.log('showBody.value', showBody.value)
    console.log('props.editConfig.editData', props.editConfig.editData)

  }else if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW ) {
    headId.value = props.editConfig.editData.sid
    getStoreEHeadByHeadSid({headId:props.editConfig.editData.sid}).then((res) => {
      if (res.code === 200) {
        if(res.data !== null && res.data !== ''){
          eHeadId.value = res.data.sid
        }
      }
    })

    if (props.editConfig.editData.status === '2' ) {
      checkOrderHeadIsNextModule({sid:props.editConfig.editData.sid}).then((res) => {
        if (res.code === 200) {
          showBody.value = res.data.showBody > 0
          showBodyPurchaseHead.value = res.data.showBodyStoreEHead > 0;
        }else {
          //
          message.error(res.message)
        }
      })
    }else {
      // 判断日期是否为空
      if (props.editConfig.editData.updateTime !== null) {
        showBody.value = true
      }
      if (props.editConfig.editData.status === '1'  ){
        showBodyPurchaseHead.value = true
      }
    }
  }
})



/* 监控tabName变化 */
watch(tabName, (value) => {
  for (let t in tabs) {
    tabs[t] = false
  }
  tabs[value] = true
  isAllConfirmed.value =  (props.editConfig.editData.status !== '0' &&  props.editConfig.editData.purchaseDataStatus !== '0'
    && props.editConfig.editData.salesDataStatus !== '0' && props.editConfig.editData.inboundReceiptStatus !== '0' &&  props.editConfig.editData.outboundReceiptStatus !== '0')
})

</script>

<style lang="less" scoped>

</style>
