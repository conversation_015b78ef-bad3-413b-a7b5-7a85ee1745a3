import {baseColumns} from "@/view/common/baseColumns";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {h, reactive, ref} from "vue";
import {useMerchant} from "@/view/common/useMerchant";
const {baseColumnsExport, baseColumnsShow} = baseColumns()
const { cmbShowRender,formatNumber } = useColumnsRender()
const { merchantOptions,getMerchantOptions } = useMerchant()

await getMerchantOptions()
export function getColumns() {

  const commColumns = reactive([
    'gName'
    , 'specifications'
    , 'qty'
    , 'unit'
    , 'currPrice'
    , 'rmbPrice'
    , 'currTotalPrice'
    , 'rmbTotalPrice'
  ])

// 导出字段设置`
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns,
  ])

  // table表格字段设置
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  // table表格字段设置
  const totalColumns = ref([
    // {
    //   width: 80,
    //   minWidth:80,
    //   title: '操作',
    //   dataIndex: 'operation',
    //   key: 'operation',
    //   align: 'center',
    //   fixed: 'left',
    //   resizable:"true",
    // },
    {
      title: '商品名称',
      minWidth: 120,
      align: 'center',
      key: 'gName',
      dataIndex: 'gName',
      resizable:"true",
    },
    {
      title: '规格',
      minWidth: 120,
      align: 'center',
      key: 'specifications',
      dataIndex: 'specifications',
      resizable:"true",
    },
    {
      title: '数量',
      minWidth: 120,
      align: 'center',
      key: 'qty',
      dataIndex: 'qty',
      resizable:"true",
      customRender({text}){
        return formatNumber(text);
      }
    },
    {
      title: '单位',
      minWidth: 120,
      align: 'center',
      key: 'unit',
      dataIndex: 'unit',
      resizable:"true",
    },
    {
      title: '外币单价',
      minWidth: 120,
      align: 'center',
      key: 'currPrice',
      dataIndex: 'currPrice',
      resizable:"true",
      customRender({text}){
        return formatNumber(text);
      }
    },

    {
      title: '人民币单价',
      minWidth: 120,
      align: 'center',
      key: 'rmbPrice',
      dataIndex: 'rmbPrice',
      resizable:"true",
      customRender({text}){
        return formatNumber(text);
      }
    },
    {
      title: '外币货价',
      minWidth: 120,
      align: 'center',
      key: 'currTotalPrice',
      dataIndex: 'currTotalPrice',
      resizable:"true",
      customRender({text}){
        return formatNumber(text);
      }
    },
    {
      title: '人民币货价',
      minWidth: 120,
      align: 'center',
      key: 'rmbTotalPrice',
      dataIndex: 'rmbTotalPrice',
      resizable:"true",
      customRender({text}){
        return formatNumber(text);
      }
    },
  ])

  return{
    columnsConfig,
    excelColumnsConfig,
    totalColumns,
    commColumns
  }
}


