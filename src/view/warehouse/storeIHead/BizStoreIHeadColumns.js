import {baseColumns} from "@/view/common/baseColumns";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {h, reactive, ref} from "vue";
import {useMerchant} from "@/view/common/useMerchant";
const {baseColumnsExport, baseColumnsShow} = baseColumns()
const { cmbShowRender } = useColumnsRender()
const { merchantOptions,getMerchantOptions } = useMerchant()

await getMerchantOptions()
export function getColumns() {

  const commColumns = reactive([
    'storeINo'
    , 'contractNo'
    // , 'purSaleContractNo'
    , 'purchaseOrderNo'
    , 'invoiceNo'
    , 'merchantCode'
    // , 'status'
    , 'createrBy'
    , 'createrTime'
  ])

// 导出字段设置`
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns,
  ])

  // table表格字段设置
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  // table表格字段设置
  const totalColumns = ref([
    {
      width: 80,
      minWidth:80,
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      align: 'center',
      fixed: 'left',
      resizable:"true",
    },
    {
      title: '入库回单编号',
      minWidth: 120,
      align: 'center',
      key: 'storeINo',
      dataIndex: 'storeINo',
      resizable:"true",
    },
    {
      title: '合同号',
      minWidth: 120,
      align: 'center',
      key: 'contractNo',
      dataIndex: 'contractNo',
      resizable:"true",
    },
    // {
    //   title: '购销合同号',
    //   minWidth: 120,
    //   align: 'center',
    //   key: 'purSaleContractNo',
    //   dataIndex: 'purSaleContractNo',
    //   resizable:"true",
    // },
    {
      title: '进货单号',
      minWidth: 120,
      align: 'center',
      key: 'purchaseOrderNo',
      dataIndex: 'purchaseOrderNo',
      resizable:"true",
    },
    {
      title: '发票号',
      minWidth: 120,
      align: 'center',
      key: 'invoiceNo',
      dataIndex: 'invoiceNo',
      resizable:"true",
    },

    {
      title: '供应商',
      minWidth: 120,
      align: 'center',
      key: 'merchantCode',
      dataIndex: 'merchantCode',
      resizable:"true",
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text, merchantOptions.value))
      }
    },
    // {
    //   title: '单据状态',
    //   minWidth: 120,
    //   align: 'center',
    //   key: 'status',
    //   dataIndex: 'status',
    //   resizable:"true",
    //   customRender: ({ text }) => {
    //     return h(<div></div>, cmbShowRender(text,productClassify.dataStatus))
    //   }
    // },
    {
      title: '制单人',
      minWidth: 120,
      align: 'center',
      key: 'createrUserName',
      dataIndex: 'createrUserName',
      resizable:"true",
    },
    {
      title: '制单时间',
      minWidth: 120,
      align: 'center',
      key: 'createrTime',
      dataIndex: 'createrTime',
      resizable:"true",
    },
  ])

  return{
    columnsConfig,
    excelColumnsConfig,
    totalColumns,
    commColumns
  }
}


