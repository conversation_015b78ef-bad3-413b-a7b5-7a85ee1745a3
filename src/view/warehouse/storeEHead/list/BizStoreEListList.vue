 <template>
  <section  class="dc-section" ref="orderListRef">
    <div class="cs-action"  v-show="show">

      <!-- 操作按钮区域 -->
      <div class="cs-action-btn" v-if="!showDisable">
          <div class="cs-action-btn-item" v-has="['yc-cs:importedCigarettes-order-list:delete']">
            <a-button  size="small" :loading="deleteLoading" @click="handlerDelete" :disabled="!props.isEdit">
              <template #icon>
                <GlobalIcon type="delete" style="color:red"/>
              </template>
              {{localeContent('m.common.button.delete')}}
            </a-button>
          </div>
      </div>

      <!-- 表格区域 -->
      <div>
        <s-table
          v-if="tableLoading === false"
          ref="tableRef"
          class="cs-action-item-modal-table remove-table-border-add-bg"
          size="small"
          :scroll="{ y: tableHeight,x:400 }"
          bordered
          :pagination="false"
          :columns="totalColumns"
          :data-source="dataSourceList"
          :row-selection="{  selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          row-key="sid"
          :row-height="30"
          :range-selection="false"
        >
          <!-- 空数据 -->
          <template #emptyText>
            <a-empty description="暂无数据" />
          </template>
          <!-- 看下面注释
                原始行内编辑，这里全部打开
                #cellEditor="{ column, modelValue, save, closeEditor, editorRef, getPopupContainer,record,recordIndex }"
                @keydown.enter="handleEnter(save, closeEditor,record)"
                @blur="handleBlur(save, closeEditor)"
                @keydown.enter="handleEnter(save, closeEditor,record)"
                @keydown.esc="closeEditor"
                :ref="editorRef"
          -->
          <template #bodyCell="{text, record, index, column, key }">
            <template v-if="( !showDisable  &&   props.isEdit ) && column.dataIndex === 'qtyIss' ">
              <!-- 禁止回车 -->
              <a-input-number
                size="small"
                v-model:value="dataSourceList[index].qtyIss"
                style="width: 100%;height: 24px"
                :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                :parser="value => inputParser(value)"
                @blur="() => {
                  handleQuantityChange(record);
                }"
                @keydown.enter="() => {
                  handleQuantityChange(record);
                }"
              />
            </template>
            <template v-if="( !showDisable  &&   props.isEdit ) && column.dataIndex === 'qtyDeli' ">
              <!-- 禁止回车 -->
              <a-input-number
                size="small"
                v-model:value="dataSourceList[index].qtyDeli"
                style="width: 100%;height: 24px"
                :formatter="value => isNullOrEmpty(value)?null:inputFormatter(value)"
                :parser="value => inputParser(value)"
                @blur="() => {
                  handleQuantityChange(record);
                }"
                @keydown.enter="() => {
                  handleQuantityChange(record);
                }"
              />
            </template>
            <template v-if="column.dataIndex === 'unit'">
              {{ formatUnit(record.unit) }}
            </template>
          </template>
        </s-table>
        <div  v-else  style="width: 100%;height: 200px;display: flex;justify-content: center;align-items: center">
          <a-spin tip="数据加载中..." size="small"></a-spin>
        </div>

      </div>

      <!-- 分页 -->
      <div class=cs-pagination>
<!--        <div class="cs-margin-right cs-list-total-data ">-->
<!--          总数量：{{formatNumber(totalData.qtyTotal)}} ，总金额：{{formatNumber(totalData.decTotal)}}-->
<!--        </div>-->
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer  :page-size="page.pageSize" :total="page.total"  @change="onPageChange">
          <template #buildOptionText="props">
            <span >{{ props.value }}条/页</span>
          </template>
        </a-pagination>

      </div>
    </div>

<!--    &lt;!&ndash; 新增 编辑数据 &ndash;&gt;-->
<!--    <div v-if="!show">-->
<!--      <biz-i-order-list-edit :editConfig="editConfig" @on-back="handlerOnBack" />-->
<!--    </div>-->

  </section>


</template>
 <script>
 import { defineComponent, createVNode, onMounted, reactive, ref, watch } from 'vue'
 import { message, Modal } from 'ant-design-vue'
 import ExclamationCircleOutlined from '@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined'
 import { getColumns } from "@/view/warehouse/storeEHead/list/BizStoreEListColumns"
 import { localeContent } from "@/view/utils/commonUtil"
 import ycCsApi from "@/api/ycCsApi"
 import { editStatus } from "@/view/common/constant"
 import BizIOrderListEdit from "@/view/dec/imported_cigarettes/list/BizIOrderListEdit.vue"
 import { deleteStoreEListList, getITotal, getStoreEListListBySid, updateStoreEListList
 } from "@/api/cs_api_constant"
 import { useCommon } from '@/view/common/useCommon'
 import {useColumnsRender} from "@/view/common/useColumnsRender";
 import useEventBus from "@/view/common/eventBus";
 import {isNullOrEmpty} from "@/view/utils/common";


 export default defineComponent({
   name: 'BizStoreEListList',
   methods: {isNullOrEmpty},
   components: {
     BizIOrderListEdit
   },
   props: {
     headId: {
       type: String,
       default: () => ''
     },
     isEdit: {
       type: Boolean,
       default: () => false
     },
     showDisable: {
       type: Boolean,
       default: () => false
     },
     unitOptions: {
       type: Array,
       default: () => []
     }
   },
   setup(props) {
     const { inputFormatter,inputParser,formatNumber}  = useColumnsRender()
     // 从useCommon中解构需要的属性
     const {
       editConfig,
       show,
       page,
       dataSourceList,
       tableLoading,
       getTableScroll,
       doExport
     } = useCommon()

     // 响应式状态
     const totalData = ref({
       qtyTotal: 0,
       decTotal: 0
     })
     const tableHeight = ref('')
     const deleteLoading = ref(false)
     const orderListRef = ref(null)
     const tableRef = ref(null)

     const gridData = reactive({
       selectedRowKeys: [],
       selectedData: [],
       loading: false
     })

     const formatUnit = (code) => {
       const option = props.unitOptions.find(opt => opt.value === code);
       return option ? option.label : code;
     }


     // 获取列配置
     const { totalColumns } = getColumns()

     // 方法定义
     const getList = () => {
       console.log('headid',props.headId)
       tableLoading.value = true
       window.majesty.httpUtil.postAction(`${ycCsApi.warehouseUrl.bizStoreEList.list}?page=${page.current}&limit=${page.pageSize}`,
         { headId: props.headId }
       ).then(res => {
         dataSourceList.value = res.data
         page.total = res.total
       }).finally(() => {
         tableLoading.value = false
       })
     }

     const onPageChange = (pageNumber, pageSize) => {
       page.current = pageNumber
       page.pageSize = pageSize
       getList()
     }

     const initIListSumData = () => {
       getITotal({ headId: props.headId }).then(res => {
         if (res.code === 200) {
           totalData.value = { ...res.data }
         } else {
           message.error(res.message)
         }
       })
     }

     const handlerOnBack = (flag) => {
       show.value = !show.value
       gridData.selectedData = []
       gridData.selectedRowKeys = []
       editConfig.editData = {}
       if (flag) {
         getList()
       }
     }

     const handlerDelete = () => {
       if (gridData.selectedRowKeys.length <= 0) {
         message.warning('请选择一条数据')
         return
       }
       Modal.confirm({
         title: '提醒?',
         icon: createVNode(ExclamationCircleOutlined),
         okText: '删除',
         cancelText: '取消',
         content: '确认删除所选项吗？',
         onOk() {
           deleteLoading.value = true
           deleteStoreEListList(gridData.selectedRowKeys).then(res => {
             if (res.code === 200) {
               message.success("删除成功！")
               getList()
               // initIListSumData()
             } else {
               message.error(res.message)
             }
           }).finally(() => {
             deleteLoading.value = false
           })
         }
       })
     }

     const handleBlur = (save, closeEditor) => {
       save()
       closeEditor()
       getList()
       // initIListSumData()
     }

     const handleEnter = (save, closeEditor, record) => {
       // 保存数据
       save()
       // 关闭编辑器
       closeEditor()

       // 检查数据变更并更新
       // if (record && record.sid) {
       //   getOrderListBySid(record.sid, record).then(res => {
       //     if (res.code === 200) {
       //       // 判断值是否发生变化 qty
       //       if (isNullOrEmpty(res.data) || res.data.qty === record.qty) {
       //         return
       //       }
       //       updateBizErpIOrderList(record.sid, record).then(res => {
       //         if (res.code === 200) {
       //           message.success("修改成功！")
       //         } else {
       //           message.error(res.message)
       //         }
       //       }).finally(() => {
       //         getList()
       //         initIListSumData()
       //       })
       //     }
       //   })
       // }
     }

     const isEditLoading = ref(false)
     // 处理数量变更
     const handleQuantityChange = (record) => {
       if (isEditLoading.value === true){
         console.log('回车，失焦同时触发！')
         return
       }
       isEditLoading.value = true
       if (record && record.sid) {
         getStoreEListListBySid(record.sid, record).then(res => {
           if (res.code === 200) {
             let dataTemp = res.data
             // 判断值是否发生变化 qty
             if (isNullOrEmpty(res.data) || (res.data.qtyIss === record.qtyIss && res.data.qtyDeli === record.qtyDeli)) {
               return
             }
             updateStoreEListList(record.sid, record).then(res => {
               if (res.code === 200) {
                 message.success("修改成功！")
               } else {
                 // 恢复原先的值
                 record.qtyIss = dataTemp.qtyIss
                 record.qtyDeli = dataTemp.qtyDeli
                 message.error(res.message)
               }
             }).finally(() => {
               // initIListSumData()
             })
           }
         })
       }
       setTimeout(() => {
         isEditLoading.value = false
       }, 100)
     }

     const handlerExport = () => {
       doExport('测试文件导出.xlsx', totalColumns)
     }


     const {onEvent} = useEventBus()

     onEvent('refreshOrderList',()=>{
       // 刷新列表
       getList()
       // initIListSumData()
     })



     // 生命周期钩子
     onMounted(() => {
       // tableHeight.value = getTableScroll(100, '')
       getList()
       // initIListSumData()
     })

     // 暴露给模板的内容
     return {
       // 是否正在编辑
       isEditLoading,
       // 状态
       totalColumns,
       totalData,
       tableHeight,
       deleteLoading,
       orderListRef,
       tableRef,
       gridData,
       props,

       // 来自useCommon
       editConfig,
       show,
       page,
       dataSourceList,
       tableLoading,

       // 方法
       getList,
       onPageChange,
       // initIListSumData,
       handlerOnBack,
       handlerDelete,
       handleBlur,
       handleEnter,
       handleQuantityChange,
       handlerExport,
       localeContent,
       onSelectChange: (selectedRowKeys, rowSelectData) => {
         gridData.selectedData = rowSelectData
         gridData.selectedRowKeys = selectedRowKeys
       },
       inputFormatter,
       inputParser,
       formatNumber,
       onEvent,
       formatUnit
     }
   }
 })
 </script>

<style lang="less" scoped>
.editable-cell {
  position: relative;
  .editable-cell-input-wrapper,
  .editable-cell-text-wrapper {
    padding-right: 24px;
  }

  .editable-cell-text-wrapper {
    padding: 5px 24px 5px 5px;
  }

  .editable-cell-icon,
  .editable-cell-icon-check {
    position: absolute;
    right: 0;
    width: 20px;
    cursor: pointer;
  }

  .editable-cell-icon {
    margin-top: 4px;
    display: inline-block;
    // display: none;
  }

  .editable-cell-icon-check {
    margin-top: 8px;
  }

  .editable-cell-icon:hover,
  .editable-cell-icon-check:hover {
    color: #108ee9;
  }

  .editable-add-btn {
    margin-bottom: 8px;
  }
}
.editable-cell:hover .editable-cell-icon {
  display: inline-block;
}

/* 弹框表格样式 */
.cs-action-item-modal-table {
  padding: 4px 0;
  margin: 2px 0;
  background: #fff;
  box-sizing: border-box; /* 确保 padding 不会撑大容器 */
  // min-height: calc(100vh - 300px);
  min-height: calc(40vh);
  height: auto;
  .surely-table-body{
    // min-height: calc(100vh - 300px);
    min-height: calc(40vh);
  }
}


</style>
