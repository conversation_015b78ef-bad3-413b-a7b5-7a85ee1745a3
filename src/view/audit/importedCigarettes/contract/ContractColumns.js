import { h, reactive, ref } from 'vue'
import { Tag } from 'ant-design-vue'
import { baseColumns, createSorter, createDateSorter, createNumberSorter } from "@/view/common/baseColumns";
import { productClassify } from '@/view/common/constant'
import { useColumnsRender } from "../../../common/useColumnsRender";
import { useMerchant } from "@/view/common/useMerchant";

// 格式化数字为千分位分隔的工具函数
const formatNumber = (value) => {
  if (value === undefined || value === null || value === '') {
    return '';
  }
  // 将数字转换为字符串并添加千分位分隔符
  // 检查是否有小数部分
  const numValue = Number(value);
  if (Number.isNaN(numValue)) {
    return '';
  }

  const hasDecimal = numValue % 1 !== 0;
  if (hasDecimal) {
    // 如果有小数，保留原有小数位数
    return new Intl.NumberFormat('zh-CN').format(numValue);
  } else {
    // 如果没有小数，补充.00
    return new Intl.NumberFormat('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(numValue);
  }
};

const { baseColumnsExport, baseColumnsShow } = baseColumns()
const { cmbShowRender } = useColumnsRender()
const { merchantOptions, getMerchantOptions } = useMerchant()

// 初始化时获取供应商数据
await getMerchantOptions()

function getColumns() {
  const commColumns = reactive([
    'businessType',
    'planNo',
    'planYear',
    'halfYear',
    'buyer',
    'seller',
    'contractNo',
    'contractEffectiveDate',
    'contractExpiryDate',
    'signDate',
    'loadingPort',
    'arrivalPort',
    'tradeTerms',
    'priceTermPort',
    'exportCountry',
    'totalAmount',
    'totalQuantity',
    'shortOverPercent',
    'note',
    'preparedBy',
    'prepareTime',
    'dataStatus',
    'confirmTime',
    'approvalStatus',
    'versionNo'
  ])

  // 导出字段设置
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])

  // table表格字段设置
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  // table表格字段设置
  const totalColumns = ref([
    {
      title: '操作',
      maxWidth: 80,
      width: 80,
      dataIndex: 'operation',
      key: 'operation',
      align: 'center',
      fixed: 'left',
    },
    {
      title: '合同号',
      width: 180,
      minWidth: 180,
      align: 'center',
      dataIndex: 'contractNo',
      resizable: true,
      key: 'contractNo',
      ...createSorter('contractNo')
    },
    {
      title: '供应商',
      width: 200,
      minWidth: 200,
      align: 'center',
      dataIndex: 'seller',
      resizable: true,
      key: 'seller',
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text, merchantOptions.value))
      }
    },
    {
      title: '合同总金额',
      width: 150,
      minWidth: 150,
      align: 'center',
      dataIndex: 'totalAmount',
      resizable: true,
      key: 'totalAmount',
      ...createNumberSorter('totalAmount'),
      customRender: ({ text }) => {
        return formatNumber(text);
      }
    },
    {
      title: '合同总数量',
      width: 150,
      minWidth: 150,
      align: 'center',
      dataIndex: 'totalQuantity',
      resizable: true,
      key: 'totalQuantity',
      customRender: ({ text }) => {
        return formatNumber(text);
      }
    },
    {
      title: '计划编号',
      width: 150,
      minWidth: 150,
      align: 'center',
      dataIndex: 'planNo',
      key: 'planNo',
      resizable: true,
    },
    {
      title: '计划年度',
      width: 150,
      minWidth: 150,
      align: 'center',
      dataIndex: 'planYear',
      key: 'planYear',
      resizable: true,
      customRender: ({ text }) => {
        // 如果有值且包含年份，则只截取年份部分
        if (text && typeof text === 'string') {
          // 尝试提取年份（假设格式为yyyy或yyyy-mm-dd等包含年份的格式）
          const yearMatch = text.match(/^(\d{4})/);
          return yearMatch ? yearMatch[1] : text;
        }
        return text;
      }
    },
    {
      title: '上下半年',
      width: 150,
      minWidth: 150,
      align: 'center',
      dataIndex: 'halfYear',
      key: 'halfYear',
      resizable: true,
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,productClassify.yearConst))
      }
    },
    {
      title: '客户',
      width: 200,
      minWidth: 200,
      align: 'center',
      dataIndex: 'buyer',
      resizable: true,
      key: 'buyer',
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text, merchantOptions.value))
      }
    },
    {
      title: '合同生效期',
      width: 150,
      minWidth: 150,
      align: 'center',
      dataIndex: 'contractEffectiveDate',
      resizable: true,
      key: 'contractEffectiveDate',
      ...createDateSorter('contractEffectiveDate')
    },
    {
      title: '合同有效期',
      width: 150,
      minWidth: 150,
      align: 'center',
      dataIndex: 'contractExpiryDate',
      resizable: true,
      key: 'contractExpiryDate',
      customRender: ({ text }) => {
        return h('span', text ? text.slice(0, 10) : text)
      }
    },
    {
      title: '签约日期',
      width: 150,
      minWidth: 150,
      align: 'center',
      dataIndex: 'signDate',
      resizable: true,
      key: 'signDate',
      customRender: ({ text }) => {
        return h('span', text ? text.slice(0, 10) : text)
      }
    },
    {
      title: '装货港',
      width: 150,
      minWidth: 150,
      align: 'center',
      dataIndex: 'loadingPort',
      key: 'loadingPort',
      resizable: true,
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,[],'PORT_LIN'))
      }
    },
    {
      title: '到货港',
      width: 150,
      minWidth: 150,
      align: 'center',
      dataIndex: 'arrivalPort',
      key: 'arrivalPort',
      resizable: true,
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,[],'PORT_LIN'))
      }
    },
    {
      title: '贸易条款',
      width: 150,
      minWidth: 150,
      align: 'center',
      dataIndex: 'tradeTerms',
      resizable: true,
      key: 'tradeTerms'
    },
    {
      title: '价格条款对应的港口',
      width: 200,
      minWidth: 200,
      align: 'center',
      dataIndex: 'priceTermPort',
      resizable: true,
      key: 'priceTermPort',
      // customRender: ({ text }) => {
      //   return h(<div></div>, cmbShowRender(text,productClassify.PricePortOptions))
      // }
    },
    {
      title: '出口国家(地区)',
      width: 150,
      minWidth: 150,
      align: 'center',
      dataIndex: 'exportCountry',
      key: 'exportCountry',
      resizable: true,
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,[],'COUNTRY_OUTDATED'))
      }
    },
    {
      title: '短溢数%',
      width: 150,
      minWidth: 150,
      align: 'center',
      dataIndex: 'shortOverPercent',
      resizable: true,
      key: 'shortOverPercent',
      customRender: ({ text }) => {
        return text !== undefined && text !== null ? `${formatNumber(text)}%` : '';
      }
    },
    {
      title: '备注',
      width: 150,
      minWidth: 150,
      align: 'center',
      dataIndex: 'note',
      resizable: true,
      key: 'note'
    },
    {
      title: '制单人',
      width: 150,
      minWidth: 150,
      align: 'center',
      dataIndex: 'preparedBy',
      resizable: true,
      key: 'preparedBy'
    },
    {
      title: '制单时间',
      width: 150,
      minWidth: 150,
      align: 'center',
      dataIndex: 'prepareTime',
      resizable: true,
      key: 'prepareTime'
    },
    {
      title: '单据状态',
      width: 150,
      minWidth: 150,
      align: 'center',
      dataIndex: 'dataStatus',
      resizable: true,
      key: 'dataStatus',
      customRender: ({ text }) => {
        const tagColor = text === '2' ? 'error' : 'success';
        return h(Tag, { color: tagColor }, cmbShowRender(text, productClassify.data_status))
      }
    },
    {
      title: '确认时间',
      width: 150,
      minWidth: 150,
      align: 'center',
      dataIndex: 'confirmTime',
      resizable: true,
      key: 'confirmTime'
    },
    {
      title: '审批状态',
      width: 150,
      minWidth: 150,
      align: 'center',
      dataIndex: 'approvalStatus',
      resizable: true,
      key: 'approvalStatus',
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,productClassify.approval_status))
      }
    },
    {
      title: '版本号',
      width: 150,
      minWidth: 150,
      align: 'center',
      resizable: true,
      dataIndex: 'versionNo',
      key: 'versionNo'
    },
    {
      title: '业务类型',
      width: 150,
      minWidth: 150,
      align: 'center',
      dataIndex: 'businessType',
      key: 'businessType',
      resizable: true,
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,productClassify.businessType))
      }

    },
  ])

  return {
    columnsConfig,
    excelColumnsConfig,
    totalColumns
  }
}

export { getColumns }
