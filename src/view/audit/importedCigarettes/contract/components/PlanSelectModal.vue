<template>
  <a-modal
      v-model:visible="props.visible"
      title="选择计划"
      width="1000px"
      @ok="handleOk"
      @cancel="handleCancel"
      :maskClosable="false"
      :keyboard="false"
      okText="保存"
      cancelText="关闭"
  >
    <!-- 查询条件 -->
    <div class="cs-search">
      <a-form layout="inline" :model="searchForm">
        <a-form-item label="计划编号">
          <a-input
              v-model:value="searchForm.planNo"
              placeholder="请输入计划编号"
              allow-clear
              style="width: 200px"
          />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="handleSearch">查询</a-button>
          <a-button style="margin-left: 8px" @click="handleReset">重置</a-button>
        </a-form-item>
      </a-form>
    </div>

    <!-- 计划列表 -->
    <div class="table-container" :style="{ minHeight: tableHeight + 'px' }">
      <s-table
          v-if="loading || planData.length > 0"
          ref="planTableRef"
          class="cs-action-item"
          size="small"
          :scroll="{ y: tableHeight }"
          bordered
          :columns="columns"
          :data-source="planData"
          :row-key="getRowKey"
          :pagination="false"
          :loading="loading"
          :row-selection="{
          type: 'radio',
          selectedRowKeys: selectedKeys,
          onChange: onSelectChange
        }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'planYear'">
            {{ formatDate(record.planYear) }}
          </template>
          <template v-if="column.dataIndex === 'halfYear'">
            {{ formatHalfYear(record.halfYear) }}
          </template>
          <template v-if="column.dataIndex === 'seller'">
            {{ sellerValueFormat(record.seller) }}
          </template>
        </template>
      </s-table>
      <div v-else class="empty-data">暂无数据</div>
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import ycCsApi from "@/api/ycCsApi";
import {insertContract} from "@/api/importedCigarettes/contract/contractApi";
import { productClassify } from '@/view/common/constant';
import { useColumnsRender } from '@/view/common/useColumnsRender';
import { useMerchant } from "@/view/common/useMerchant"

const { cmbShowRender } = useColumnsRender();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible', 'select']);

// 计划列表数据
const planData = ref([]);
const selectedKeys = ref([]);
const selectedPlan = ref(null);
const loading = ref(false);
const tableHeight = ref(300);

// 查询表单
const searchForm = reactive({
  planNo: ''
});

// 获取供应商数据
const { merchantOptions, getMerchantOptions } = useMerchant()

// 列定义
const columns = [
  {
    title: '计划编号',
    dataIndex: 'planNo',
    width: 150
  },
  {
    title: '计划年度',
    dataIndex: 'planYear',
    width: 100
  },
  {
    title: '上下半年',
    dataIndex: 'halfYear',
    width: 100
  },
  {
    title: '供应商',
    dataIndex: 'seller',
    width: 200
  },
  {
    title: '计划数量',
    dataIndex: 'planQuantity',
    width: 150
  }
];

// 格式化日期
const formatDate = (date) => {
  if (!date) return '';
  const yearMatch = date.match(/^(\d{4})/);
  return yearMatch ? yearMatch[1] : date;
};


// 格式化上下半年
const formatHalfYear = (halfYear) => {
  if (!halfYear && halfYear !== '0') return '';
  return cmbShowRender(halfYear, productClassify.yearConst);
};

const sellerValueFormat = (value) => {
  if (!value) return '';
  return cmbShowRender(value, merchantOptions.value);
}

// 生成行唯一标识
const getRowKey = (record) => {
  return `${record.planNo}_${record.seller}`;
};

// 获取计划列表
const getPlanList = async () => {
  loading.value = true;
  try {
    const params = {
      planNo: searchForm.planNo
    };
    const res = await window.majesty.httpUtil.postAction(
        `${ycCsApi.importedCigarettes.contract.planList}`,
        params
    );
    if (res.code === 200) {
      planData.value = res.data || [];
    } else {
      planData.value = [];
      message.error(res.message || '获取计划列表失败');
    }
  } catch (error) {
    planData.value = [];
    message.error('获取计划列表失败');
  } finally {
    loading.value = false;
  }
};

// 处理查询
const handleSearch = () => {
  selectedKeys.value = [];
  selectedPlan.value = null;
  getPlanList();
};

// 处理重置
const handleReset = () => {
  searchForm.planNo = '';
  selectedKeys.value = [];
  selectedPlan.value = null;
  getPlanList();
};

// 处理选择
const onSelectChange = (selectedRowKeys, selectedRows) => {
  selectedKeys.value = selectedRowKeys;
  selectedPlan.value = selectedRows[0];
};

// 处理确认
const handleOk = () => {
  if (!selectedPlan.value) {
    message.warning('请选择一条计划');
    return;
  }
  const params = {
    planNo: selectedPlan.value
  };
  try {
    insertContract(params).then((res)=>{
      if (res.code === 200){
        message.success('新增成功!')
        emit('select', res.data);
        handleCancel();
      } else {
        message.error(res.message);
      }
    })
  } catch (error) {
    console.error('新增失败', error)
  }
};

// 处理取消
const handleCancel = () => {
  selectedKeys.value = [];
  selectedPlan.value = null;
  planData.value = [];
  emit('update:visible', false);
};

// 监听visible变化，当显示时加载数据
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 设置表格高度
    tableHeight.value = 300;
    getPlanList();
  } else {
    planData.value = [];
    selectedKeys.value = [];
    selectedPlan.value = null;
  }
});

// 组件卸载时清理数据
onMounted(() => {
  planData.value = [];
  selectedKeys.value = [];
  selectedPlan.value = null;
  getMerchantOptions()
});
</script>

<style lang="less" scoped>
.cs-search {
  margin-bottom: 16px;
}

.table-container {
  position: relative;
  min-height: 300px;
}

.empty-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 200px;
  color: #999;
  font-size: 14px;
}

:deep(.ant-table-wrapper) {
  .ant-table {
    .ant-table-row {
      &.ant-table-row-selected > td {
        background-color: #e6f7ff;
      }
      &:hover > td {
        background-color: #fafafa;
      }
    }
  }
}

:deep(.ant-radio-wrapper) {
  .ant-radio-checked {
    .ant-radio-inner {
      border-color: #1890ff;
      &::after {
        background-color: #1890ff;
      }
    }
  }
}
</style>
