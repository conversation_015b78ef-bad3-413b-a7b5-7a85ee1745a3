<template>
  <section>
    <a-card size="small" title="进口合同信息" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData" class="grid-container">
          <!-- 业务类型 -->
          <a-form-item name="businessType" class="grid-item" :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('businessType')"
                @click="handleLabelClick('businessType')"
              >
                业务类型
              </span>
            </template>
            <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.businessType" id="businessType">
              <a-select-option v-for="item in productClassify.businessType" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 计划编号 -->
          <a-form-item name="planNo" class="grid-item" :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('planNo')"
                @click="handleLabelClick('planNo')"
              >
                计划编号
              </span>
            </template>
            <a-input disabled size="small" v-model:value="formData.planNo"/>
          </a-form-item>
          <!-- 计划年度 -->
          <a-form-item name="planYear" class="grid-item" :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('planYear')"
                @click="handleLabelClick('planYear')"
              >
                计划年度
              </span>
            </template>
            <a-date-picker
                disabled
                v-model:value="formData.planYear"
                id="planYear"
                valueFormat="YYYY-MM-DD HH:mm:ss"
                format="YYYY"
                :locale="locale"
                picker="year"
                size="small"
                style="width: 100%"
                placeholder=""
            />
          </a-form-item>
          <!-- 上下半年 -->
          <a-form-item name="halfYear" class="grid-item" :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('halfYear')"
                @click="handleLabelClick('halfYear')"
              >
                上下半年
              </span>
            </template>
            <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.halfYear" id="halfYear">
              <a-select-option v-for="item in productClassify.yearConst" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 客户 -->
          <a-form-item name="buyer" class="grid-item" :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('buyer')"
                @click="handleLabelClick('buyer')"
              >
                客户
              </span>
            </template>
            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.buyer" id="buyer">
              <a-select-option v-for="item in buyerOptions" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 供应商 -->
          <a-form-item name="seller" class="grid-item" :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('seller')"
                @click="handleLabelClick('seller')"
              >
                供应商
              </span>
            </template>
            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.seller" id="seller">
              <a-select-option v-for="item in buyerOptions" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 合同编号 -->
          <a-form-item name="contractNo" class="grid-item" :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('contractNo')"
                @click="handleLabelClick('contractNo')"
              >
                合同号
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.contractNo"/>
          </a-form-item>
          <!-- 合同生效期 -->
          <a-form-item name="contractEffectiveDate" class="grid-item" :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('contractEffectiveDate')"
                @click="handleLabelClick('contractEffectiveDate')"
              >
                合同生效期
              </span>
            </template>
            <a-date-picker
                :disabled="showDisable"
                v-model:value="formData.contractEffectiveDate"
                id="contractEffectiveDate"
                valueFormat="YYYY-MM-DD"
                format="YYYY-MM-DD"
                :locale="locale"
                size="small"
                style="width: 100%"
                placeholder=""
            />
          </a-form-item>
          <!-- 合同有效期 -->
          <a-form-item name="contractExpiryDate" class="grid-item" :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('contractExpiryDate')"
                @click="handleLabelClick('contractExpiryDate')"
              >
                合同有效期
              </span>
            </template>
            <a-date-picker
                :disabled="showDisable"
                v-model:value="formData.contractExpiryDate"
                id="contractExpiryDate"
                valueFormat="YYYY-MM-DD"
                format="YYYY-MM-DD"
                :locale="locale"
                size="small"
                style="width: 100%"
                placeholder=""
            />
          </a-form-item>
          <!-- 签约日期 -->
          <a-form-item name="signDate" class="grid-item" :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('signDate')"
                @click="handleLabelClick('signDate')"
              >
                签约日期
              </span>
            </template>
            <a-date-picker
                :disabled="showDisable"
                v-model:value="formData.signDate"
                id="signDate"
                valueFormat="YYYY-MM-DD"
                format="YYYY-MM-DD"
                :locale="locale"
                size="small"
                style="width: 100%"
                placeholder=""
            />
          </a-form-item>
          <!-- 装货港 -->
          <a-form-item name="loadingPort" class="grid-item" :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('loadingPort')"
                @click="handleLabelClick('loadingPort')"
              >
                装货港
              </span>
            </template>
            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.loadingPort" id="loadingPort">
              <a-select-option v-for="item in customsPortOptions" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 到货港 -->
          <a-form-item name="arrivalPort" class="grid-item" :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('arrivalPort')"
                @click="handleLabelClick('arrivalPort')"
              >
                到货港
              </span>
            </template>
            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.arrivalPort" id="arrivalPort">
              <a-select-option v-for="item in customsPortOptions" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 贸易条款 -->
          <a-form-item name="tradeTerms" class="grid-item" :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('tradeTerms')"
                @click="handleLabelClick('tradeTerms')"
              >
                贸易条款
              </span>
            </template>
            <div class="trade-terms-container" style="display: flex; width: 100%;">
              <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                         v-model:value="formData.tradeTerms" id="tradeTermsSelect" style="flex: 1; margin-right: 5px;">
                <a-select-option v-for="(key,value) in pCode.TRADE_TERMS"  :key="value" :value="value" :label="key">
                  {{value }}
                </a-select-option>
              </cs-select>
              <a-input :disabled="showDisable" size="small" v-model:value="formData.extend1" style="flex: 1;" @change="updateTradeTerms"/>
            </div>
          </a-form-item>

          <!-- 价格条款对应的港口 -->
          <a-form-item name="priceTermPort" class="grid-item" :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('priceTermPort')"
                @click="handleLabelClick('priceTermPort')"
              >
                价格条款对应的港口
              </span>
            </template>
            <a-input :disabled="showDisable" size="small" v-model:value="formData.priceTermPort"/>
<!--            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search-->
<!--                       v-model:value="formData.priceTermPort" id="priceTermPort">-->
<!--              <a-select-option v-for="item in productClassify.PricePortOptions" :key="item.value + ' ' + item.label"-->
<!--                               :value="item.value" :label="item.value + item.label">-->
<!--                {{ item.value }} {{ item.label }}-->
<!--              </a-select-option>-->
<!--            </cs-select>-->
          </a-form-item>
          <!-- 出口国家或地区 -->
          <a-form-item name="exportCountry" class="grid-item" :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('exportCountry')"
                @click="handleLabelClick('exportCountry')"
              >
                出口国家(地区)
              </span>
            </template>
            <cs-select :disabled="showDisable" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.exportCountry" id="exportCountry">
              <a-select-option v-for="item in countryOptions" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 合同总金额 -->
<!--          <a-form-item name="totalAmount" :label="'合同总金额'" class="grid-item" :colon="false">-->
<!--            <a-input-number disabled size="small" v-model:value="formData.totalAmount" style="width: 100%"/>-->
<!--          </a-form-item>-->
<!--          &lt;!&ndash; 合同总数量 &ndash;&gt;-->
<!--          <a-form-item name="totalQuantity" :label="'合同总数量'" class="grid-item" :colon="false">-->
<!--            <a-input-number disabled size="small" v-model:value="formData.totalQuantity" style="width: 100%"/>-->
<!--          </a-form-item>-->
          <!-- 短溢数% -->
          <a-form-item name="shortOverPercent" class="grid-item" :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('shortOverPercent')"
                @click="handleLabelClick('shortOverPercent')"
              >
                短溢数%
              </span>
            </template>
            <a-input-number
              :disabled="showDisable"
              size="small"
              v-model:value="formData.shortOverPercent"
              style="width: 100%"
              :formatter="value => `${value}%`"
              :parser="value => value.replace('%', '')"
            />
          </a-form-item>
          <a-form-item name="note" class="grid-item merge-3" :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('note')"
                @click="handleLabelClick('note')"
              >
                备注
              </span>
            </template>
            <a-textarea :disabled="showDisable" size="small" v-model:value="formData.note"  :autosize="{ minRows: 3, maxRows: 10 }"/>
          </a-form-item>
          <!-- 制单人 -->
          <a-form-item name="preparedBy" class="grid-item" :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('preparedBy')"
                @click="handleLabelClick('preparedBy')"
              >
                制单人
              </span>
            </template>
            <a-input disabled size="small" v-model:value="formData.preparedBy"/>
          </a-form-item>
          <!-- 制单时间 -->
          <a-form-item name="prepareTime" class="grid-item" :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('prepareTime')"
                @click="handleLabelClick('prepareTime')"
              >
                制单时间
              </span>
            </template>
            <a-date-picker
                disabled
                v-model:value="formData.prepareTime"
                id="prepareTime"
                valueFormat="YYYY-MM-DD HH:mm:ss"
                format="YYYY-MM-DD HH:mm:ss"
                :locale="locale"
                size="small"
                style="width: 100%"
                placeholder=""
            />
          </a-form-item>
          <!-- 单据状态 -->
          <a-form-item name="dataStatus" class="grid-item" :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('dataStatus')"
                @click="handleLabelClick('dataStatus')"
              >
                单据状态
              </span>
            </template>
            <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.dataStatus" id="dataStatus">
              <a-select-option v-for="item in productClassify.data_status" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 确认时间 -->
          <a-form-item name="confirmTime" class="grid-item" :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('confirmTime')"
                @click="handleLabelClick('confirmTime')"
              >
                确认时间
              </span>
            </template>
            <a-date-picker
                disabled
                v-model:value="formData.confirmTime"
                id="confirmTime"
                valueFormat="YYYY-MM-DD HH:mm:ss"
                format="YYYY-MM-DD HH:mm:ss"
                :locale="locale"
                size="small"
                style="width: 100%"
                placeholder=""
                showTime
            />
          </a-form-item>
          <!-- 审批状态 -->
          <a-form-item name="approvalStatus" class="grid-item" :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('approvalStatus')"
                @click="handleLabelClick('approvalStatus')"
              >
                审批状态
              </span>
            </template>
            <cs-select disabled optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.approvalStatus" id="approvalStatus">
              <a-select-option v-for="item in productClassify.approval_status" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!-- 版本号 -->
          <a-form-item name="versionNo" class="grid-item" :colon="false">
            <template #label>
              <span
                class="form-label"
                :class="getLabelClass('versionNo')"
                @click="handleLabelClick('versionNo')"
              >
                版本号
              </span>
            </template>
            <a-input disabled size="small" v-model:value="formData.versionNo"/>
          </a-form-item>

          <div class="cs-submit-btn merge-3">
            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right"
                      v-show="props.editConfig.editStatus !== editStatus.SHOW">保存
            </a-button>
<!--            <a-button size="small" type="primary" @click="handlerSaveClose" class="cs-margin-right"-->
<!--                      v-show="props.editConfig.editStatus !== editStatus.SHOW">保存关闭-->
<!--            </a-button>-->
            <a-button size="small" :loading="auditLoading" @click="handlerAudit" class="cs-margin-right"
                      v-show="props.editConfig.editStatus === editStatus.SHOW">
              <template #icon>
                <GlobalIcon type="cloud" style="color:deepskyblue"/>
              </template>
              审核通过
            </a-button>
            <a-button size="small" :loading="invalidLoading" @click="handlerInvalid" class="cs-margin-right"
                      v-show="props.editConfig.editStatus === editStatus.SHOW">
              <template #icon>
                <GlobalIcon type="close-square" style="color:red"/>
              </template>
              审核退回
            </a-button>
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
          </div>
        </a-form>
      </div>
    </a-card>
    <a-card size="small"  class="cs-card-form">
      <contract-detail-table
          ref="detailTableRef"
          :disabled="showDisable"
          :head-id="formData.sid"
          :edit-config="props.editConfig"
          @change="handleDetailChange"
      />
    </a-card>
  </section>
</template>

<style scoped>
.form-label {
  font-weight: bold;
  padding: 2px 6px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-block;
}

.form-label:hover {
  opacity: 0.8;
}

.label-green {
  background-color: #52c41a;
  color: white;
}

.label-red {
  background-color: #ff4d4f;
  color: white;
}

</style>

<script setup>
import {editStatus, productClassify} from '@/view/common/constant'
import {message, Modal} from "ant-design-vue";
import {onMounted, reactive, ref, computed, createVNode} from "vue";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
import {deleteContract, updateContract} from "@/api/importedCigarettes/contract/contractApi";
import ContractDetailTable from './list/ContractDetailTable.vue';
import ycCsApi from "@/api/ycCsApi";
import {useFieldMarking} from '@/utils/useFieldMarking';
import {ExclamationCircleOutlined} from '@ant-design/icons-vue';

const { getPCode } = usePCode()

// 加载状态
const auditLoading = ref(false)
const invalidLoading = ref(false)

// 字段名称映射（英文字段名 -> 中文显示名）
const fieldNameMap = {
  businessType: '业务类型',
  planNo: '计划编号',
  planYear: '计划年度',
  halfYear: '上下半年',
  buyer: '客户',
  seller: '供应商',
  contractNo: '合同号',
  contractEffectiveDate: '合同生效期',
  contractExpiryDate: '合同有效期',
  signDate: '签约日期',
  loadingPort: '装货港',
  arrivalPort: '到货港',
  tradeTerms: '贸易条款',
  priceTermPort: '价格条款对应的港口',
  exportCountry: '出口国家(地区)',
  shortOverPercent: '短溢数%',
  note: '备注',
  preparedBy: '制单人',
  prepareTime: '制单时间',
  dataStatus: '单据状态',
  confirmTime: '确认时间',
  approvalStatus: '审批状态',
  versionNo: '版本号'
}

const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});

// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack']);

//首次新增未保存 删除数据
let firstAddSave = ref(false);

const onBack = (val) => {
  if (props.editConfig.editStatus === editStatus.ADD && !firstAddSave) {
    deleteContract(formData.sid).then(res => {
      emit('onEditBack', val);
    })
  } else {
    emit('onEditBack', val);
    saveCurrentMarkings(formData.sid, 'default', fieldMarkings.value)
  }
};

// 是否禁用
const showDisable = ref(false)

// 表单数据
const formData = reactive({
  businessType: '',
  planNo: '',
  planYear: '',
  halfYear: '',
  buyer: '',
  seller: '',
  contractNo: '',
  contractEffectiveDate: '',
  contractExpiryDate: '',
  signDate: '',
  loadingPort: '',
  arrivalPort: '',
  tradeTerms: '',
  tradeTermsSelect: '',
  extend1: '',
  priceTermPort: '上海',
  exportCountry: '',
  totalAmount: null,
  totalQuantity: null,
  shortOverPercent: null,
  preparedBy: '',
  prepareTime: '',
  dataStatus: '',
  confirmTime: '',
  approvalStatus: '',
  versionNo: ''
})

// 表单校验规则
const rules = {
  businessType: [
    {required: true, message: '请选择业务类型', trigger: 'change'},
    {max: 60, message: '业务类型不能超过60个字符', trigger: 'blur'}
  ],
  planNo: [
    {required: true, message: '请输入计划编号', trigger: 'blur'},
    {max: 60, message: '计划编号不能超过60个字符', trigger: 'blur'}
  ],
  planYear: [
    {required: true, message: '请选择计划年度', trigger: 'change'},
  ],
  halfYear: [
    {required: true, message: '请选择上下半年', trigger: 'change'},
    {max: 10, message: '上下半年不能超过10个字符', trigger: 'blur'}
  ],
  buyer: [
    {required: true, message: '请选择客户', trigger: 'change'},
    {max: 200, message: '客户不能超过200个字符', trigger: 'blur'}
  ],
  seller: [
    {required: true, message: '请选择供应商', trigger: 'change'},
    {max: 200, message: '供应商不能超过200个字符', trigger: 'blur'}
  ],
  contractNo: [
    {required: true, message: '请输入合同编号', trigger: 'blur'},
    {max: 60, message: '合同编号不能超过60个字符', trigger: 'blur'}
  ],
  contractEffectiveDate: [
    {required: true, message: '请选择合同生效期', trigger: 'change'},
    {max: 60, message: '合同生效期不能超过60个字符', trigger: 'blur'}
  ],
  contractExpiryDate: [
    {required: true, message: '请选择合同有效期', trigger: 'change'}
  ],
  signDate: [
    {required: true, message: '请选择签约日期', trigger: 'change'}
  ],
  tradeTerms: [
    {required: true, message: '请选择贸易条款', trigger: 'blur'},
    {max: 50, message: '贸易条款不能超过50个字符', trigger: 'blur'}
  ],
  totalAmount: [
    {required: true, message: '请输入合同总金额', trigger: 'blur'}
  ],
  totalQuantity: [
    {required: true, message: '请输入合同总数量', trigger: 'blur'}
  ],
  preparedBy: [
    {required: true, message: '请输入制单人', trigger: 'blur'},
    {max: 10, message: '制单人不能超过10个字符', trigger: 'blur'}
  ],
  prepareTime: [
    {required: true, message: '请选择制单时间', trigger: 'change'}
  ],
  dataStatus: [
    {required: true, message: '请选择单据状态', trigger: 'change'},
    {max: 10, message: '单据状态不能超过10个字符', trigger: 'blur'}
  ],
  versionNo: [
    {required: true, message: '请输入版本号', trigger: 'blur'},
    {max: 10, message: '版本号不能超过10个字符', trigger: 'blur'}
  ]
}


// 表单引用
const formRef = ref()

// 表格引用
const detailTableRef = ref();

// 使用字段标记功能
const {
  fieldMarkings,
  handleLabelClick,
  getLabelClass,
  setFieldMarkings,
  getFieldMarkings,
  clearFieldMarkings,
  getMarkedFieldsCount,
  saveCurrentMarkings,
  getBySidAndFormType
} = useFieldMarking()

// 处理明细数据变化
const handleDetailChange = (details) => {
  formData.details = details;
};
const handlerSaveClose = async () => {
  try {
    await formRef.value.validate()
    // 根据编辑状态判断是新增还是修改
    if (props.editConfig.editStatus === editStatus.ADD) {
      // 新增逻辑
      updateContract(formData.sid, formData).then((res)=>{
        if (res.code === 200){
          message.success('保存成功')
          firstAddSave = true
          onBack(true)
        } else {
          message.error(res.message);
        }
      })
    } else if (props.editConfig.editStatus === editStatus.EDIT) {
      updateContract(formData.sid, formData).then((res)=>{
        if (res.code === 200){
          message.success('修改成功!')
          onBack(true)
        } else {
          message.error(res.message);
        }
      })
    }
  } catch (error) {
    console.error('表单验证失败', error)
  }
}

// 修改保存处理函数
const handlerSave = async () => {
  try {
    await formRef.value.validate()
    // 根据编辑状态判断是新增还是修改
    if (props.editConfig.editStatus === editStatus.ADD) {
      // 新增逻辑
      updateContract(formData.sid, formData).then((res)=>{
        if (res.code === 200){
          message.success('保存成功')
          firstAddSave = true
          // 保存成功后刷新明细表格数据
          if (detailTableRef.value) {
            detailTableRef.value.reloadData();
          }
        } else {
          message.error(res.message);
        }
      })
    } else if (props.editConfig.editStatus === editStatus.EDIT) {
      updateContract(formData.sid, formData).then((res)=>{
        if (res.code === 200){
          message.success('修改成功!')
          // 保存成功后刷新明细表格数据
          if (detailTableRef.value) {
            detailTableRef.value.reloadData();
          }
        } else {
          message.error(res.message);
        }
      })
    }
  } catch (error) {
    console.error('表单验证失败', error)
  }
}


//基础资料-客商信息
const buyerOptions = reactive([])

const getBuyerOptions = async () => {
  try {
    const params = {}
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.bizMerchant.list}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        buyerOptions.push({
          value: item.merchantCode,
          label: item.merchantNameCn
        });
      });
    } else {
      message.error(res.message || '获取客商数据失败');
    }
  } catch (error) {
    message.error('获取客商数据失败');
  }
}

//海关参数国家
const countryOptions = reactive([]);

const getCountryOptions = async () => {
  const params = {
    paramsType: 'COUNTRY',
  }
  try {
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.importedCigarettes.contract.customsList}/1`,
      params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        countryOptions.push({
          value: item.key,
          label: item.value
        });
      });
    } else {
      message.error(res.message || '获取国家数据失败');
    }
  } catch (error) {
    message.error('获取国家数据失败');
  }
}

//海关参数港口
const customsPortOptions = reactive([]);

const getCustomsPortOptions = async () => {
  const params = {
    paramsType: 'PORT',
  }
  try {
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.importedCigarettes.contract.customsList}/1`,
      params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        customsPortOptions.push({
          value: item.key,
          label: item.value
        });
      });
    } else {
      message.error(res.message || '获取港口数据失败');
    }
  } catch (error) {
    message.error('获取港口数据失败');
  }
}

const pCode = ref('')



// 组件挂载时根据编辑状态设置表单数据和禁用状态
onMounted(() => {
  getPCode().then(res=>{
    console.log('res',res)
    pCode.value = res;
  })
  firstAddSave = false;
  // 获取港口数据
  getCustomsPortOptions();
  // 获取国家数据
  getCountryOptions();
  //获取客商信息
  getBuyerOptions();
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = true
  }
  getBySidAndFormType(formData.sid, 'default')
})

/* 审核通过事件 */
const handlerAudit = () => {
  // 校验是否有红色标识错误的数据
  const redFieldNames = Object.keys(fieldMarkings.value).filter(key => fieldMarkings.value[key] === 'red')
  if (redFieldNames.length > 0) {
    message.error('存在待确认数据，不允许审批通过')
    return
  }

  // 审核意见输入框
  const auditOpinion = ref('同意审批')

  // 弹出审核确认框
  Modal.confirm({
    title: '审核通过',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: createVNode('div', {}, [
      createVNode('div', { style: 'margin-top: 10px;' }, [
        createVNode('label', { style: 'display: block; margin-bottom: 5px;' }, '审核意见：'),
        createVNode('textarea', {
          value: auditOpinion.value,
          onInput: (e) => { auditOpinion.value = e.target.value },
          style: 'width: 100%; height: 80px; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px; resize: vertical;',
          placeholder: '请输入审核意见'
        })
      ])
    ]),
    onOk() {
      auditLoading.value = true
      const params = {
        ids: [formData.sid],
        apprMessage: auditOpinion.value || '同意审批',
        businessType: '1',
        billType: 'contract',
      }

      // 调用audit接口
      window.majesty.httpUtil.postAction(ycCsApi.importedCigarettes.contract.audit, params)
        .then(res => {
          if (res.code === 200) {
            message.success("审核通过成功！")
            // 返回列表页面
            onBack(true)
          } else {
            message.error(res.message || '审核失败')
          }
        })
        .catch(error => {
          console.error('审核失败:', error)
          message.error('审核失败，请重试')
        })
        .finally(() => {
          auditLoading.value = false
        })
    },
    onCancel() {
      // 取消操作
    },
  });
}

/* 审核退回事件 */
const handlerInvalid = () => {
  // 直接读取当前页面的标记信息
  let markedFields = ''
  const redFieldNames = Object.keys(fieldMarkings.value).filter(key => fieldMarkings.value[key] === 'red')
  if (redFieldNames.length > 0) {
    // 将英文字段名转换为中文显示
    const redFieldsChineseNames = redFieldNames.map(field => fieldNameMap[field] || field)
    markedFields = '\n\n标红字段：' + redFieldsChineseNames.join('、')
  }

  // 审核意见输入框
  const auditOpinion = ref('审批退回' + markedFields + '需进一步确认')

  // 弹出审核退回确认框
  Modal.confirm({
    title: '审核退回',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: createVNode('div', {}, [
      createVNode('div', { style: 'margin-top: 10px;' }, [
        createVNode('label', { style: 'display: block; margin-bottom: 5px;' }, '审核意见：'),
        createVNode('textarea', {
          value: auditOpinion.value,
          onInput: (e) => { auditOpinion.value = e.target.value },
          style: 'width: 100%; height: 120px; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px; resize: vertical;',
          placeholder: '请输入审核意见'
        })
      ])
    ]),
    onOk() {
      invalidLoading.value = true
      const params = {
        ids: [formData.sid],
        apprMessage: auditOpinion.value || '审批退回',
        businessType: '1',
        billType: 'contract',
      }

      // 调用audit接口进行退回
      window.majesty.httpUtil.postAction(ycCsApi.importedCigarettes.contract.reject, params)
        .then(res => {
          if (res.code === 200) {
            // 审核退回成功后，调用标记保存接口
            return saveCurrentMarkings(formData.sid, 'default', fieldMarkings.value)
          } else {
            throw new Error(res.message || '审核退回失败')
          }
        })
        .then(res => {
          message.success("审核退回成功！")
          // 返回列表页面
          onBack(true)
        })
        .catch(error => {
          console.error('审核退回失败:', error)
          message.error(error.message || '审核退回失败，请重试')
        })
        .finally(() => {
          invalidLoading.value = false
        })
    },
    onCancel() {
      // 取消操作
    },
  });
}

</script>
