import { ref } from 'vue';
import ycCsApi from '@/api/ycCsApi';

// 这里传入immediate: true 表示立即执行，否则需要手动调用getSupplierList方法
// 根据传入的immediate参数决定动态执行某个具体的方法，该如何修改
export const useIncomingCommon = (options = { immediate: false }) => {
    const supplierList = ref([]);
    const getSupplierList = async () => {
        const params = {};
        const res = await window.majesty.httpUtil.postAction(
            ycCsApi.bizNonInComingHead.getSupplierList,
            params
        );

        if (res.code === 200) {
            supplierList.value = res.data;
        }
    };

    const portList = ref([]);
    const getPortList = async () => {
        const params = {};
        const res = await window.majesty.httpUtil.postAction(
            ycCsApi.bizNonInComingHead.getPortList, params);

        if (res.code === 200) {
            portList.value = res.data;
        }
    };

    const currList = ref([]);
    const getCurrList = async () => {
        const params = {};
        const res = await window.majesty.httpUtil.postAction(
            ycCsApi.bizNonInComingHead.getCurrList, params);

        if (res.code === 200) {
            currList.value = res.data;
        }
    };

    const priceTermList = ref([]);
    const getPriceTermList = async () => {
        const params = {};
        const res = await window.majesty.httpUtil.postAction(
            ycCsApi.bizNonInComingHead.getPriceTermList, params);

        if (res.code === 200) {
            priceTermList.value = res.data;
        }
    };

    // 如果传入 immediate: true，就立即执行
    if (options.immediate) {
        getSupplierList();
        getPortList();
        getCurrList();
        getPriceTermList();
    }

    return {
        supplierList,
        getSupplierList,
        portList,
        getPortList,
        currList,
        getCurrList,
        priceTermList,
        getPriceTermList,
    };
};
