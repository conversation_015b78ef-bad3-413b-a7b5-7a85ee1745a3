<template>
  <a-modal
    :visible="visible"
    @update:visible="handleVisibleChange"
    title="划款信息"
    :width="800"
    @cancel="handleCancel"
    :footer="null"
  >
    <a-form
      ref="formRef"
      labelAlign="right"
      :label-col="{ style: { width: '120px' } }"
      :model="formData"
      class="grid-container"
    >
      <!-- 合同金额 -->
      <a-form-item name="contractAmount" :label="'合同金额'" class="grid-item" :colon="false">
        <a-input-number disabled size="small" v-model:value="formData.contractAmount" style="width: 100%" :precision="2" :formatter="formatNumber" :parser="parseNumber"/>
      </a-form-item>
      <!-- 汇率 -->
      <a-form-item name="exchangeRate" :label="'汇率'" class="grid-item" :colon="false">
        <a-input-number size="small" v-model:value="formData.exchangeRate" style="width: 100%" :precision="2" :formatter="formatNumberWithThousand" :parser="parseNumber"/>
      </a-form-item>
      <!-- 关税税率 -->
      <a-form-item name="tariffRate" :label="'关税税率'" class="grid-item" :colon="false">
        <a-input-number disabled size="small" v-model:value="formData.tariffRate" style="width: 100%" :precision="2" :formatter="formatNumber" :parser="parseNumber"/>
      </a-form-item>
      <!-- 关税金额 -->
      <a-form-item name="tariffAmount" :label="'关税金额'" class="grid-item" :colon="false">
        <a-input-number disabled size="small" v-model:value="formData.tariffAmount" style="width: 100%" :precision="2" :formatter="formatNumber" :parser="parseNumber"/>
      </a-form-item>
      <!-- 消费税率 -->
      <a-form-item name="consumptionTaxRate" :label="'消费税率'" class="grid-item" :colon="false">
        <a-input-number disabled size="small" v-model:value="formData.consumptionTaxRate" style="width: 100%" :precision="2" :formatter="formatNumber" :parser="parseNumber"/>
      </a-form-item>
      <!-- 消费税金额 -->
      <a-form-item name="consumptionTaxAmount" :label="'消费税金额'" class="grid-item" :colon="false">
        <a-input-number disabled size="small" v-model:value="formData.consumptionTaxAmount" style="width: 100%" :precision="2" :formatter="formatNumber" :parser="parseNumber"/>
      </a-form-item>
      <!-- 增值税税率 -->
      <a-form-item name="vatRate" :label="'增值税税率'" class="grid-item" :colon="false">
        <a-input-number disabled size="small" v-model:value="formData.vatRate" style="width: 100%" :precision="2" :formatter="formatNumber" :parser="parseNumber"/>
      </a-form-item>
      <!-- 增值税金额 -->
      <a-form-item name="vatAmount" :label="'增值税金额'" class="grid-item" :colon="false">
        <a-input-number disabled size="small" v-model:value="formData.vatAmount" style="width: 100%" :precision="2" :formatter="formatNumber" :parser="parseNumber"/>
      </a-form-item>
      <!-- 进出口代理费率 -->
      <a-form-item name="importExportAgencyRate" :label="'进出口代理费率'" class="grid-item" :colon="false">
        <a-input-number disabled size="small" v-model:value="formData.importExportAgencyRate" style="width: 100%" :precision="2" :formatter="formatNumber" :parser="parseNumber"/>
      </a-form-item>
      <!-- 进出口代理费 -->
      <a-form-item name="importExportAgencyFee" :label="'进出口代理费'" class="grid-item" :colon="false">
        <a-input-number disabled size="small" v-model:value="formData.importExportAgencyFee" style="width: 100%" :precision="2" :formatter="formatNumber" :parser="parseNumber"/>
      </a-form-item>
      <!-- 总部代理费率 -->
      <a-form-item name="headquartersAgencyRate" :label="'总部代理费率'" class="grid-item" :colon="false">
        <a-input-number disabled size="small" v-model:value="formData.headquartersAgencyRate" style="width: 100%" :precision="2" :formatter="formatNumber" :parser="parseNumber"/>
      </a-form-item>
      <!-- 总部代理费 -->
      <a-form-item name="headquartersAgencyFee" :label="'总部代理费'" class="grid-item" :colon="false">
        <a-input-number disabled size="small" v-model:value="formData.headquartersAgencyFee" style="width: 100%" :precision="2" :formatter="formatNumber" :parser="parseNumber"/>
      </a-form-item>
      <!-- 合同数量 -->
      <a-form-item name="contractQuantity" :label="'合同数量'" class="grid-item" :colon="false">
        <a-input-number disabled size="small" v-model:value="formData.contractQuantity" style="width: 100%" :precision="2" :formatter="formatNumber" :parser="parseNumber"/>
      </a-form-item>
      <!-- 计费重量 -->
      <a-form-item name="billingWeight" :label="'计费重量'" class="grid-item" :colon="false">
        <a-input-number size="small" v-model:value="formData.billingWeight" style="width: 100%" :precision="2" :formatter="formatNumberWithThousand" :parser="parseNumber"/>
      </a-form-item>
      <!-- 通关费 -->
      <a-form-item name="customsClearanceFee" :label="'通关费'" class="grid-item" :colon="false">
        <a-input-number disabled size="small" v-model:value="formData.customsClearanceFee" style="width: 100%" :precision="2" :formatter="formatNumber" :parser="parseNumber"/>
      </a-form-item>
      <!-- 验柜服务费 -->
      <a-form-item name="containerInspectionFee" :label="'验柜服务费'" class="grid-item" :colon="false">
        <a-input-number disabled size="small" v-model:value="formData.containerInspectionFee" style="width: 100%" :precision="2" :formatter="formatNumber" :parser="parseNumber"/>
      </a-form-item>
      <!-- 货运代理费 -->
      <a-form-item name="freightForwarderFee" :label="'货运代理费'" class="grid-item" :colon="false">
        <a-input-number disabled size="small" v-model:value="formData.freightForwarderFee" style="width: 100%" :precision="2" :formatter="formatNumber" :parser="parseNumber"/>
      </a-form-item>
      <!-- 保险费率 -->
      <a-form-item name="insuranceRate" :label="'保险费率'" class="grid-item" :colon="false">
        <a-input-number disabled size="small" v-model:value="formData.insuranceRate" style="width: 100%" :precision="2" :formatter="formatNumber" :parser="parseNumber"/>
      </a-form-item>
      <!-- 保险费 -->
      <a-form-item name="insuranceFee" :label="'保险费'" class="grid-item" :colon="false">
        <a-input-number disabled size="small" v-model:value="formData.insuranceFee" style="width: 100%" :precision="2" :formatter="formatNumber" :parser="parseNumber"/>
      </a-form-item>
      <!-- 人民币划款金额 -->
      <a-form-item name="paymentAmount" :label="'人民币划款金额'" class="grid-item" :colon="false">
        <a-input-number size="small" v-model:value="formData.paymentAmount" style="width: 100%" :precision="2" :formatter="formatNumberWithThousand" :parser="parseNumber"/>
      </a-form-item>
    </a-form>
    <div class="modal-footer" style="display: block">
      <a-button @click="handleSave" type="primary" :loading="loading">保存</a-button>
      <a-dropdown>
        <template #overlay>
          <a-menu @click="handlePrintMenuClick">
            <a-menu-item key=".pdf">打印PDF</a-menu-item>
            <a-menu-item key=".xlsx">打印EXCEL</a-menu-item>
          </a-menu>
        </template>
        <a-button type="primary" :loading="printLoading" style="margin: 0 8px">
<!--          <template #icon>-->
<!--            <GlobalIcon class="btn-icon" type="cloud-download" style="font-size: 12px;"/>-->
<!--          </template>-->
          保存打印
        </a-button>
      </a-dropdown>
      <a-button @click="handleCancel">关闭</a-button>
    </div>
  </a-modal>
</template>

<script setup>
import {ref, reactive, watch, onMounted, nextTick} from 'vue';
import { message } from 'ant-design-vue';
import {
  checkTransferNotice,
  handlerTransferNotice,
  saveTransferNotice
} from '@/api/nonAuxiliaryMaterials/aggrContract/AggrContractApi';
import { useColumnsRender } from '@/view/common/useColumnsRender';
import ycCsApi from "@/api/ycCsApi";

const { toFixedTwo, formatNumber: formatNumberUtil } = useColumnsRender();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:visible', 'refresh']);
const loading = ref(false);
const printLoading = ref(false);

const formData = reactive({
  contractAmount: null,
  exchangeRate: null,
  tariffRate: null,
  tariffAmount: null,
  consumptionTaxRate: null,
  consumptionTaxAmount: null,
  vatRate: null,
  vatAmount: null,
  importExportAgencyRate: null,
  importExportAgencyFee: null,
  headquartersAgencyRate: null,
  headquartersAgencyFee: null,
  contractQuantity: null,
  billingWeight: null,
  customsClearanceFee: null,
  containerInspectionFee: null,
  freightForwarderFee: null,
  insuranceRate: null,
  insuranceFee: null,
  paymentAmount: null,
  isTransferNotice: '0',
});

// 监听数据变化
watch(() => props.data, (newVal) => {
  if (newVal) {
    Object.assign(formData, newVal);
  }
}, { immediate: true, deep: true });

// 监听弹框显示状态
watch(() => props.visible, async (newVal) => {
  if (newVal && props.data) {
    try {
      const res = await handlerTransferNotice(props.data);
      if (res.code === 200) {
        Object.assign(formData, res.data);
      } else {
        message.error(res.message || '获取数据失败');
      }
    } catch (error) {
      message.error('获取数据失败');
    } finally {
    }
  }
});

const handleSave = async () => {
  try {
    loading.value = true;
    const res = await saveTransferNotice(formData);
    if (res.code === 200) {
      message.success('保存成功');
      Object.assign(formData, res.data);
      emit('refresh');
    } else {
      message.error(res.message || '保存失败');
    }
  } catch (error) {
    message.error('保存失败');
  } finally {
    loading.value = false;
  }
};
// 处理打印菜单点击事件
const handlePrintMenuClick = (e) => {
  const printType = e.key; // 'pdf' or 'xlsx'
  handleCheckTransferNotice(printType)
}

const handleCheckTransferNotice = async (printType) => {
  try {
    printLoading.value = true;
    const res = await checkTransferNotice(formData);
    if (res.code === 200) {
      handleSaveAndPrint(printType);
    } else {
      message.error(res.message || '保存失败');
    }
  } catch (error) {
    message.error('保存失败');
  } finally {
    printLoading.value = false;
  }
}

const handleSaveAndPrint = async (fileType) => {

  printLoading.value = true;
  formData.fileType = fileType;
  // const params = {sid:'c1bb54fb-28c6-4e61-856d-cbda164fe6f9',type:'.pdf'}
  const params =formData
  const url = `${ycCsApi.nonAuxiliaryMaterials.bizINonStateAuxmatAggrContractHead.saveTransferNoticePrint}`;

  window.majesty.httpUtil.downloadFile(
    url, null, params, 'post', null
  ).then(res => {
    //downloadStreamFile(res.data, res.headers)
  }).catch(() => {
    message.error(`打印${fileType === 'xlsx' ? 'XLSX' : 'PDF'}失败`);
  }).finally(() => {
    printLoading.value = false;
  })
};



const handleVisibleChange = (val) => {
  emit('update:visible', val);
  if (!val) {
    emit('refresh');
  }
};

const handleCancel = () => {
  emit('update:visible', false);
  emit('refresh');
};

// 格式化数字显示
const formatNumber = (value) => {
  if (!value) return '';
  const formatted = formatNumberUtil(value, true, 2);
  return toFixedTwo(formatted);
};

// 只添加千位分隔符的格式化函数
const formatNumberWithThousand = (value) => {
  if (!value) return '';
  return formatNumberUtil(value, false);
};

// 解析数字输入
const parseNumber = (value) => {
  if (!value) return '';
  return value.replace(/[^0-9.-]/g, '');
};
</script>

<style scoped>
.grid-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.grid-item {
  margin-bottom: 0;
}

.grid-item :deep(.ant-form-item-label) {
  padding-right: 8px;
}

.grid-item :deep(.ant-input-number) {
  width: 100%;
}

.modal-footer {
  text-align: center;
  margin-top: 24px;
}

.modal-footer .ant-btn {
  min-width: 100px;
}
</style>
