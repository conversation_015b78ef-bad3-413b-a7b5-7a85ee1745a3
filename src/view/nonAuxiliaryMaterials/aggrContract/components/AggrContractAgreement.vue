<template>
  <section>
    <a-card size="small" title="协议信息" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData" class="grid-container">
          <!-- 协议编号 -->
          <a-form-item name="agreementNo" :label="'协议编号'" class="grid-item" :colon="false">
            <a-input :disabled="fieldDisabled" size="small" v-model:value="formData.agreementNo"
                     maxlength="60" placeholder="请输入协议编号"/>
          </a-form-item>

          <!-- 签约日期 -->
          <a-form-item name="agreementSigningDate" :label="'签约日期'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="fieldDisabled"
              v-model:value="formData.agreementSigningDate"
              id="agreementSigningDate"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder="请选择签约日期"
            />
          </a-form-item>

          <!-- 代理费率 -->
          <a-form-item name="agreementAgentFeeRate" :label="'代理费率'" class="grid-item" :colon="false">
            <a-input-number
              :disabled="fieldDisabled"
              size="small"
              v-model:value="formData.agreementAgentFeeRate"
              style="width: 100%"
              :precision="2"
              :controls="false"
              :min="0"
              placeholder="请输入代理费率"
              :formatter="formatNumber"
              :parser="parseNumber"
            />
          </a-form-item>

          <!-- 建议授权签约人 -->
          <a-form-item name="suggestedSigner" :label="'建议授权签约人'" class="grid-item" :colon="false">
            <a-input :disabled="fieldDisabled" size="small" v-model:value="formData.suggestedSigner"
                     maxlength="30" placeholder="请输入建议授权签约人"/>
          </a-form-item>

          <!-- 协议条款 -->
          <a-form-item name="agreementTerms" :label="'协议条款'" class="grid-item merge-3" :colon="false">
            <a-textarea :disabled="fieldDisabled" size="small" v-model:value="formData.agreementTerms"
                        :autosize="{ minRows: 4, maxRows: 8 }" maxlength="300"
                        placeholder="请输入协议条款" show-count/>
          </a-form-item>

          <!-- 备注 -->
          <a-form-item name="agreementRemarks" :label="'备注'" class="grid-item merge-3" :colon="false">
            <a-textarea :disabled="fieldDisabled" size="small" v-model:value="formData.agreementRemarks"
                        :autosize="{ minRows: 3, maxRows: 6 }" maxlength="200"
                        placeholder="请输入备注" show-count/>
          </a-form-item>

          <div class="cs-submit-btn merge-3">
            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right"
                      v-show="props.editConfig.editStatus !== editStatus.SHOW" :disabled="fieldDisabled">保存
            </a-button>
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack">返回</a-button>
          </div>
        </a-form>
      </div>
    </a-card>
  </section>
</template>

<script setup>
import {computed, onMounted, reactive, ref, watch} from "vue";
import {editStatus} from "@/view/common/constant";
import {message} from "ant-design-vue";
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import { updateAggrContract } from "@/api/nonAuxiliaryMaterials/aggrContract/AggrContractApi";


defineOptions({
  name: 'AggrContractAgreement'
})

const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  },
  headId: {
    type: String,
    default: ''
  },
  operationStatus: {
    type: String,
    default: ''
  }
});

// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack']);

const onBack = () => {
  emit('onEditBack', true);
};

// 是否禁用
const showDisable = ref(false)

// 计算最终禁用状态
const fieldDisabled = computed(() => {
  return showDisable.value || props.editConfig.editStatus === editStatus.SHOW
})

// 日期本地化
const locale = zhCN;

// 格式化数字为千分位分隔的工具函数
const formatNumber = (value) => {
  if (value === undefined || value === null || value === '') {
    return '';
  }
  // 将数字转换为字符串并添加千分位分隔符
  const numValue = Number(value);
  if (Number.isNaN(numValue)) {
    return '';
  }

  // 保留最多两位小数并添加千位分隔符，不补零
  return new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  }).format(numValue);
};

// 解析数字输入，移除千位分隔符
const parseNumber = (value) => {
  if (!value) return '';
  // 移除千位分隔符，只保留数字和小数点
  return value.replace(/[^0-9.-]/g, '');
};

// 表单引用
const formRef = ref();

// 表单数据 - 使用reactive创建响应式数据
const formData = reactive({
  agreementNo: '',
  agreementSigningDate: '',
  agreementAgentFeeRate: null,
  suggestedSigner: '',
  agreementTerms: '',
  agreementRemarks: ''
})

// 表单校验规则
const rules = {
  agreementNo: [
    {max: 60, message: '协议编号不能超过60个字符', trigger: 'blur'}
  ],
  suggestedSigner: [
    {max: 30, message: '建议授权签约人不能超过30个字符', trigger: 'blur'}
  ],
  agreementTerms: [
    {max: 300, message: '协议条款不能超过300个字符', trigger: 'blur'}
  ],
  agreementRemarks: [
    {max: 200, message: '备注不能超过200个字符', trigger: 'blur'}
  ]
}

// 保存操作
const handlerSave = async () => {
  try {
    // 表单验证
    await formRef.value.validate();

    // 检查是否有headId
    if (!props.headId) {
      message.error('缺少表头数据，无法保存');
      return;
    }

    // 准备保存数据 - 需要包含完整的合同数据，不只是协议字段
    const saveData = {
      // 从父组件获取完整的合同数据
      ...props.editConfig.editData,
      // 更新协议相关字段
      agreementNo: formData.agreementNo,
      agreementSigningDate: formData.agreementSigningDate,
      agreementAgentFeeRate: formData.agreementAgentFeeRate,
      suggestedSigner: formData.suggestedSigner,
      agreementTerms: formData.agreementTerms,
      agreementRemarks: formData.agreementRemarks
    };

    // 调用updateAggrContract接口
    const res = await updateAggrContract(props.headId, saveData);

    if (res.success) {
      // 将后端返回的完整数据同步回父组件的editData
      if (props.editConfig && props.editConfig.editData && res.data) {
        Object.assign(props.editConfig.editData, res.data);
      }

      // 同时更新本地表单数据
      if (res.data) {
        formData.agreementNo = res.data.agreementNo || '';
        formData.agreementSigningDate = res.data.agreementSigningDate || '';
        formData.agreementAgentFeeRate = res.data.agreementAgentFeeRate || null;
        formData.suggestedSigner = res.data.suggestedSigner || '';
        formData.agreementTerms = res.data.agreementTerms || '';
        formData.agreementRemarks = res.data.agreementRemarks || '';
      }

      message.success('协议信息保存成功');
    } else {
      message.error(res.message || '保存失败');
    }

  } catch (error) {
    console.error('保存失败:', error);
    message.error('保存失败，请检查输入信息');
  }
}

// 初始化数据
const initData = () => {
  if (props.editConfig && props.editConfig.editData) {
    const editData = props.editConfig.editData;
    formData.agreementNo = editData.agreementNo || '';
    formData.agreementSigningDate = editData.agreementSigningDate || '';
    formData.agreementAgentFeeRate = editData.agreementAgentFeeRate || null;
    formData.suggestedSigner = editData.suggestedSigner || '';
    formData.agreementTerms = editData.agreementTerms || '';
    formData.agreementRemarks = editData.agreementRemarks || '';
  }
}

// 监听editConfig变化
watch(() => props.editConfig, (newVal) => {
  if (newVal && newVal.editData) {
    initData();
  }
  if (newVal && newVal.editStatus === editStatus.SHOW) {
    showDisable.value = true;
  } else {
    showDisable.value = false;
  }
}, { deep: true, immediate: true })

// 初始化操作
onMounted(() => {
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    showDisable.value = true;
  }
  initData();
})

</script>

<style lang="less" scoped>
.cs-card-form {
  margin-bottom: 16px;
}

.cs-form {
  .grid-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;

    .grid-item {
      margin-bottom: 0;

      &.merge-3 {
        grid-column: span 3;
      }
    }
  }

  .cs-submit-btn {
    display: flex;
    justify-content: center;
    margin-top: 24px;

    .cs-margin-right {
      margin-right: 8px;
    }

    .cs-warning {
      background-color: transparent;
      border-color: #d9d9d9;
      color: rgba(0, 0, 0, 0.88);

      &:hover {
        background-color: #f5f5f5;
        border-color: #d9d9d9;
        color: rgba(0, 0, 0, 0.88);
      }
    }
  }
}
</style>
