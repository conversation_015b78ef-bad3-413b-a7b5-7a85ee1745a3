<template>
  <a-form  layout="inline"  label-align="right"  :label-col="{ style: { width: '100px' } }" :model="searchParam"   class="cs-form  grid-container" >
<!--    业务类型-->
    <a-form-item name="businessType" :label="'业务类型'" class="grid-item" :colon="false">
      <cs-select   optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="searchParam.businessType" id="businessType">
        <a-select-option class="cs-select-dropdown" v-for="item in productClassify.businessType"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
          {{item.value}} {{item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>
<!--    单据状态-->
    <a-form-item name="state" :label="'单据状态'" class="grid-item" :colon="false">
      <cs-select   optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="searchParam.state" id="state">
        <a-select-option class="cs-select-dropdown" v-for="item in productClassify.state"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
          {{item.value}} {{item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>
<!--    收款方-->
    <a-form-item name="payee"   :label="'收款方'" class="grid-item"  :colon="false">
      <cs-select   optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="searchParam.payee" id="payee">
        <a-select-option class="cs-select-dropdown" v-for="item in typeMessage.buyerOptions"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
          {{item.value}} {{item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>
<!--    费用类型-->
    <a-form-item name="expenseType"   :label="'费用类型'" class="grid-item"  :colon="false">
      <cs-select   optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="searchParam.expenseType" id="expenseType">
        <a-select-option class="cs-select-dropdown" v-for="item in typeMessage.costTypeOptions"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
          {{item.value}} {{item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>
<!--    进货单号-->
    <a-form-item name="orderNumber"   :label="'进货单号'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.orderNumber" />
    </a-form-item>
<!--    合同号-->
    <a-form-item name="contractNumber"   :label="'合同号'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.contractNumber" />
    </a-form-item>
<!--    制单日期起止-->
    <a-form-item name="createUserTime" label="制单日期起止" class="grid-item" :colon="false">
      <a-form-item-rest>
        <a-row>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.createUserTimeFrom"
              id="createUserTimeFrom"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
            />
          </a-col>
          <a-col :span="2" style="text-align: center">
            -
          </a-col>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.createUserTimeTo"
              size="small"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD"
              :locale="locale"
              style="width: 100%"
              placeholder=""
            />
          </a-col>
        </a-row>
      </a-form-item-rest>

    </a-form-item>
<!--    制单人-->
    <a-form-item name="createUser"   :label="'制单人'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.createUser" />
    </a-form-item>
  </a-form>
</template>

<script setup>
import {inject, onMounted, reactive} from 'vue'
import {productClassify} from "@/view/common/constant";
import CsSelect from "@/components/select/CsSelect.vue";

defineOptions({
  name: 'CostHeadSearch'
})
const searchParam = reactive({
  businessType:'',
  state:'',
  payee:'',
  expenseType:'',
  orderNumber:'',
  contractNumber:'',
  createUser:'',
  createUserTimeFrom:'',
  createUserTimeTo:'',
})

const props = defineProps({
  typeMessage: {
    type: Object,
    default: () => {
    }
  }
});
/* 定义重置方法(注意前后顺序) */
const resetSearch = () => {
  Object.keys(searchParam).forEach(key => {
    searchParam[key] = '';
  });
}
defineExpose({searchParam,resetSearch});
onMounted(() => {

});





</script>
