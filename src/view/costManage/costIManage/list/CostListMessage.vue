<template>
  <section  class="dc-section">
    <div class="cs-action-btn" style="display: inline-block;">
        <div v-if="props.editConfig.editStatus != 'show'" class="cs-action-btn-item" style="display: inline-block;" v-has="['yc-cs:costIManage:delete']">
          <a-button  size="small" :loading="deleteLoading" @click="handlerDelete">
            <template #icon>
              <GlobalIcon type="delete" style="color:red"/>
            </template>
            {{localeContent('m.common.button.delete')}}
          </a-button>
        </div>
        <div v-if="props.editConfig.editStatus != 'show'" class="cs-action-btn-item" style="display: inline-block;" v-has="['yc-cs:costIManage:add']">
          <a-button size="small" @click="handlerAdd" >
            <template #icon>
              <GlobalIcon type="plus" style="color:green"/>
            </template>
            新增明细
          </a-button>
        </div>
          <div class="cs-action-btn-item" style="display: inline-block;" v-has="['yc-cs:costIManage:export']">
            <a-button  size="small"  :loading="exportLoading" @click="handlerExport">
              <template #icon>
                <GlobalIcon type="cloud-download" style="color:deepskyblue"/>
              </template>
              {{localeContent('m.common.button.export')}}
            </a-button>
          </div>
          <div class="cs-action-btn-item" style="display: inline-block;" v-has="['yc-cs:costIManage:upload']">
            <a-button  size="small" @click="handlerOpenTaxUpload">
              <template #icon>
                <GlobalIcon type="file-text" style="color:#1890ff"/>
              </template>
              读取税金
            </a-button>
          </div>
    </div>
    <!-- 原有内容 -->
    <div style="box-sizing: border-box; ">
      <s-table
        :animate-rows="false"
        :row-selection="props.editConfig.editStatus !=='show' ? { selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange } : null"
        ref="tableRef"
        :columns="columns"
        :scroll="{ y: 900,x:400 }"
        :data-source="dataSourceList"
        :pagination="false"
        bordered
        row-key="sid"
        class="cs-action-item-modal-table remove-table-border-add-bg"
      >

        <template v-if="props.editConfig.editData.state === '0'" #cellEditor="{ column, modelValue, save, closeEditor, editorRef,record }">
          <template v-if="props.editConfig.editStatus !=='show' &&(column.dataIndex === 'taxAmount')">
            <a-input  size="small"
                      :ref="editorRef"
                      v-model:value="modelValue.value"
                      @blur="handleEnterTaxAmount(save, closeEditor,record,modelValue.value)"
                      @keydown.enter="handleEnterTaxAmount(save, closeEditor,record,modelValue.value)"
                      @keydown.esc="closeEditor"
                      style="width: 100%"
                      @input="(event) => {
                        const value = event.target.value;
                        // 正则表达式：最多17位整数，小数点后最多2位
                        const regex = /^\d{0,17}(\.\d{0,2})?$/;
                        if (regex.test(value)) {
                          modelValue.value = value;
                        } else {
                          // 如果不符合要求，截断为符合要求的格式
                          modelValue.value = value.slice(0, -1);
                        }
                      }"
                      maxlength="20"
                      placeholder="请输入最多17位整数和2位小数"/>
          </template>
          <template v-if="props.editConfig.editStatus !=='show' &&(column.dataIndex === 'noTaxAmount')">
            <a-input  size="small"
                      :ref="editorRef"
                      v-model:value="modelValue.value"
                      @blur="handleEnterNoTaxAmount(save, closeEditor,record,modelValue.value)"
                      @keydown.enter="handleEnterNoTaxAmount(save, closeEditor,record,modelValue.value)"
                      @keydown.esc="closeEditor"
                      style="width: 100%"
                      @input="(event) => {
                        const value = event.target.value;
                        // 正则表达式：最多17位整数，小数点后最多2位
                        const regex = /^\d{0,17}(\.\d{0,2})?$/;
                        if (regex.test(value)) {
                          modelValue.value = value;
                        } else {
                          // 如果不符合要求，截断为符合要求的格式
                          modelValue.value = value.slice(0, -1);
                        }
                      }"
                      maxlength="20"
                      placeholder="请输入最多17位整数和2位小数"/>
          </template>
        </template>
      </s-table>
    </div>
    <!-- 分页 -->
    <div class=cs-pagination>
      <div class="cs-margin-right cs-list-total-data ">
        费用金额：{{sumData.expenseAmount}}
      </div>
      <div class="count-number">
        <span>共 {{ page.total }} 条</span>
      </div>
      <a-pagination size="small" v-model:current="page.current" show-size-changer  :page-size="page.pageSize" :total="page.total"  @change="onPageChange">
        <template #buildOptionText="props">
          <span >{{ props.value }}条/页</span>
        </template>
      </a-pagination>

    </div>
    <div>
      <!-- 使用原生 <dialog> 元素 -->
      <cs-modal :visible="open" :title="'新增明细'" style="width: 80%" :footer="false" :closable="false">
        <template #customContent>
          <cost-list-message-tab v-if="modelView" :editConfig="props.editConfig" @onEditBack="handleCancel" :typeMessage="typeMessage" :expenseType="expenseType"/>
        </template>
      </cs-modal>
      <!-- 税金上传组件 -->
      <cs-modal :visible="taxUploadOpen" :title="'读取税金'" style="width: 80%" :footer="false" :closable="false">
        <template #customContent>
          <tax-upload-form v-if="taxUploadView" :editConfig="props.editConfig" @onEditBack="handleTaxUploadCancel" :typeMessage="typeMessage" :expenseType="expenseType"/>
        </template>
      </cs-modal>
    </div>
  </section>
</template>

<script setup>
import CsModal from "@/components/modal/cs-modal.vue";

const open = ref(false)
const taxUploadOpen = ref(false)
import CostListMessageTab from "./CostListMessageTab.vue"
import TaxUploadForm from "./windows/shippingOrder/TaxUploadForm.vue"
import {reactive, watch, defineProps, nextTick, ref, onMounted, createVNode} from 'vue';
import {useCommon} from '@/view/common/useCommon'
import {deleteCostList, selectCostList, UpdateCostList,getSumDataCost} from "@/api/cost/cost_message_info";
import {message, Modal} from "ant-design-vue";
import {getColumns} from "./CostListColumns";
import ycCsApi from "@/api/ycCsApi";
import {localeContent} from "@/view/utils/commonUtil";
import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
import {isNullOrEmpty} from "@/view/utils/common";
import {editStatus} from "@/view/common/constant";

const props = defineProps({
  editConfig: {
    type: Object,
    default: () => ({})
  },
  typeMessage: {
    type: Object,
    default: () => ({})
  }
});
// 响应式数据源
const dataSource = ref([]);

const modelView = ref(false);
const taxUploadView = ref(false);

const expenseType = ref(null);

const tableRef = ref(null);
const tableKey = ref(0);
// 可编辑数据缓存
const editableData = reactive({});
//-------------------------------------------------------------------
// 获取 <dialog> 元素的引用
const modal = ref(null);

// 处理确认操作
const handleConfirm = () => {
  console.log('用户点击了确认');

};

// 处理取消操作
const handleCancel = () => {
  console.log('用户点击了取消');
  modelView.value = false;
  open.value = false;
  getList()
};

// 处理税金上传取消操作
const handleTaxUploadCancel = () => {
  console.log('用户点击了税金上传取消');
  taxUploadView.value = false;
  taxUploadOpen.value = false;
  getList()
};

// 打开税金上传组件
const handlerOpenTaxUpload = () => {
  if(props.editConfig.editData.state !== '0'){
    message.warning('仅编制状态数据允许读取税金！')
    return
  }
  taxUploadView.value = true;
  taxUploadOpen.value = true;
};

//-------------------------------------------------------------------
/* 引入通用方法 */
const {
  tableLoading,
  gridData,
  dataSourceList,
  page
} = useCommon()
// 表格列定义
//如果不为show则将getColumns().commColumns过滤掉 key: 'operation'
const columns = props.editConfig.editStatus !=='show'? getColumns(props.typeMessage).totalColumns : getColumns(props.typeMessage).totalColumns.value.filter(item => item.key!== 'operation');

const columnName = getColumns().commColumns;

const sumData = reactive({
  expenseAmount:0
})

// 定义自定义事件
const emit = defineEmits(['save']);
/* 选中事件(这里的事件，顺序不能乱，必须key,后面点跟实体，不然回显出问题。) */
const onSelectChange = (selectedRowKeys, rowSelectData) => {
  console.log(selectedRowKeys, rowSelectData)
  gridData.selectedData = rowSelectData;
  gridData.selectedRowKeys = selectedRowKeys;
};
//按钮
const deleteLoading = ref(false)
const exportLoading = ref(false)
onMounted(fn => {
  getList();
})

const getList = async () => {
  try {
  //通过sid查询表体信息
  tableLoading.value = true;
  const message = {headId: props.editConfig.editData.sid}
  console.log(message)
  // ycCsApi.costI.Ilist.list
  window.majesty.httpUtil.postAction(`${ycCsApi.costI.Ilist.list}?page=${page.current}&limit=${page.pageSize}`,
    message
  ).then((res)=>{
    if (res.code === 200){
      dataSourceList.value = res.data
      page.total = res.total
      // 返回清空选择数据
      //汇总并去重res.data.expenseType将其装到数组里
      expenseType.value = [...new Set(res.data.map(item => item.expenseType))]
      gridData.selectedData = [];
      gridData.selectedRowKeys = [];
      tableKey.value++;
      // 数据加载完成后，如果不是查看模式，触发编辑器
      // if (props.editConfig.editStatus !== editStatus.SHOW) {
      //   setTimeout(() => {
          triggerEditor();
        // }, 200);
      // }
    }
    tableLoading.value = false;
  }).finally(() => {
    getSumData();
  })
  } catch (error) {
    message.error('获取数据失败'+error);
  }
}
// 监听编辑状态变化，当进入编辑状态时自动触发编辑器
watch(() => props.editConfig.editStatus, (newVal) => {
  if (newVal !== editStatus.SHOW && dataSourceList.value.length > 0) {
    nextTick(() => {
      triggerEditor();
    });
  }
});

// 触发编辑器打开
const triggerEditor = () => {
  if (props.editConfig.editStatus !== editStatus.SHOW) {
    // 确保使用最新的表格数据
    dataSource.value = [...dataSourceList.value];
    // 构建所有行的编辑配置
    const editConfigs = [];
    dataSource.value.forEach(row => {
      editConfigs.push({ columnKey: 'taxAmount', rowKey: row.sid });
      editConfigs.push({ columnKey: 'noTaxAmount', rowKey: row.sid });
    });

    // 使用nextTick确保DOM已更新
    nextTick(() => {
      console.log('触发编辑器，行数:', dataSource.value.length, '编辑配置:', editConfigs);
      if (editConfigs.length > 0) {
        console.log(tableRef)
        // 一次性打开所有单元格的编辑状态
        tableRef.value?.openEditor(editConfigs);
      }
    });
  }
};
const onPageChange =async (pageNumber, pageSize)=> {
  page.current = pageNumber
  page.pageSize = pageSize
  // 在这里添加处理页码变化的逻辑
  await getList()
}

/* 获取进货信息表体汇总数据 */
const getSumData = async ()=>{
  if (isNullOrEmpty(props.editConfig.editData.sid)){
    console.log('headId',props.editConfig.editData.sid)
    return
  }
  const res = await getSumDataCost({headId:props.editConfig.editData.sid})
  if (res.code === 200) {
    Object.assign(sumData,res.data)
  }else {
    message.error(res.message)
  }

}

const handlerAdd = () => {
  // modal.value.showModal(); // 调用 showModal() 方法打开弹框
  if(props.editConfig.editData.state !== '0'){
    message.warning('仅编制状态数据允许新增明细！')
    return
  }
  modelView.value = true;
  open.value = true;
}

const handlerExport = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  const timestamp = `${year}${month}${day}${hours}${minutes}${seconds}`;
  doExport('进口费用('+props.editConfig.editData.documentNumber+')_'+timestamp+'.xlsx',getColumns(props.typeMessage).totalColumns)
}
/* 转换Columns配置 将关键属性转为 key,value形式，并且过滤操作等属性 */
function filterExportParams(params) {
  let tempArr = []
  if (isNullOrEmpty(params)) {
    return []
  }
  params.value.forEach(item => {
    // 如果当前字段中dataIndex是operation 则不导出
    if (item.dataIndex !== 'operation') {
      tempArr.push({
        key: item.dataIndex,
        value: item.title
      })
    }
  })
  return tempArr
}

function doExport(fileName, exportHeader, exportColumns) {
  exportLoading.value = true
  window.window.majesty.httpUtil.downloadFile( "/biz/api/" + 'v1/expenseIList/export',
    fileName,
    {
      exportColumns: {headid : props.editConfig.editData.sid},
      name: fileName,
      header: exportHeader ? filterExportParams(exportHeader) : []
    },
    'post',
    () => {
    }
  ).then((res) => {

  }).finally(() => {
    exportLoading.value = false
  })
}
/* 删除数据 */
const handlerDelete = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if(props.editConfig.editData.state !== '0'){
    message.warning('仅编制状态数据允许删除！')
    return
  }
  // 弹出确认框
  Modal.confirm({
    title: '提醒?',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '删除',
    cancelText: '取消',
    content: '所选中的明细中存在相同合同或进货单号，将一起删除，请确认？',
    onOk() {
      deleteLoading.value = true
      deleteCostList(gridData.selectedRowKeys).then(res => {
        if (res.code === 200) {
          message.success("删除成功！")
            getList()
        }
      }).finally(() => {
        deleteLoading.value = false
      })
    },
    onCancel() {

    },
  });

}

// 编辑操作
const edit = (sid) => {
  console.log('开始编辑',sid);
  editableData[sid] = { ...dataSourceList.value.find(item => item.sid === sid) };
};

// 取消操作
const cancel = (sid) => {
  delete editableData[sid];
};
const save = (key) => {
  console.log('key',key)
  delete editableData[key];
};
/* 双击关闭行内编辑触发事件 */
const handleBlur = (save, closeEditor) => {
  // 这里不要做修改 逻辑直接西在下面写
  // save();
  // closeEditor();
};
const handleEnterNoTaxAmount = (save, closeEditor,record,newValue) => {
  if (newValue === record.noTaxAmount) {
    return;
  }
  // 这里不要做修改 逻辑直接西在下面写
  // save();
  // closeEditor();
  record.noTaxAmount = newValue
  console.log('editorRef',record)
  // emit('save');
  UpdateCostList(record.sid,record).then(res => {
    if (res.code === 200) {
      // message.success("修改成功！")
    }
    if (res.code === 500) {
      // message.error(res.message)
    }
  }).finally(async () => {
    await getList();
  })
};
const handleEnterTaxAmount = (save, closeEditor,record,newValue) => {
  if (newValue === record.taxAmount) {
    return;
  }
  // 这里不要做修改 逻辑直接西在下面写
  // save();
  // closeEditor();
  record.taxAmount = newValue
  console.log('editorRef',record)
  // emit('save');
  UpdateCostList(record.sid,record).then(res => {
    if (res.code === 200) {
      // message.success("修改成功！")
    }
    if (res.code === 500) {
      message.error(res.message)
    }
  }).finally(async () => {
    await getList();
  })
};
</script>

<style lang="less" scoped>
.editable-row-operations {
  display: flex;
  justify-content: space-around;
}

.editable-row-operations a {
  font-size: 12px;
}

///* 弹框样式 */
.modal {
  border: none;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  width: 60%; /* 设置弹框宽度 */
  height: 60%; /* 设置弹框高度 */
  overflow: auto; /* 内容超出时显示滚动条 */
}

/* 弹框遮罩层样式 */
.modal::backdrop {
  background-color: rgba(0, 0, 0, 0.5);
}

/* 弹框操作按钮样式 */
.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

/* 按钮样式 */
button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  opacity: 0.8;
}
</style>
