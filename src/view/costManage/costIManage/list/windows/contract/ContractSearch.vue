<template>
  <a-form  layout="inline"  label-align="right"  :label-col="{ style: { width: '100px' } }" :model="searchParam"   class="cs-form  grid-container" >

<!--    合同号-->
    <a-form-item name="contractNo"   :label="'合同号'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.contractNo" />
    </a-form-item>
<!--    订单号-->
    <a-form-item v-if="props.businessType !== '2'" name="orderNo"   :label="'订单号'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.orderNo" />
    </a-form-item>
  </a-form>
</template>

<script setup>
import {inject, onMounted, reactive} from 'vue'
import {productClassify} from "@/view/common/constant";
import CsSelect from "@/components/select/CsSelect.vue";

defineOptions({
  name: 'ContractSearch'
})
const props = defineProps({
  businessType: {
    type: String
  },
  headId: {
    type: String
  }
})
const searchParam = reactive({
  contractNo:'',
  orderNo:'',
  headId:props.headId,
  businessType:props.businessType
})
/* 定义重置方法(注意前后顺序) */
const resetSearch = () => {
  Object.keys(searchParam).forEach(key => {
    searchParam[key] = '';
  });
}
defineExpose({searchParam,resetSearch});
onMounted(() => {
  console.log(props.headId)

});





</script>
