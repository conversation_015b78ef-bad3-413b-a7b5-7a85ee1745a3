import {baseColumns} from "@/view/common/baseColumns";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {h, reactive, ref} from "vue";
import {productClassify} from "@/view/common/constant";
import {Tag} from "ant-design-vue";
const {baseColumnsExport, baseColumnsShow} = baseColumns()
const { cmbShowRender } = useColumnsRender()

// 格式化数字为千分位分隔的工具函数
const formatNumber = (value) => {
  if (value === undefined || value === null || value === '') {
    return '';
  }
  // 将数字转换为字符串并添加千分位分隔符
  return new Intl.NumberFormat('zh-CN').format(value);
};
export function getColumns(typeMessage) {
  const commColumns = reactive([
    "contractNo"
    ,"orderNo"
    ,"partyA"
    ,"curr"
    ,"decTotal"
    ,"qty"
    ,"unit"
    ,"merchandiseCategories"
  ])

// 导出字段设置`
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])

  // table表格字段设置
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  // table表格字段设置
  const totalColumns1 = ref([
    // 合同号
    {
      title: '合同号',
      width: 220,
      align: 'center',
      dataIndex: 'contractNo',
      key: 'contractNo',
    },
    // 订单号
    {
      title: '订单号',
      width: 220,
      align: 'center',
      dataIndex: 'orderNo',
      key: 'orderNo',
    },
    // 客户名称
    {
      title: '客户名称',
      width: 220,
      align: 'center',
      dataIndex: 'partyA',
      key: 'partyA',
    },
    // 币种
    {
      title: '币种',
      width: 50,
      align: 'center',
      dataIndex: 'curr',
      key: 'curr',
    },
    // 金额
    {
      title: '金额',
      width: 150,
      align: 'center',
      dataIndex: 'decTotal',
      key: 'decTotal',
      customRender: ({ text }) => {
        return formatNumber(text);
      }
    },
    // 数量
    {
      title: '数量',
      width: 150,
      align: 'center',
      dataIndex: 'qty',
      key: 'qty',
      customRender: ({ text }) => {
        return formatNumber(text);
      }
    },
    // 单位
    {
      title: '单位',
      width: 150,
      align: 'center',
      dataIndex: 'unit',
      key: 'unit',
    },
    // 商品类别
    {
      title: '商品类别',
      width: 150,
      align: 'center',
      dataIndex: 'merchandiseCategories',
      key: 'merchandiseCategories',
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,typeMessage.productTypeOptions))
      }
    }
  ])
  const totalColumns2 = ref([
    // 合同号
    {
      title: '合同号',
      width: 220,
      align: 'center',
      dataIndex: 'contractNo',
      key: 'contractNo',
    },
    // 客户名称
    {
      title: '客户名称',
      width: 220,
      align: 'center',
      dataIndex: 'partyA',
      key: 'partyA',
    },
    // 币种
    {
      title: '币种',
      width: 50,
      align: 'center',
      dataIndex: 'curr',
      key: 'curr',
    },
    // 金额
    {
      title: '金额',
      width: 150,
      align: 'center',
      dataIndex: 'decTotal',
      key: 'decTotal',
      customRender: ({ text }) => {
        return formatNumber(text);
      }
    },
    // 数量
    {
      title: '数量',
      width: 150,
      align: 'center',
      dataIndex: 'qty',
      key: 'qty',
      customRender: ({ text }) => {
        return formatNumber(text);
      }
    },
    // 单位
    {
      title: '单位',
      width: 150,
      align: 'center',
      dataIndex: 'unit',
      key: 'unit',
    },
    // 商品类别
    {
      title: '商品类别',
      width: 100,
      align: 'center',
      dataIndex: 'merchandiseCategories',
      key: 'merchandiseCategories',
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,typeMessage.productTypeOptions))
      }
    }
  ])
  return{
    columnsConfig,
    excelColumnsConfig,
    totalColumns1,
    totalColumns2,
    commColumns
  }
}


