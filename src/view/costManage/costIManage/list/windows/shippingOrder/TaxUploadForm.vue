<template>
  <section class="dc-section">
    <div class="cs-action" v-show="show">
      <!-- 查询列表区域 -->
      <div class="cs-search">
        <a-card :bordered="false">
          <bread-crumb>
            <div ref="area_head">
              <div class="search-btn">
                <a-button size="small" type="primary" class="cs-margin-right cs-refresh" @click="handlerRefresh" v-show="showSearch">
                  <template #icon>
                    <GlobalIcon type="redo" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" class="cs-margin-right" @click="handlerSearch">
                  {{localeContent('m.common.button.query')}}
                  <template #icon>
                    <GlobalIcon type="search" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" danger class="cs-margin-right cs-warning" @click="handleShowSearch">
                  <template #icon>
                    <GlobalIcon v-show="!showSearch" type="down" style="color:#fff"/>
                    <GlobalIcon v-show="showSearch" type="up" style="color:#fff"/>
                  </template>
                </a-button>
              </div>
            </div>
          </bread-crumb>
          <div class="separateLine"></div>
          <div ref="area_search" style="padding-bottom: 10px;padding-top: 10px">
            <div v-show="showSearch">
              <ContractSearch ref="headSearch" />
            </div>
          </div>
        </a-card>
      </div>
      <!-- 表格区域 -->
      <div v-if="showColumns && showColumns.length > 0">
        <s-table
          :animate-rows="false"
          ref="tableRef"
          size="small"
          :height="450"
          bordered
          :pagination="false"
          :columns="showColumns.length > 0 ?showColumns:totalColumns"
          :data-source="dataSourceList"
          :row-selection="{  selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          row-key="sid"
        >
        </s-table>
      </div>
      <!-- 分页 -->
      <div class=cs-pagination v-if="showColumns && showColumns.length > 0">
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer :page-size="page.pageSize" :total="page.total" @change="onPageChange">
          <template #buildOptionText="props">
            <span>{{ props.value }}条/页</span>
          </template>
        </a-pagination>
      </div>

      <!-- 附件上传部分 -->
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '90px' } }" :rules="rules"
                :model="formData" class="grid-container">
          <div class="cs-submit-btn merge-3">
            <div style="display: flex;">
              <a-button size="small" :loading="iconLoading" type="primary" @click="handlerSave" class="cs-margin-right">保存</a-button>
              <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
            </div>
          </div>



          <!-- 附件上传 -->
          <a-form-item name="file" :label="'上传附件'" class="grid-item" :colon="false">
            <a-upload
              v-model:file-list="fileList"
              :before-upload="beforeUpload"
              :remove="handleRemove"
              :max-count="1"
              class="upload-container"
              accept=".pdf"
            >
              <a-button>
                <upload-outlined /> 选择文件
              </a-button>
              <div class="upload-tip">仅支持 PDF 格式，文件大小不超过10MB</div>
            </a-upload>
          </a-form-item>
        </a-form>
      </div>
    </div>
  </section>
</template>

<script setup>
/* 使用自定义 Hook 函数 */
import {useCommon} from '@/view/common/useCommon'
import { onMounted, reactive, ref, watch} from "vue";
import ContractSearch from "./ShippingOrderSearch.vue";
import {getColumns} from "./ShippingOrderColumns";
import BreadCrumb from "@/components/breadcrumb/BreadCrumb.vue";
import { UploadOutlined, FilePdfOutlined } from '@ant-design/icons-vue';
import {localeContent} from "@/view/utils/commonUtil";
import ycCsApi from "@/api/ycCsApi";

import {message, Upload} from "ant-design-vue";
import {STable} from "@surely-vue/table";

const props = defineProps({
  editConfig: {
    type: Object,
    default: () => ({})
  },
  typeMessage: {
    type: Object,
    default: () => ({})
  },
  expenseType: {
    type: Object,
    default: () => ({})
  },
});

// 确保在props定义后再使用它们
const { totalColumns } = getColumns(props.typeMessage || {})

/* 引入通用方法 */
const {
  tableLoading,
  gridData,
  page,
  ajaxUrl,
  dataSourceList,
  getList,
  handlerSearch,
  handlerRefresh,
  onPageChange,
  handleShowSearch,
  showSearch,
  show
} = useCommon()

const iconLoading = ref(false);

// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack']);

const onBack = (val) => {
  console.log('返回', val);
  emit('onEditBack', val);
};

// 文件上传相关
const fileList = ref([]);

const formData = reactive({
  file: null,
  taxAmount: '',
  noTaxAmount: '',
  amount: '',
  apportionment: '0',
  methodAllocation: '0'
});


const rules = {
  file: [
    {required: true, message: '请上传文件', trigger: 'blur'},
  ],
};

// 上传前检查
const beforeUpload = (file) => {
  // 检查文件类型
  const isPDF = file.type === 'application/pdf';
  if (!isPDF) {
    message.error('只能上传PDF文件!');
    return Upload.LIST_IGNORE;
  }

  // 检查文件大小
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('文件大小不能超过10MB!');
    return Upload.LIST_IGNORE;
  }

  formData.file = file;
  return false; // 阻止自动上传
};

// 移除文件
const handleRemove = (file) => {
  fileList.value = [];
  formData.file = null;
  formData.taxAmount = '';
  formData.noTaxAmount = '';
  formData.amount = '';
  return true;
};


const expenseTypeArr =ref([]);
/* 显示列数据 */
const showColumns = ref([])
// vue3中使用ref,需要先定义，然后在模板中使用。因为在模板渲染之前，DOM 元素还不存在。
const formRef = ref(null);
onMounted(fn => {
  ajaxUrl.selectAllPage = ycCsApi.costI.Ilist.shippingOrder.list
  // tableHeight.value = getTableScroll(100,'');
  getList()

  // 安全地访问props.typeMessage.costTypeOptions
  if(props.typeMessage && props.typeMessage.costTypeOptions) {
    if(props.expenseType && Array.isArray(props.expenseType)){
      expenseTypeArr.value = props.typeMessage.costTypeOptions.filter(item => !props.expenseType.includes(item.value));
    } else {
      expenseTypeArr.value = props.typeMessage.costTypeOptions;
    }
  }
})
const tableHeight = ref(500)

/* 选中事件(这里的事件，顺序不能乱，必须key,后面点跟实体，不然回显出问题。) */
const onSelectChange = (selectedRowKeys, rowSelectData) => {
  gridData.selectedData = rowSelectData;
  gridData.selectedRowKeys = selectedRowKeys;
};


/* 监控 dataSourceList */
watch(dataSourceList, (newValue, oldValue) => {
  showColumns.value = [...totalColumns.value];
  // 将showColumns的数据属性 和 初始属性进行比对，如果初始属性存在customRender 方法，追加到showColumns中
},{deep:true})

// 保存
const handlerSave = () => {
  formRef.value
    .validate()
    .then(() => {
      iconLoading.value = true;
      if(gridData.selectedRowKeys.length < 1){
        iconLoading.value = false;
        return message.error('请选择进/出货订单!')
      }

      if(!formData.file) {
        iconLoading.value = false;
        return message.error('请选择要上传的文件!');
      }

      // 创建FormData对象用于文件上传
      const formDataObj = new FormData();
      formDataObj.append('file', formData.file);
      // formDataObj.append('taxType', formData.taxType);
      formDataObj.append('headId', props.editConfig.editData.sid);
      formDataObj.append('sids', gridData.selectedRowKeys);

      // 调用上传API
      window.majesty.httpUtil.postAction(
        `${ycCsApi.costI.Ilist.shippingOrder.upload}`,
        formDataObj
      ).then(res => {
        if (res.code === 200) {
          message.success('上传成功');
          onBack(true);
        } else {
          message.error(res.message || '上传失败');
        }
      }).catch(error => {
        console.error('上传失败', error);
        message.error('上传失败');
      }).finally(() => {
        iconLoading.value = false;
      });
    })
    .catch(error => {
      console.log('validate failed', error);
    });
};
</script>

<style lang="less" scoped>
.grid-item {
  margin: 2px; /* 重置外边距 */
  width: 99%;
}

.upload-container {
  width: 100%;
}

.upload-tip {
  color: #999;
  font-size: 12px;
  margin-top: 5px;
  margin-left: 8px;
  display: inline-block;
}
</style>

