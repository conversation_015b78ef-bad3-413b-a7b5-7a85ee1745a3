import {baseColumns} from "@/view/common/baseColumns";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {h, reactive, ref} from "vue";
import {productClassify} from "@/view/common/constant";
import {Tag} from "ant-design-vue";
const {baseColumnsExport, baseColumnsShow} = baseColumns()
const { cmbShowRender } = useColumnsRender()

// 格式化数字为千分位分隔的工具函数
const formatNumber = (value) => {
  if (value === undefined || value === null || value === '') {
    return '';
  }
  // 将数字转换为字符串并添加千分位分隔符
  return new Intl.NumberFormat('zh-CN').format(value);
};
export function getColumns(typeMessage) {

  const commColumns = reactive([
    'businessType',
    'payee',
    'expenseType',
    'contractNumber',
    'orderNumber',
    'curr',
    'totalAmount',
    'advanceFlag',
    'sendUfida',
    'state',
    'createUser',
    'createUserTime',
    'confirmationTime'
  ])

// 导出字段设置`
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])

  // table表格字段设置
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  // table表格字段设置
  const totalColumns = ref([
    {
      width: 80,
      minWidth:80,
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      align: 'center',
      fixed: 'left',
    },
    {
      title: '业务类型',
      width: 150,
      align: 'center',
      dataIndex: 'businessType',
      key: 'businessType',
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,productClassify.businessType))
      }
    },
    {
      title: '收款方',
      width: 220,
      align: 'center',
      dataIndex: 'payee',
      key: 'payee',
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,typeMessage.value.buyerOptions))
      }
    },
    {
      title: '费用类型',
      width: 220,
      align: 'center',
      dataIndex: 'expenseType',
      key: 'expenseType',
      customRender: ({ text }) => {
        // 1. 将费用类型字符串拆分为数组
        const expenseTypes = text ? text.split(',') : [];

        // 2. 去重
        const uniqueExpenseTypes = [...new Set(expenseTypes)];

        // 3. 映射为对应的标签
        const labels = uniqueExpenseTypes.map(type => {
          const option = typeMessage.value.costTypeOptions.find(opt => opt.value === type);
          return option ? option.label : type; // 如果找不到映射，则显示原始值
        });

        // 4. 以逗号分隔显示
        return h('div', labels.join(', '));
      }
    },
    {
      title: '订单号',
      width: 220,
      align: 'center',
      dataIndex: 'contractNumber',
      key: 'contractNumber',
    },
    {
      title: '进/出货单号',
      width: 220,
      align: 'center',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
    },
    {
      title: '币种',
      width: 150,
      align: 'center',
      dataIndex: 'curr',
      key: 'curr',
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,typeMessage.value.currTypeOptions))
      }
    },
    {
      title: '总金额',
      width: 220,
      align: 'center',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      customRender: ({ text }) => {
        return formatNumber(text);
      }
    },
    {
      title: '预付标志',
      width: 150,
      align: 'center',
      dataIndex: 'advanceFlag',
      key: 'advanceFlag',
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,productClassify.isNot))
      }
    },
    {
      title: '发送用友',
      width: 150,
      align: 'center',
      dataIndex: 'sendUfida',
      key: 'sendUfida',
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,productClassify.isNot))
      }
    },
    {
      title: '制单人',
      width: 220,
      align: 'center',
      dataIndex: 'createUser',
      key: 'createUser',
    },
    {
      title: '制单日期',
      width: 220,
      align: 'center',
      dataIndex: 'createUserTime',
      key: 'createUserTime',
    },
    {
      title: '确认时间',
      width: 220,
      align: 'center',
      dataIndex: 'confirmationTime',
      key: 'confirmationTime',
    }
  ])

  return{
    columnsConfig,
    excelColumnsConfig,
    totalColumns
  }
}


