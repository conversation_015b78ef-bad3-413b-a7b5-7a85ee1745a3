<template>
  <div>
    <section>
      <div>HelloWord</div>
      <a-input placeholder="测试测试" v-model:value="testInput"></a-input>
      <a-button @click="csApi">测试接口</a-button>
    </section>
    <a-select
      v-model:value="select"
      :options="options"
      placeholder="ant Pcode案例"
      labelInValue
      style="width: 200px"
      :fieldNames="{label:'text'}"
      :option-label-prop="'label'"
      label-in-value
      @change="handleChange"
    ></a-select>
    <v-select
      :options="options"
    ></v-select>
    <a-button @click="testForm">测试浮云弹窗Form</a-button>
    <a-button @click="testView">测试浮云弹窗View</a-button>
    <a-button @click="testLayout">测试浮云自由组合页面</a-button>
    <a-button @click="testRouter">测试浮云跳转新路由</a-button>
  </div>

</template>

<script>
  import axios from 'axios'
  export default {
    name: 'HelloWorld',
    data() {
      return {
        testInput: '',
        select: '',
        options: []
      }
    },
    mounted() {

      this.pcodeList('CONTAINER_MODEL').then(res => {
        console.log(JSON.stringify(res))
        let options = JSON.parse(JSON.stringify(res));
        options.forEach(o=> {
          if (o.text) {
            o.label = o.text
          }
        })
        this.options = options
      }).catch(err => {

      })

    },
    methods:{
      csApi(){
        window.majesty.httpUtil.getAction('/api/pcode?type=COMPLEX&code=' + (this.testInput ? this.testInput : '0101210010')).then(res => {
          alert(res.data[0]['NAME'])
        })
      },
      /**
       * 弹出框显示页面（供外部系统调用）
       * @param {*} func 操作类型(add / edit / detail)
       * @param {*} formCode  表单编码
       * @param {*} formType 页面类型(form)
       * @param {*} id 表单记录id
       * @param {*} dialogSize 弹框大小(large / middle / small)
       * @param {*} title 标题(会根据多语言进行翻译)
       * @param {*} 回调函数
       * @param {*} param 参数{}（浮云会放在stores里）
       */
      testForm(){
        window.majesty.CommonDialogMixin.methods.bizOpenDialog('add','tHTestForm','form','','large','测试form', this.getFormData, {test: '123'})
      },
      /**
       * 弹出框显示页面（供外部系统调用）
       * @param {*} func 操作类型(add)
       * @param {*} viewCode  页面编码
       * @param {*} formType 页面类型(view)
       * @param {*} multi 是否多选(true/ false)
       * @param {*} dialogSize 弹框大小(large / middle / small)
       * @param {*} title 标题(会根据多语言进行翻译)
       * @param {*} 回调函数
       * @param {*} param 参数{}（浮云会放在stores里）
       */
      testView(){
        window.majesty.CommonDialogMixin.methods.bizOpenDialog('add','kafkaHeadConfig','view',true,'large','测试view', this.getViewData, {test: '123'})
      },
      /**
       * 弹出框显示页面（供外部系统调用）
       * @param {*} func 操作类型(add / edit / detail)
       * @param {*} formCode  自由组合页面编码
       * @param {*} formType 页面类型(dragLayout)
       * @param {*} id 表单记录id
       * @param {*} dialogSize 弹框大小(large / middle / small)
       * @param {*} title 标题(会根据多语言进行翻译)
       * @param {*} param 参数{}（浮云会放在stores里）
       */
      testLayout(){
        window.majesty.CommonDialogMixin.methods.bizOpenDialog('add','kafkaConfig','dragLayout','','large','测试自由组合', {test: '123'})
      },
      testRouter(){
        window.majesty.CommonDialogMixin.methods.bizOpenRouter('/view/redirect/wm-ems-overview')
      },
      /**
       * 表单弹窗提交数据时，提交按钮脚本写法如下：this.$emit('submitAndClose', this.getAnotherFieldsValue())
       * @param data
       */
      getFormData(data) {
        console.log('getFormData', data)
      },
      getViewData(data) {
        console.log('getViewData', data)
      }
    }
  }
</script>
<style lang="less" scoped>
:deep(.ant-select-single .ant-select-selector .ant-select-selection-search) {
  inset-inline-start: auto;
  inset-inline-end: auto;
}
.vs__dropdown-menu {
  z-index: 1021 !important;
}
.vs__search{
  margin:0;
}
.vs--single {
  &:not(.vs--open) .vs__selected + .vs__search {
    width: 0;
    padding: 0;
    margin: 0;
    border: none;
    height: 0;
  }
  .vs__selected {
    color:#021726;
    font-size: 12px;
    //margin-top: 2px;
    display: block;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 100%;
    height: 20px;
    line-height: 20px;
    overflow: hidden;
    margin:0px;
    padding:0px 7px;
  }
  .vs__clear{
    transform: scale(0.8);
    padding-bottom: 4px;
  }
  .vs__search {
    height: 18px;
    margin-top: 2px;
  }
}
.vs__selected-options {
  width: 0;
  padding:0px;
  height:22px;
  overflow-y: auto;
  color: #bbbbbb;
}
.vs__open-indicator{
  transform: scale(0.6);
}
.vs--open .vs__open-indicator {
  transform: rotate(180deg) scale(0.6);
}

.vs__dropdown-toggle {
  height: 24px;
  padding:0px
}

.vs__actions > svg {
  width: 1em;
  height: 1em;
}
.has-error .vs__dropdown-toggle {
  border-color: #f5222d;
}

.vs__dropdown-option--selected:not(.vs__dropdown-option--highlight) {
  background: #dddddd;
  color: #333;
}
.vs--multiple {
  .vs__selected {
    color: #021726;
    font-size: 12px;
    //margin-top: 2px;
    display: block;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 100%;
    height: 20px;
    line-height: 18px;
    overflow: hidden;
    margin: 1px;
    padding: 0px 7px;
  }
  .vs__search {
    height: 18px;
    margin-top: 2px;
  }
}
</style>
<style scoped>

</style>
