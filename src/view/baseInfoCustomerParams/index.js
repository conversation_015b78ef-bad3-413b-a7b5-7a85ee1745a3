import { defineAsyncComponent } from "vue"

export default  [
  {
    path: '/tobacco/customsParams/curr',
    name: 'BaseHeadListCURR',
    meta: {
      title: '币制'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "./curr/BaseHeadList.vue"))
  },
  {
    path: '/tobacco/customsParams/unit',
    name: 'BaseHeadListUNIT',
    meta: {
      title: '单位'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "./unit/BaseHeadList.vue"))
  },
  {
    path: '/tobacco/customsParams/country',
    name: 'BaseHeadListCOUNTRY',
    meta: {
      title: '国别'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "./country/BaseHeadList.vue"))
  },
  {
    path: '/tobacco/customsParams/port',
    name: 'BaseHeadListPORT',
    meta: {
      title: '港口'
    },
    component: defineAsyncComponent(()=>import(/* webpackChunkName: "my-chunk-name" */ "./port/BaseHeadList.vue"))
  }
]
