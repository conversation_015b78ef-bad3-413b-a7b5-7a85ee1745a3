import {baseColumns} from "@/view/common/baseColumns";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {h, reactive, ref} from "vue";
import {productClassify} from "@/view/common/constant";
import {Tag} from "ant-design-vue";
const {baseColumnsExport, baseColumnsShow} = baseColumns()
const { cmbShowRender } = useColumnsRender()


export function getColumns() {
  const commColumns = reactive([
    'paramsType',
    'paramsCode',
    'paramsName',
    'customParamCode',
    'customParamName',
    'insertUser',
    'insertTime',
    'updateUser',
    'updateTime',
    'tradeCode',
    'unitYonyou',
    'yyCode'
  ])

// 导出字段设置`
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])

  // table表格字段设置
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  // table表格字段设置
  const totalColumns = ref([
    {
      width: 80,
      minWidth:80,
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      align: 'center',
      fixed: 'left',
      resizable:"true",
    },
    {
      title: '币制代码',
      width: 220,
      align: 'center',
      dataIndex: 'paramsCode',
      key: 'paramsCode',
      resizable:"true",
    },
    {
      title: '币制英文代码',
      width: 220,
      align: 'center',
      dataIndex: 'customParamCode',
      key: 'customParamCode',
      resizable:"true",
    },

    {
      title: '币制名称',
      width: 150,
      align: 'center',
      dataIndex: 'paramsName',
      key: 'paramsName',
      resizable:"true",
    },
    {
      title: '常用标志（业务类型）',
      width: 600,
      align: 'center',
      dataIndex: 'businessTypeList',
      key: 'businessTypeList',
      resizable:"true",
      customRender: ({ text }) => {
       // return h(<div></div>, cmbShowRender(text,productClassify.commonBusinessType))
      }
    },
    {
      title: '计量单位(用友)',
      width: 150,
      align: 'center',
      dataIndex: 'unitYonyou',
      key: 'unitYonyou',
      resizable:"true"
    },
    {
      title: '用友代码',
      width: 150,
      align: 'center',
      dataIndex: 'yyCode',
      key: 'yyCode',
      resizable:"true",
    },
  ])

  return{
    columnsConfig,
    excelColumnsConfig,
    totalColumns
  }
}


