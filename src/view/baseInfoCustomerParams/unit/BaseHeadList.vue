<template>
  <section  class="dc-section">
    <div class="cs-action"  v-show="show">
      <!-- 查询列表区域 -->
      <div class="cs-search">
        <a-card :bordered="false">
          <bread-crumb>
            <div ref="area_head">
              <div class="search-btn">
                <a-button size="small" type="primary" class="cs-margin-right cs-refresh" @click="handlerRefresh" v-show="showSearch">
                  <template #icon>
                    <GlobalIcon type="redo" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" class="cs-margin-right" @click="handlerSearch">
                  {{localeContent('m.common.button.query')}}
                  <template #icon>
                    <GlobalIcon type="search" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" danger class="cs-margin-right cs-warning" @click="handleShowSearch">
                  <template #icon>
                    <GlobalIcon v-show="!showSearch" type="down" style="color:#fff"/>
                    <GlobalIcon v-show="showSearch" type="up" style="color:#fff"/>
                  </template>
                </a-button>
              </div>
            </div>
          </bread-crumb>
          <div class="separateLine"></div>
          <div ref="area_search">
            <div v-show="showSearch">
              <base-head-search ref="headSearch" />
            </div>
          </div>
        </a-card>
      </div>
      <!-- 操作按钮区域 -->
      <div class="cs-action-btn">
<!--           <div class="cs-action-btn-item" >-->
<!--           <a-button size="small" @click="handlerAdd" >-->
<!--             <template #icon>-->
<!--               <GlobalIcon type="plus" style="color:green"/>-->
<!--             </template>-->
<!--             {{localeContent('m.common.button.add')}}-->
<!--           </a-button>-->
<!--         </div>-->
<!--         <div class="cs-action-btn-item" >-->
<!--           <a-button  size="small" :loading="deleteLoading" @click="handlerDelete">-->
<!--             <template #icon>-->
<!--               <GlobalIcon type="delete" style="color:red"/>-->
<!--             </template>-->
<!--             {{localeContent('m.common.button.delete')}}-->
<!--           </a-button>-->
<!--         </div>-->




       <div class="cs-action-btn-settings">
         <!-- 自定义显示组件 -->
          <CsTableColSettings
            :resId="tableKey"
            :tableKey="tableKey+'-client_code'"
            :initSettingColumns="originalColumns"
            :showColumnSettings="true"
            @customColumnChange="customColumnChange"
          >
          </CsTableColSettings>
        </div>





      </div>

      <!-- 表格区域 -->
      <div  v-if="showColumns && showColumns.length > 0">
        <s-table
          :animate-rows="false"
          ref="tableRef"
          class="cs-action-item"
          size="small"
          :scroll="{ y: tableHeight,x:400 }"
          bordered
          :pagination="false"
          :columns="showColumns.length > 0 ?showColumns:totalColumns"
          :data-source="dataSourceList"
          :row-selection="{  selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          row-key="sid"
        >
          <!-- 操作 -->
          <template #bodyCell="{ column, text, record }">
            <template v-if="['businessTypeList'].includes(column.dataIndex)">
              <div>
                <a-select v-if="editableData[record.sid]"
                          v-model:value="editableData[record.sid][column.dataIndex]"
                          mode="multiple"
                          style="width: 100%"
                          placeholder="Please select"
                          :options="productClassify.commonBusinessType"
                          @change="handleChange"
                ></a-select>

                <template v-else>
                  {{ formatSelectedLabels(record.businessTypeList) }}
                </template>
              </div>
            </template>
            <template v-if="['yyCode'].includes(column.dataIndex)" >
              <a-input  v-if="editableData[record.sid]"
                        v-model:value="editableData[record.sid][column.dataIndex]"
                        size="small"
                        style="width: 100%"
                        maxlength="20"/>
            </template>

               <template v-else-if="column.dataIndex === 'operation'">
                 <div class="editable-row-operations">
                       <span v-if="editableData[record.sid]">
                         <a @click="save(record.sid)">保存</a>
                         <a @click="cancel(record.sid)">取消</a>

                       </span>
                   <span v-else>
                          <a @click="edit(record.sid)">编辑</a>
                    </span>
                 </div>
               </template>
             </template>

           </s-table>
         </div>
         <!-- 分页 -->
      <div class=cs-pagination  v-if="showColumns && showColumns.length > 0">
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer  :page-size="page.pageSize" :total="page.total"  @change="onPageChange">
          <template #buildOptionText="props">
            <span >{{ props.value }}条/页</span>
          </template>
        </a-pagination>

      </div>
    </div>


  </section>
</template>

<script setup>
/* 使用自定义 Hook 函数 */
import {useCommon} from '@/view/common/useCommon'
import {createVNode, onMounted, provide, reactive, ref, watch} from "vue";
import BaseHeadSearch from "@/view/baseInfoCustomerParams/unit/BaseHeadSearch.vue";
import {getColumns} from "@/view/baseInfoCustomerParams/unit/BaseHeadColumns";
import {message, Modal} from "ant-design-vue";
import BreadCrumb from "@/components/breadcrumb/BreadCrumb.vue";
import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
import {deleteBaseInfoCustomerParams} from "@/api/bi/bi_client_info";
const { totalColumns } = getColumns()
import {localeContent} from "../../utils/commonUtil";
import { useImport } from "@/view/common/useImport";
import ycCsApi from "@/api/ycCsApi";
import CsTableColSettings from "@/components/settings/CsTableColSettings.vue";
import {useRoute} from "vue-router";
import {editStatus} from "@/view/common/constant";
import {deepClone} from "@/view/utils/common";

import {updateHead,insertHead} from "@/api/baseInfoCustomerParams/baseInfoCustomerParams";
import {productClassify} from "@/view/common/constant";
const { importConfig } = useImport()


/* 引入通用方法 */
const {
  editConfig,
  show,
  page,
  showSearch,
  headSearch,
  handleEditByRow,
  handleViewByRow,
  operationEdit,
  onPageChange,
  handleShowSearch,
  handlerSearch,
  dataSourceList,
  tableLoading,
  getTableScroll,
  exportLoading,
  getList,
  ajaxUrl,
  doExport,
  handlerRefresh,
  gridData

} = useCommon()



defineOptions({
  name: 'BaseHeadListUNIT',
});



const importShow = ref(false)
onMounted(fn => {


  ajaxUrl.selectAllPage = ycCsApi.baseInfoCustomerParams.list
  ajaxUrl.exportUrl = ycCsApi.baseInfoCustomerParams.export

  tableHeight.value = getTableScroll(100,'');

  getList()

  initCustomColumn()


})

const tableHeight = ref('')




/* 选中事件(这里的事件，顺序不能乱，必须key,后面点跟实体，不然回显出问题。) */
const onSelectChange = (selectedRowKeys, rowSelectData) => {
  gridData.selectedData = rowSelectData;
  gridData.selectedRowKeys = selectedRowKeys;
};


/* 按钮loading */
const deleteLoading = ref(false)

const editableData = reactive({});

const edit = (sid) => {
  editableData[sid] = { ...dataSourceList.value.find(item => item.sid === sid) };
};

// 保存操作
const save = (sid) => {
     // 1. 找到数据源中与当前 sid 匹配的项
     const targetItem = dataSourceList.value.find(item => item.sid === sid);

     // 2. 将临时编辑的数据（editableData[sid]）合并到目标项中
     Object.assign(targetItem, editableData[sid]);
    if(targetItem.yyCode === undefined || targetItem.yyCode === null || targetItem.yyCode === ''){
      message.error("用友代码必填")
      return
    }
     // 3. 删除临时编辑的数据，清理内存
     delete editableData[sid];
     if (sid.includes("save")){
       //执行保存请求
       insertHead(targetItem).then((res)=>{
         if (res.code === 200){
           message.success('新增成功!')
           getList()
         }
         if (res.code === 400){
           message.error(res.message)
         }
       })
     }else {
       updateHead(sid,targetItem).then((res)=>{
         if (res.code === 200){
           message.success('修改成功!')
           getList()
         }else if (res.code === 400){
           message.error(res.message)
         }
       })
     }


};
// 取消操作
const cancel = (sid) => {
  delete editableData[sid];
};
/* 新增数据 */
const handlerAdd = () => {
  let sid = new Date().getTime()+"save";
  const newData = {
    sid: `${sid}`,
    paramsType:"UNIT",
    paramsCode:"",
    paramsName:"",
    customParamCode:"",
    customParamName:""
  };
  dataSourceList.value.push(newData);
  dataSourceList.value = [].concat(dataSourceList.value);
};




/* 删除数据 */
const handlerDelete = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  // 弹出确认框
  Modal.confirm({
    title: '提醒?',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '删除',
    cancelText: '取消',
    content: '确认删除所选项吗？',
    onOk() {
      deleteLoading.value = true
      deleteBaseInfoCustomerParams(gridData.selectedRowKeys).then(res => {
        if (res.code === 200) {
          message.success("删除成功！")
          getList()
        }
      }).finally(() => {
        deleteLoading.value = false
      })
    },
    onCancel() {

    },
  });

}



const handleChange = (value) => {
  console.log(`selected ${value}`);
};

/* 导出事件 */
const handlerExport = () =>{
  doExport('测试文件导出.xlsx',totalColumns)
}



/* 自定义设置 */
/* 显示列数据 */
const showColumns =  ref([])

/* 唯一键 */
const tableKey = ref('')
console.log('window.majesty.router',window.majesty.router.patch)
tableKey.value = window.$vueApp ? window.majesty.router.currentRoute.value.path : useRoute().path

const originalColumns = ref()




/* 自定义显示列初始化操作 */
const initCustomColumn = () => {
  // 这里是拷贝是属于
  let tempColumns = deepClone(totalColumns.value)
  let dealColumns = []
  // 使用map遍历会丢失customRender方法，所以使用forEach
  tempColumns.map((item) => {
    let newObj = Object.assign({}, item);
    newObj["visible"] = true;
    // 需要将customRender 方法追加到新对象中
    if (item.customRender) {
      newObj["customRender"] = item.customRender;
    }
    dealColumns.push(newObj);
  });
  //原始列信息
  originalColumns.value = dealColumns;
}



/* 选中visible为true的数据进行显示 */
const customColumnChange = (settingColumns)  => {
  totalColumns.value = settingColumns.filter((item) => item.visible === true);
  showColumns.value = [...totalColumns.value]
  //  深拷贝之前实现 丢失了方法，所以修改了utils的deepClone方法，这里不要了
  // showColumns.value.map((item) => {
  //   let temp = totalColumns.value.find((tempItem) => tempItem.key === item.key);
  //   if (temp && temp.customRender) {
  //     item.customRender = temp.customRender;
  //   }
  // });
}





/* 监控 dataSourceList */
watch(dataSourceList, (newValue, oldValue) => {
  showColumns.value = [...totalColumns.value];
  // 将showColumns的数据属性 和 初始属性进行比对，如果初始属性存在customRender 方法，追加到showColumns中
},{deep:true})




// 格式化选中的值
const formatSelectedLabels = (selectedValues) => {
  if(selectedValues){
    return selectedValues
      .map(value => {
        const option = productClassify.commonBusinessType.find(opt => opt.value === value);
        return option ?option.value +" "+ option.label : '';
      })
      .join(', ');
  }else {
    return null
  }

};

</script>

<style lang="less" scoped>

.editable-row-operations a {
  margin-right: 8px;
}
</style>
