<template>
  <a-form  layout="inline"  label-align="right"  :label-col="{ style: { width: '100px' } }" :model="searchParam"   class="cs-form  grid-container" >
    <!-- 币制代码 -->
    <a-form-item name="customerCode"   :label="'计量单位代码'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.paramsCode" />
    </a-form-item>
    <a-form-item name="paramsName"   :label="'计量单位名称'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.paramsName" />
    </a-form-item>
  </a-form>
</template>

<script setup>
import {inject, onMounted, reactive} from 'vue'


defineOptions({
  name: 'BaseHeadSearch'
})
const searchParam = reactive({
  paramsType:'UNIT',
  paramsCode:'',
  paramsName:'',
})
/* 定义重置方法(注意前后顺序) */
const resetSearch = () => {
  Object.keys(searchParam).forEach(key => {
    if (key!='paramsType'){
      searchParam[key] = '';
    }
  });
}
defineExpose({searchParam,resetSearch});
onMounted(() => {

});





</script>
