import {baseColumns} from "@/view/common/baseColumns";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {h, reactive, ref} from "vue";
import {Tag} from "ant-design-vue";
import {productClassify} from "@/view/common/constant";
import {useMerchant} from "@/view/common/useMerchant";
const {baseColumnsExport, baseColumnsShow} = baseColumns()
const { cmbShowRender } = useColumnsRender()
const { merchantOptions,getMerchantOptions } = useMerchant()
await getMerchantOptions()
export function getColumns() {

  const commColumns = reactive([
    'businessType'
    , 'customer'
    , 'contractNo'
    , 'purchaseOrderNo'
    , 'currPrice'
    , 'curr'
    , 'rmbPrice'
    , 'sendFinance'
    , 'createrBy'
    , 'createrTime'
    , 'status'
  ])

// 导出字段设置`
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns,
  ])

  // table表格字段设置
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  // table表格字段设置
  const totalColumns = ref([
    {
      width: 80,
      minWidth:80,
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      align: 'center',
      fixed: 'left',
      resizable:"true",
    },
    {
      title: '业务类型',
      minWidth: 120,
      align: 'center',
      key: 'businessType',
      dataIndex: 'businessType',
      resizable:"true",
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,productClassify.businessType2))
      }
    },
    {
      title: '客户',
      minWidth: 120,
      align: 'center',
      key: 'customer',
      dataIndex: 'customer',
      resizable:"true",
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text, merchantOptions.value))
      }
    },
    {
      title: '合同号',
      minWidth: 120,
      align: 'center',
      key: 'contractNo',
      dataIndex: 'contractNo',
      resizable:"true",
    },
    {
      title: '进/出货单号',
      minWidth: 120,
      align: 'center',
      key: 'purchaseOrderNo',
      dataIndex: 'purchaseOrderNo',
      resizable:"true",
    },
    {
      title: '外币金额',
      minWidth: 120,
      align: 'center',
      key: 'currPrice',
      dataIndex: 'currPrice',
      resizable:"true",
    },

    {
      title: '币种',
      minWidth: 120,
      align: 'center',
      key: 'curr',
      dataIndex: 'curr',
      resizable:"true",
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,[],'CURR'))
      }
    },
    {
      title: '人民币金额',
      minWidth: 120,
      align: 'center',
      key: 'rmbPrice',
      dataIndex: 'rmbPrice',
      resizable:"true",
    },
    {
      title: '发送财务系统',
      minWidth: 120,
      align: 'center',
      key: 'sendFinance',
      dataIndex: 'sendFinance',
      resizable:"true",
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text, productClassify.isNot))
        // return h(Tag, cmbShowRender(text,productClassify.isNot))
      }
    },

    {
      title: '版本号',
      minWidth: 120,
      align: 'center',
      key: 'versionNo',
      dataIndex: 'versionNo',
      resizable:"true",
    },
    {
      title: '制单人',
      minWidth: 120,
      align: 'center',
      key: 'createrUserName',
      dataIndex: 'createrUserName',
      resizable:"true",
    },
    {
      title: '制单时间',
      minWidth: 120,
      align: 'center',
      key: 'createrTime',
      dataIndex: 'createrTime',
      resizable:"true",
    },
    {
      title: '单据状态',
      minWidth: 120,
      align: 'center',
      key: 'status',
      dataIndex: 'status',
      resizable:"true",
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,productClassify.state))
      }
    },
  ])

  return{
    columnsConfig,
    excelColumnsConfig,
    totalColumns,
    commColumns
  }
}


