<template>
  <section class="cs-action cs-action-tab">
    <div class="cs-tab">
      <a-tabs class="sticky-header"  v-model:activeKey="tabName" size="small" :tabBarStyle="tabBarStyle" >
        <a-tab-pane key="headTab" tab="外商合同" >
          <contract-message  ref="headTab"  :edit-config="editConfig"  @onEditBack="editBack" @onHeadback="headBack"></contract-message>
        </a-tab-pane>
        <a-tab-pane v-if="showBody" key="file" tab="进货明细" >
          <shipping-order-message  :edit-config="editConfig"  @onEditBack="editBack" @onHeadback="headBack"></shipping-order-message>
        </a-tab-pane>
        <template #rightExtra>
            <div class="cs-tab-icon" @click="editBack">
              <GlobalIcon type="close-circle" style="color:#000"/>
            </div>
        </template>
      </a-tabs>
    </div>

  </section>
</template>

<script setup>

import {onMounted, reactive, ref, watch} from "vue";
import {editStatus} from "@/view/common/constant";
import ContractMessage from "./contract/ContractMessage.vue";
import ShippingOrderMessage from "./shippingOrder/ShippingOrderMessage.vue";

defineOptions({
  name:'CustomerAccountDetailTab'
})

// const emit = defineEmits(['onEditBack'])
const emit = defineEmits(['onEditBack','onHeadback'])
/* 定义editConfig 用于向子组件传递 */
const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});


/* 自定义样式 */
const tabBarStyle = {
  background:'#fff',
  position:'sticky',
  top:'0',
  zIndex:'100',
}

/* 激活Tab key */
const tabName = ref('headTab');

/* 总tab信息 */
const tabs = reactive({
  headTab:true,
  shipFrom:true,
})

/* 表头headId */
const headId = ref('')


/* 是否显示子模块 tab */
const showBody = ref(true)



/* 返回tab界面 */
const editBack = (val) => {
  if (val.editStatus === editStatus.EDIT){
    showBody.value = val.showBody
    props.editConfig.editStatus = val.editStatus
    props.editConfig.editData = val.editData
  }else {
    if (val) {
      emit('onEditBack', val)
    }
  }
}

const headBack = (val) =>{
  if (val){
    emit('onHeadback', val);
  }
}


/* 初始化操作 */
onMounted(()=>{
  console.log('props.editConfig', props.editConfig)
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    showBody.value = true
  }
  else if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    showBody.value = true;
  }else if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    showBody.value = true
  }
})



/* 监控tabName变化 */
watch(tabName, (value) => {
  for (let t in tabs) {
    tabs[t] = false
  }
  tabs[value] = true
})

</script>

<style lang="less" scoped>

</style>
