<template>
  <a-form  layout="inline"  label-align="right"  :label-col="{ style: { width: '100px' } }" :model="searchParam"   class="cs-form  grid-container" >
    <a-form-item name="businessType" :label="'业务类型'" class="grid-item" :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key"  clearable  show-search v-model:value="searchParam.businessType" id="businessType">
        <a-select-option class="cs-select-dropdown" v-for="item in productClassify.businessType2"  :key="item.value + ' ' + item.label  " :value="item.value" :label=" item.value + ' ' +item.label">
          {{item.value}} {{ item.label  }}
        </a-select-option>
      </cs-select>
    </a-form-item>
<!--    合同号-->
    <a-form-item name="contractNo"   :label="'合同号'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.contractNo" />
    </a-form-item>
  </a-form>
</template>

<script setup>
import {inject, onMounted, reactive} from 'vue'
import {productClassify} from "@/view/common/constant";
import CsSelect from "@/components/select/CsSelect.vue";
import {useCommon} from "@/view/common/useCommon";

defineOptions({
  name: 'ContractSearch'
})

const {
  editConfig,
} = useCommon()

const props = defineProps({
  headId: {
    type: Object,
    default: () => {
    }
  }
});
const searchParam = reactive({
  contractNo:'',
  businessType:'',
})
/* 定义重置方法(注意前后顺序) */
const resetSearch = () => {
  Object.keys(searchParam).forEach(key => {
    searchParam[key] = '';
  });
}
const rules = {
  // businessType: [
  //   { required: true, message: '', trigger: 'blur' },
    // { required: true, message: '业务类型不能为空！', trigger: 'blur' },
  // ],
}
defineExpose({searchParam,resetSearch});
onMounted(() => {

});





</script>
