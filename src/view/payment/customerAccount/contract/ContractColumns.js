import {baseColumns} from "@/view/common/baseColumns";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {h, reactive, ref} from "vue";
import {productClassify} from "@/view/common/constant";
import {Tag} from "ant-design-vue";
import {useOrderColumnsCommon} from "@/view/dec/imported_cigarettes/head/useOrderColumnsCommon";
const {baseColumnsExport, baseColumnsShow} = baseColumns()
const { cmbShowRender } = useColumnsRender()
const { supplierList,getSupplierList} = useOrderColumnsCommon()
getSupplierList()

// 格式化数字为千分位分隔的工具函数
const formatNumber = (value) => {
  if (value === undefined || value === null || value === '') {
    return '';
  }
  // 将数字转换为字符串并添加千分位分隔符
  return new Intl.NumberFormat('zh-CN').format(value);
};
export function getColumns(typeMessage) {
  const commColumns = reactive([
    "contractNo"
    // ,"orderNo"
    ,"partyB"
    ,"curr"
    ,"decTotal"
    ,"qty"
    ,"unit"
    ,"merchandiseCategories"
  ])

// 导出字段设置`
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])

  // table表格字段设置
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  // table表格字段设置
  const totalColumns = ref([
    // 合同号
    {
      title: '合同号',
      width: 180,
      align: 'center',
      dataIndex: 'contractNo',
      key: 'contractNo',
    },
    // 订单号
    // {
    //   title: '订单号',
    //   width: 180,
    //   align: 'center',
    //   dataIndex: 'orderNo',
    //   key: 'orderNo',
    // },
    // 客户名称
    {
      title: '客户',
      width: 180,
      align: 'center',
      dataIndex: 'domesticPrincipal',
      key: 'domesticPrincipal',
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text, supplierList.value))
      }
    },
    // 币种
    {
      title: '币种',
      width: 50,
      align: 'center',
      dataIndex: 'currency',
      key: 'currency',
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,typeMessage.value.currTypeOptions))
      }
    },
    // 金额
    {
      title: '金额',
      width: 150,
      align: 'center',
      dataIndex: 'decTotalToList',
      key: 'decTotalToList',
      customRender: ({ text }) => {
        return formatNumber(text);
      }
    },
    // 数量
    {
      title: '数量',
      width: 150,
      align: 'center',
      dataIndex: 'qtyToList',
      key: 'qtyToList',
      customRender: ({ text }) => {
        return formatNumber(text);
      }
    },
    // 单位
    {
      title: '单位',
      width: 150,
      align: 'center',
      dataIndex: 'unitToList',
      key: 'unitToList',
    },
    // 商品类别
    {
      title: '商品类别',
      width: 150,
      align: 'center',
      dataIndex: 'merchandiseCategoriesToList',
      key: 'merchandiseCategoriesToList',
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,typeMessage.value.productTypeOptions))
      }
    }
  ])

  return{
    columnsConfig,
    excelColumnsConfig,
    totalColumns,
    commColumns
  }
}


