<template>
  <div class="contract-detail-table">

    <!-- 操作按钮区域 -->
    <div class="cs-action-btn">
      <div class="cs-action-btn-item" v-has="['yc-cs:registrationList:delete']">
        <a-button size="small" :loading="deleteLoading" @click="handlerDelete" v-show="props.editConfig.editStatus !== editStatus.SHOW">
          <template #icon>
            <GlobalIcon type="delete" style="color:red"/>
          </template>
          {{localeContent('m.common.button.delete')}}
        </a-button>
      </div>
      <div class="cs-action-btn-item" v-has="['yc-cs:registrationList:export']">
        <a-button size="small" :loading="exportLoading" @click="handlerExport">
          <template #icon>
            <GlobalIcon type="folder-open" style="color:orange"/>
          </template>
          {{localeContent('m.common.button.export')}}
        </a-button>
      </div>
    </div>

    <s-table
      ref="tableRef"
      class="cs-action-item-modal-table remove-table-border-add-bg"
      bordered
      :pagination="false"
      :height="500"
      column-drag
      :data-source="tableData"
      :columns="getColumns"
      row-key="sid"
      :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'decTotal'">
          {{ formatNumber(record.decTotal) }}
        </template>
      </template>

      <template v-if="props.editConfig.editStatus !== editStatus.SHOW" #cellEditor="{ column, modelValue, save, closeEditor, editorRef, getPopupContainer, record }">
        <template v-if="column.dataIndex === 'orderNumber'">
          <a-input
              size="small"
              :placeholder="record.orderNumber"
              v-model:value="modelValue.value"
              style="width: 100%;height: 24px"
              @blur="() => {handleChange(record, modelValue.value);}"
              @keydown.enter="() => {handleChange(record, modelValue.value);}"
          />
        </template>
        <template v-if="column.dataIndex === 'invoiceNumber'">
          <a-input
              size="small"
              :placeholder="record.invoiceNumber"
              v-model:value="modelValue.value"
              style="width: 100%;height: 24px"
              @blur="() => {handleChange2(record, modelValue.value);}"
              @keydown.enter="() => {handleChange2(record, modelValue.value);}"
          />
        </template>
        <template v-if="column.dataIndex === 'decTotal'">
          <a-input-number
              :ref="editorRef"
              size="small"
              v-model:value="modelValue.value"
              style="width: 100%;height: 24px"
              :formatter="value => {
                if (!value) return '';
                // 分别处理整数部分和小数部分
                const parts = value.toString().split('.');
                // 只对整数部分添加千位分隔符
                parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                // 组合整数和小数部分
                return parts.join('.');
              }"
              :parser="value => value.replace(/\$\s?|(,*)/g, '')"
              @blur="() => {handleDecTotalChange(record, modelValue.value);}"
              @keydown.enter="() => {handleDecTotalChange(record, modelValue.value);}"
              @keydown.esc="closeEditor"
          />
        </template>
      </template>
    </s-table>

    <!-- 分页 -->
    <div class=cs-pagination>
      <div class="count-number">
        <span>共 {{ page.total }} 条</span>
      </div>
      <a-pagination size="small" v-model:current="page.current" show-size-changer :page-size="page.pageSize" :total="page.total" @change="onPageChange">
        <template #buildOptionText="props">
          <span>{{ props.value }}条/页</span>
        </template>
      </a-pagination>
    </div>
  </div>
</template>

<script setup>
import {ref, reactive, watch, onMounted, createVNode, computed, h, nextTick} from 'vue';
import {message, Modal} from 'ant-design-vue';
import ycCsApi from "@/api/ycCsApi";
import {deleteContractList, getContractTotal, updateContractList} from "@/api/importedCigarettes/contract/contractApi";
import { usePCode } from "@/view/common/usePCode";
import {localeContent} from "@/view/utils/commonUtil";
import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
import {useCommon} from '@/view/common/useCommon'
import {editStatus} from "@/view/common/constant";
import { useMerchant } from "@/view/common/useMerchant"
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {
  deleteRegistrationHead,
  deleteRegistrationList,
  updateRegistrationHead,
  updateRegistrationList
} from "@/api/payment/payment_info";
import {isNullOrEmpty} from "@/view/utils/common";
const { cmbShowRender } = useColumnsRender();

const { productTypeOptions, getProductTypeOptions } = useMerchant()



const {
  page
} = useCommon()

const { getPCode } = usePCode();
const pCode = ref('');




const returnData = (val) => {
  emit('change', val);
};

function formatter(value) {
  if (value === '' || value === null || value === undefined) return '';
  const number = Number(value);
  if (isNaN(number)) return '';
  // 使用 Intl.NumberFormat 格式化，强制至少两位小数
  return new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 10
  }).format(number);
}
function parser(value) {
  // 去除所有千分位逗号，保留原始小数位
  return value.replace(/,/g, '');
}


// 格式化数字为千分位分隔的工具函数
const formatNumber = (value) => {
  if (value === null || value === undefined || value === '') {
    return '';
  }
  // 将值转换为数字
  const num = parseFloat(value);
  if (isNaN(num)) {
    return '';
  }

  // 分别处理整数部分和小数部分
  const parts = num.toFixed(2).split('.');
  // 只对整数部分添加千位分隔符
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  // 组合整数和小数部分
  return parts.join('.');
};

function filterExportParams(params) {
  console.log(params)
  let tempArr = []
  if (isNullOrEmpty(params)) {
    return []
  }
  params.forEach(item => {
    // 如果当前字段中dataIndex是operation 则不导出
    if (item.dataIndex !== 'operation') {
      tempArr.push({
        key: item.dataIndex,
        value: item.title
      })
    }
  })
  return tempArr
}

function doExport(fileName, exportHeader, exportColumns) {
  exportLoading.value = true
  window.window.majesty.httpUtil.downloadFile( "/biz/api/" + 'v1/registrationList/export',
      fileName,
      {
        exportColumns: {headid : props.editConfig.editData.sid},
        name: fileName,
        header: exportHeader ? filterExportParams(exportHeader) : []
      },
      'post',
      () => {
      }
  ).then((res) => {

  }).finally(() => {
    exportLoading.value = false
  })
}

const handlerExport = () =>{
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  const timestamp = `${year}${month}${day}${hours}${minutes}${seconds}`
  doExport( `收款登记表体${timestamp}.xlsx`,columns)
}

// 获取列表后自动触发编辑器
const getList = async () => {
  try {
    const params = {
      headId: props.headId
    };
    window.majesty.httpUtil.postAction(`${ycCsApi.payment.registrationList.list}?page=${page.current}&limit=${page.pageSize}`,
      params
    ).then(res => {
      if (res.code === 200) {
        tableData.value = res.data || [];
        editableKeys.value = tableData.value.map(item => item.sid);
        page.total = res.total;

        // 数据加载完成后，如果不是查看模式，触发编辑器
        if (props.editConfig.editStatus !== editStatus.SHOW) {
          setTimeout(() => {
            triggerEditor();
          }, 200);
        }
      } else {
        message.error(res.message || '获取数据失败');
      }
    })
  } catch (error) {
    message.error('获取数据失败');
  }
};

const props = defineProps({
  headId: {
    type: String,
    default: ''
  },
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});

const emit = defineEmits(['update:value', 'change']);

const deleteLoading = ref(false)
const exportLoading = ref(false)

const tableRef = ref();
const selectedRowKeys = ref([]);
const tableData = ref([]);
const dataSource = ref([]);
const editableKeys = ref([]);

// 监听headId变化
watch(() => props.headId, (newVal) => {
  if (newVal) {
    getList();
  }
}, { immediate: true });

const onPageChange = async (pageNumber, pageSize) => {

  page.current = pageNumber
  page.pageSize = pageSize
  // 在这里添加处理页码变化的逻辑
  await getList()
}


// 处理合同数量变更
const handleDecTotalChange = async (record, newValue) => {
  if (newValue === record.decTotal) {
    return;
  }
  try {
    // 更新本地数据
    const rowIndex = tableData.value.findIndex(item => item.sid === record.sid);
    if (rowIndex !== -1) {
      // 这里调用后端API
      const updatedRecord = { ...record, decTotal: newValue };
      const response = await updateRegistrationList(record.sid, updatedRecord);
      if (response.code === 200) {
        // message.success('修改成功!');
        Object.assign(tableData.value[rowIndex], response.data);
        returnData(response.data)
        await getList();
      } else {
        message.error(response.message || '修改失败');
      }
    }
  } catch (error) {
    message.error('数据更新失败，请重试');
  }
};



const handleChange = async (record, newValue) => {

  try {
    // 更新本地数据
    const rowIndex = tableData.value.findIndex(item => item.sid === record.sid);
    if (rowIndex !== -1) {
      // 这里调用后端API
      const updatedRecord = { ...record, orderNumber: newValue };
      const response = await updateRegistrationList(record.sid, updatedRecord);
      if (response.code === 200) {
        // message.success('修改成功!');
        Object.assign(tableData.value[rowIndex], response.data);
        returnData(response.data)
        await getList();
      } else {
        message.error(response.message || '修改失败');
      }
    }
  } catch (error) {
    message.error('数据更新失败，请重试');
  }
};

const handleChange2 = async (record, newValue) => {

  try {
    // 更新本地数据
    const rowIndex = tableData.value.findIndex(item => item.sid === record.sid);
    if (rowIndex !== -1) {
      // 这里调用后端API
      const updatedRecord = { ...record, invoiceNumber: newValue };
      const response = await updateRegistrationList(record.sid, updatedRecord);
      if (response.code === 200) {
        // message.success('修改成功!');
        Object.assign(tableData.value[rowIndex], response.data);
        returnData(response.data)
        await getList();
      } else {
        message.error(response.message || '修改失败');
      }
    }
  } catch (error) {
    message.error('数据更新失败，请重试');
  }
};


/* 删除数据 */
const handlerDelete = () => {
  if (selectedRowKeys.value.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  // 弹出确认框
  Modal.confirm({
    title: '提醒?',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '删除',
    cancelText: '取消',
    content: '所选中的明细中存在相同合同号或进货单号，将一起删除，请确认',
    onOk() {
      deleteLoading.value = true
      deleteRegistrationList(selectedRowKeys.value).then(res => {
        if (res.code === 200) {
          message.success("删除成功！")
          getList()
        } else {
          message.error(res.message)
        }
      }).finally(() => {
        deleteLoading.value = false
      })
    },
    onCancel() {
    },
  });
}





// 计算属性：根据编辑状态动态生成列配置
const getColumns = computed(() => {
  return columns;
});

// 监听编辑状态变化，当进入编辑状态时自动触发编辑器
watch(() => props.editConfig.editStatus, (newVal) => {
  if (newVal !== editStatus.SHOW && tableData.value.length > 0) {
    nextTick(() => {
      triggerEditor();
    });
  }
});


// 触发编辑器打开
const triggerEditor = () => {
  if (props.editConfig.editStatus !== editStatus.SHOW) {
    // 确保使用最新的表格数据
    dataSource.value = [...tableData.value];

    // 构建所有行的编辑配置
    const editConfigs = [];
    dataSource.value.forEach(row => {
      editConfigs.push({ columnKey: 'orderNumber', rowKey: row.sid });
      editConfigs.push({ columnKey: 'invoiceNumber', rowKey: row.sid });
      editConfigs.push({ columnKey: 'decTotal', rowKey: row.sid });
    });

    // 使用nextTick确保DOM已更新
    nextTick(() => {
      console.log('触发编辑器，行数:', dataSource.value.length, '编辑配置:', editConfigs);
      if (editConfigs.length > 0) {
        // 一次性打开所有单元格的编辑状态
        tableRef.value?.openEditor(editConfigs);
      }
    });
  }
};

// 页面加载完成后自动触发编辑状态
onMounted(() => {

});

// 初始化列定义
const columns = [
  {
    title: '购销合同号',
    dataIndex: 'contractNo',
    width: 150,
    key: 'contractNo',
    align: 'center',
  },
  {
    title: '进货单号',
    dataIndex: 'orderNumber',
    width: 150,
    key: 'orderNumber',
    editable: 'cellEditorSlot',
    align: 'center',
  },

  {
    title: '商品名称',
    dataIndex: 'gname',
    width: 120,
    key: 'gname',
    align: 'center',
  },
  {
    title: '发票号',
    dataIndex: 'invoiceNumber',
    width: 120,
    key: 'invoiceNumber',
    editable: 'cellEditorSlot',
    align: 'center',
  },
  {
    title: '金额',
    dataIndex: 'decTotal',
    width: 120,
    key: 'decTotal',
    editable: 'cellEditorSlot',
    align: 'center',
    customRender: ({ text }) => {
      return formatNumber(text);
    }
  },
];

const onSelectChange = (keys) => {
  selectedRowKeys.value = keys;
};

defineExpose({
  getTableData: () => tableData.value,
  reloadData: getList // 暴露刷新方法供父组件调用
});
</script>

<style scoped>
.contract-detail-table {
  margin-top: 16px;
}

</style>
