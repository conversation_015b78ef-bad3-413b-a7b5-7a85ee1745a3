<template>
  <section>
    <a-card size="small" title="收款登记信息" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData" class="grid-container">
          <a-form-item name="documentNo" :label="'单据号'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.documentNo"/>
          </a-form-item>
          <!-- 业务类型 -->
          <a-form-item name="businessType" :label="'业务类型'" class="grid-item" :colon="false">
            <cs-select :disabled="true" optionFilterProp="label" option-label-prop="key" allow-clear show-search
                       v-model:value="formData.businessType" id="businessType">
              <a-select-option v-for="item in productClassify.businessType" :key="item.value + ' ' + item.label"
                               :value="item.value" :label="item.value + item.label">
                {{ item.value }} {{ item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="advanceFlag" :label="'预付标志'" class="grid-item"  :colon="false">
            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.advanceFlag" id="advanceFlag">
              <a-select-option v-for="item in productClassify.isNot"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <!--          部门-->
          <a-form-item name="department" :label="'部门'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.department"/>
          </a-form-item>
          <a-form-item name="currency"   :label="'币种'" class="grid-item"  :colon="false">
            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.currency" id="currency">
              <a-select-option    v-for="item in currOptions"  :key="item.label" :value="item.value" :label="item.label">
                {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="payerName" :label="'付款客户'" class="grid-item"  :colon="false">
            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.payerName" id="payerName">
              <a-select-option v-for="item in buyerOptions"  :key="item.label" :value="item.value" :label="item.label">
                 {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="entrustCompany" :label="'委托单位'" class="grid-item"  :colon="false">
            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.entrustCompany" id="entrustCompany">
              <a-select-option v-for="item in buyerOptions"  :key="item.label" :value="item.value" :label="item.label">
                {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>
          <a-form-item name="bankFee" :label="'银行手续费'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable" size="small"
                            :formatter="value => {
                if (!value) return '';
                // 分别处理整数部分和小数部分
                const parts = value.toString().split('.');
                // 只对整数部分添加千位分隔符
                parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                // 组合整数和小数部分
                return parts.join('.');
              }"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.bankFee" notConvertNumber decimal int-length="19" precision="2"/>
          </a-form-item>
          <a-form-item name="paymentAmount" :label="'收款金额'" class="grid-item" :colon="false">
            <a-input-number style="width: 100%;padding-left: 0" :disabled="showDisable" size="small"
                            :formatter="value => {
                if (!value) return '';
                // 分别处理整数部分和小数部分
                const parts = value.toString().split('.');
                // 只对整数部分添加千位分隔符
                parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                // 组合整数和小数部分
                return parts.join('.');
              }"
                            :parser="value => value.replace(/\$\s?|(,*)/g, '')"
                            v-model:value="formData.paymentAmount" notConvertNumber decimal int-length="19" precision="2"/>
          </a-form-item>


          <a-form-item name="financeFlag" :label="'发送财务系统'" class="grid-item"  :colon="false">
            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.financeFlag" id="financeFlag">
              <a-select-option v-for="item in productClassify.isNot"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>

          <a-form-item name="businessDate" :label="'业务日期'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="showDisable"
              v-model:value="formData.businessDate"
              id="businessDate"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
            />
          </a-form-item>


          <a-form-item name="reversalFlag" :label="'是否红冲'" class="grid-item"  :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.reversalFlag" id="reversalFlag">
              <a-select-option v-for="item in productClassify.isNot"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>



          <a-form-item name="remark" :label="'备注'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.remark"/>
          </a-form-item>


          <!--          制单人-->
          <a-form-item name="updateUserName" :label="'制单人'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.updateUserName"/>
          </a-form-item>
          <!--          制单日期-->
          <a-form-item name="updateTime" :label="'制单日期'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="true"
              v-model:value="formData.updateTime"
              id="updateTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
            />
          </a-form-item>


          <!--          单据状态-->
          <a-form-item name="documentStatus" :label="'单据状态'" class="grid-item"  :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.documentStatus" id="documentStatus">
              <a-select-option v-for="item in productClassify.state"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>

          <!--          确认时间-->
          <a-form-item name="confirmTime" :label="'确认时间'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="true"
              v-model:value="formData.confirmTime"
              id="confirmTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
            />
          </a-form-item>


          <div class="cs-submit-btn merge-3">
            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right"
                      v-show="props.editConfig.editStatus !== editStatus.SHOW">保存
            </a-button>
<!--            <a-button size="small" type="primary" @click="handlerSaveClose" class="cs-margin-right"-->
<!--                      v-show="props.editConfig.editStatus !== editStatus.SHOW">保存关闭-->
<!--            </a-button>-->
            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回</a-button>
            <a-button size="small" type="primary" @click="handlerConfirm"  :loading="confirmLoading" class="cs-margin-right">确认</a-button>
          </div>
        </a-form>
      </div>
    </a-card>
    <a-card size="small"  class="cs-card-form">
      <RegistrationDetailTable
          ref="detailTableRef"
          :disabled="showDisable"
          :head-id="formData.sid"
          :edit-config="props.editConfig"
          @change="handleDetailChange"
      />
    </a-card>
  </section>
</template>

<script setup>
import {editStatus, productClassify} from '@/view/common/constant'
import {message, Modal} from "ant-design-vue";
import {onMounted, reactive, ref, computed, createVNode} from "vue";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
import zhCN from 'ant-design-vue/es/date-picker/locale/zh_CN';
import {deleteContract, updateContract} from "@/api/importedCigarettes/contract/contractApi";
import RegistrationDetailTable from './list/RegistrationDetailTable.vue';
import ycCsApi from "@/api/ycCsApi";
import {useOrderColumnsCommon} from "@/view/dec/imported_cigarettes/head/useOrderColumnsCommon";
import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
import {
  confirmRegistrationHead,
  getDocNoRegistrationHead,
  insertRegistrationHead,
  updateRegistrationHead
} from "@/api/payment/payment_info";

const { getPCode } = usePCode()

const confirmLoading = ref(false)
const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});

// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack']);




//首次新增未保存 删除数据
let firstAddSave = ref(false);

const onBack = (val) => {
  if (props.editConfig.editStatus === editStatus.ADD && !firstAddSave) {
    deleteContract(formData.sid).then(res => {
      emit('onEditBack', val);
    })
  } else {
    emit('onEditBack', val);
  }
};

function formatter(value) {
  if (value === '' || value === null || value === undefined) return '';
  const number = Number(value);
  if (isNaN(number)) return '';

  return new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 10
  }).format(number);
}



function parser(value) {
  // 去除所有千分位逗号，保留原始小数位
  return value.replace(/\$\s?|(,*)/g, '');
}


// 是否禁用
const showDisable = ref(false)

// 表单数据
const formData = reactive({
  documentNo:'',
  businessType:'',
  advanceFlag:'',
  department:'',
  currency:'',
  payerName:'',
  entrustCompany:'',
  bankFee:'',
  paymentAmount:'',
  financeFlag:'',
  businessDate:'',
  reversalFlag:'',
  documentStatus:'',
  confirmTime:'',
  remark:'',
  updateUserName: '',
  updateTime: '',
})

// 表单校验规则
const rules = {
  businessType: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  documentNo: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  advanceFlag: [
    {required: true, message: '不能为空', trigger: 'change'},
  ],
  department: [
    {required: true, message: '不能为空', trigger: 'change'},
  ],
  currency: [
    {required: true, message: '不能为空', trigger: 'change'},
  ],
  payerName: [
    {required: true, message: '不能为空', trigger: 'change'},
  ],
  entrustCompany: [
    {required: true, message: '不能为空', trigger: 'change'},
  ],
  paymentAmount: [
    {required: true, message: '不能为空', trigger: 'change'},
  ],
  financeFlag: [
    {required: true, message: '不能为空', trigger: 'change'}
  ],
  businessDate: [
    {required: true, message: '不能为空', trigger: 'change'}
  ],
  reversalFlag: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  updateUserName: [
    {required: true, message: '不能为空', trigger: 'blur'}
  ],
  updateTime: [
    {required: true, message: '不能为空', trigger: 'blur'}
  ],

}


// 表单引用
const formRef = ref()

// 表格引用
const detailTableRef = ref();

// 处理明细数据变化
const handleDetailChange = (details) => {
  formData.details = details;
};




/* 确认事件 */
const handlerConfirm = () => {
  // 弹出确认框
  Modal.confirm({
    title: '提醒',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: '确认执行此操作吗？',
    onOk() {
      confirmLoading.value = true
      // 这里需要调用确认API
      const params = {
        sid : formData.sid
      }
      confirmRegistrationHead(params).then(res => {
        if (res.code === 200) {
          message.success("确认成功！")
          formData.confirmTime = res.data
        } else {
          message.error(res.message)
        }
      }).finally(() => {
        confirmLoading.value = false
      })
    },
    onCancel() {
      // 取消操作
    },
  });
}








// 修改保存处理函数
const handlerSave = async () => {
  try {
    await formRef.value.validate()
    // 根据编辑状态判断是新增还是修改
    if (props.editConfig.editStatus === editStatus.ADD) {
      // 新增逻辑
      updateRegistrationHead(formData.sid, formData).then((res)=>{
        if (res.code === 200){
          message.success('保存成功')
          firstAddSave = true
          // 保存成功后刷新明细表格数据
          if (detailTableRef.value) {
            detailTableRef.value.reloadData();
          }
        } else {
          message.error(res.message);
        }
      })
    } else if (props.editConfig.editStatus === editStatus.EDIT) {
      updateRegistrationHead(formData.sid, formData).then((res)=>{
        if (res.code === 200){
          message.success('修改成功!')
          // 保存成功后刷新明细表格数据
          if (detailTableRef.value) {
            detailTableRef.value.reloadData();
          }
        } else {
          message.error(res.message);
        }
      })
    }
  } catch (error) {
    console.error('表单验证失败', error)
  }
}


//基础资料-客商信息
const buyerOptions = reactive([])
const currOptions = reactive([])

const getBuyerOptions = async () => {
  try {
    const params = {}
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.bizMerchant.list}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        buyerOptions.push({
          value: item.merchantNameCn,
          label: item.merchantNameCn
        });
      });
    } else {
      message.error(res.message || '获取客商数据失败');
    }
  } catch (error) {
    message.error('获取客商数据失败');
  }
}
const getCurrOptions = async () => {
  try {
    const params = {paramsType: "CURR"}
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.baseInfoCustomerParams.list}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        currOptions.push({
          value: item.customParamCode,
          label: item.customParamCode
        });
      });
    } else {
      message.error(res.message || '获取币种类型数据失败');
    }
  } catch (error) {
    message.error('获取币种类型数据失败');
  }
}


const pCode = ref('')

// 组件挂载时根据编辑状态设置表单数据和禁用状态
onMounted(() => {


  firstAddSave = false;

  //获取客商信息
  getBuyerOptions();
  //获取海关币制
  getCurrOptions();
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = true
  }
})
</script>
