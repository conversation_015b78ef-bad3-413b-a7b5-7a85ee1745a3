import { h, reactive, ref } from 'vue'
import { Tag } from 'ant-design-vue'
import { baseColumns, createSorter, createDateSorter, createNumberSorter } from "@/view/common/baseColumns";
import { productClassify } from '@/view/common/constant'
import { useColumnsRender } from "../../common/useColumnsRender";
import { useMerchant } from "@/view/common/useMerchant";
import {useOrderColumnsCommon} from "@/view/dec/imported_cigarettes/head/useOrderColumnsCommon";
const { supplierList,getSupplierList } = useOrderColumnsCommon()
getSupplierList()

// 格式化数字为千分位分隔的工具函数
const formatNumber = (value) => {
  if (value === undefined || value === null || value === '') {
    return '';
  }
  // 将数字转换为字符串并添加千分位分隔符
  // 检查是否有小数部分
  const numValue = Number(value);
  if (Number.isNaN(numValue)) {
    return '';
  }

  const hasDecimal = numValue % 1 !== 0;
  if (hasDecimal) {
    // 如果有小数，保留原有小数位数
    return new Intl.NumberFormat('zh-CN').format(numValue);
  } else {
    // 如果没有小数，补充.00
    return new Intl.NumberFormat('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(numValue);
  }
};

const { baseColumnsExport, baseColumnsShow } = baseColumns()
const { cmbShowRender } = useColumnsRender()
const { merchantOptions, getMerchantOptions } = useMerchant()

// 初始化时获取供应商数据
await getMerchantOptions()

function getColumns() {
  const commColumns = reactive([
    'businessType',
    'payerName',
    'entrustCompany',
    'contractNo',
    'orderNumber',
    'paymentAmount',
    'curr',
    'financeFlag',
    'updateUserName',
    'updateTime',
    'documentStatus',
  ])

  // 导出字段设置
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])

  // table表格字段设置
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  // table表格字段设置
  const totalColumns = ref([
    {
      title: '操作',
      maxWidth: 80,
      width: 80,
      dataIndex: 'operation',
      key: 'operation',
      align: 'center',
      fixed: 'left',
    },
    {
      title: '业务类型',
      width: 150,
      align: 'center',
      dataIndex: 'businessType',
      key: 'businessType',
      resizable: true,
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,productClassify.businessType))
      }
    },
    {
      title: '付款供应商',
      width: 200,
      align: 'center',
      dataIndex: 'payerName',
      key: 'payerName',
      resizable: true,
      customRender: ({ text }) => {
        return h(<span></span>,  cmbShowRender(text,supplierList.value))
      }
    },
    {
      title: '委托单位',
      width: 200,
      align: 'center',
      dataIndex: 'entrustCompany',
      key: 'entrustCompany',
      resizable: true,
      customRender: ({ text }) => {
        return h(<span></span>,  cmbShowRender(text,supplierList.value))
      }
    },
    {
      title: '购销合同号',
      width: 150,
      align: 'center',
      dataIndex: 'contractNo',
      resizable: true,
      key: 'contractNo',
    },
    {
      title: '进/出货单号',
      width: 150,
      align: 'center',
      dataIndex: 'orderNumber',
      resizable: true,
      key: 'orderNumber',
    },

    {
      title: '金额',
      width: 150,
      align: 'center',
      dataIndex: 'paymentAmount',
      resizable: true,
      key: 'paymentAmount',
      customRender: ({ text }) => {
        return formatNumber(text);
      }
    },
    {
      title: '币种',
      width: 150,
      align: 'center',
      dataIndex: 'currency',
      key: 'currency',
      resizable: true,
    },
    {
      title: '发送财务系统',
      width: 150,
      align: 'center',
      dataIndex: 'financeFlag',
      resizable: true,
      key: 'financeFlag',
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,productClassify.isNot))
      }
    },

    {
      title: '制单人',
      width: 150,
      align: 'center',
      dataIndex: 'updateUserName',
      resizable: true,
      key: 'updateUserName'
    },
    {
      title: '制单时间',
      width: 150,
      align: 'center',
      dataIndex: 'updateTime',
      resizable: true,
      key: 'updateTime'
    },
    {
      title: '单据状态',
      width: 150,
      align: 'center',
      dataIndex: 'documentStatus',
      resizable: true,
      key: 'documentStatus',
      customRender: ({ text }) => {
        const tagColor = text === '2' ? 'error' : 'success';
        return h(Tag, { color: tagColor }, cmbShowRender(text, productClassify.data_status))
      }
    },


  ])

  return {
    columnsConfig,
    excelColumnsConfig,
    totalColumns
  }
}

export { getColumns }
