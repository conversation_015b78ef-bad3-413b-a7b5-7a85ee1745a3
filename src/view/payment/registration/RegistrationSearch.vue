<template>
  <a-form  layout="inline"  label-align="right"  :label-col="{ style: { width: '100px' } }" :model="searchParam"   class="cs-form  grid-container" >


    <!-- 业务类型 -->
    <a-form-item name="businessType" :label="'业务类型'" class="grid-item" :colon="false">
      <cs-select  optionFilterProp="label" option-label-prop="key" allow-clear show-search
                 v-model:value="searchParam.businessType" id="businessType">
        <a-select-option v-for="item in productClassify.bizType" :key="item.value + ' ' + item.label"
                         :value="item.value" :label="item.value + item.label">
          {{ item.value }} {{ item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>
    <!-- 数据状态 -->
    <a-form-item name="documentStatus"   :label="'单据状态'" class="grid-item"  :colon="false">
      <cs-select optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="searchParam.documentStatus" id="documentStatus">
        <a-select-option   class="cs-select-dropdown"  v-for="item in productClassify.data_status"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
          {{item.value}} {{item.label }}
        </a-select-option>
      </cs-select>
    </a-form-item>

    <!-- 合同编号 -->
    <a-form-item name="contractNo"   :label="'合同号'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.contractNo" />
    </a-form-item>

    <!-- 进货单号 -->
    <a-form-item name="orderNumber"   :label="'进货单号'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.orderNumber" />
    </a-form-item>


    <!-- 签约日期 -->
    <a-form-item name="signDate" label="制单日期" class="grid-item" :colon="false">
      <a-form-item-rest>
        <a-row>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.updateTimeFrom"
              id="updateTimeFrom"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
            />
          </a-col>
          <a-col :span="2" style="text-align: center">
            -
          </a-col>
          <a-col :span="11">
            <a-date-picker
              v-model:value="searchParam.updateTimeTo"
              size="small"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD"
              :locale="locale"
              style="width: 100%"
              placeholder=""
            />
          </a-col>
        </a-row>
      </a-form-item-rest>
    </a-form-item>

    <!-- 进货单号 -->
    <a-form-item name="updateUserName"   :label="'制单人'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.updateUserName" />
    </a-form-item>

  </a-form>
</template>

<script setup>
import {inject, onMounted, reactive} from 'vue'
import {productClassify} from "@/view/common/constant";
import CsSelect from "@/components/select/CsSelect.vue";
import ycCsApi from "@/api/ycCsApi";
import {message} from "ant-design-vue";

defineOptions({
  name: 'ContractSearch'
})
const searchParam = reactive({
  businessType: '',
  contractNo: '',
  orderNumber: '',
  documentStatus: '',
  updateTimeFrom: '',
  updateTimeTo: '',
  updateUserName: '',
})


/* 定义重置方法(注意前后顺序) */
const resetSearch = () => {
  Object.keys(searchParam).forEach(key => {
    searchParam[key] = '';
  });
}

onMounted(() => {

})

defineExpose({
  searchParam,
  resetSearch
})
</script>

<style scoped>

</style>
