import {baseColumns, createDateSorter, createNumberSorter, createSorter} from "@/view/common/baseColumns";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {h, reactive, ref} from "vue";
import {productClassify} from "@/view/common/constant";
import {Tag} from "ant-design-vue";

// 添加格式化数字的辅助函数
const formatNumber = (value) => {
  if (value === null || value === undefined || value === '') {
    return '0';
  }
  // 将值转换为数字
  const num = parseFloat(value);
  if (isNaN(num)) {
    return '0';
  }

  // 分别处理整数部分和小数部分
  const parts = num.toFixed(2).split('.');
  // 只对整数部分添加千位分隔符
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  // 组合整数和小数部分
  return parts.join('.');
};
const {baseColumnsExport, baseColumnsShow} = baseColumns()
const { cmbShowRender } = useColumnsRender()


export function getColumns() {

  const commColumns = reactive([
    'businessType',
    'contractNo',
    'purchaseOrderNo',
    'originalAmount',
    'totalAmount',
    'curr',
    'confirmTime',
    'insertTime',
    'insertUserName',
    'status',
    'sendToUf'
  ])

// 导出字段设置`
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])

  // table表格字段设置
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  // table表格字段设置
  const totalColumns = ref([
    {
      width: 80,
      minWidth:80,
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      resizable: true,
      align: 'center',
      fixed: 'left',
    },
    {
      title: '业务类型',
      width: 135,
      align: 'center',
      dataIndex: 'businessType',
      key: 'businessType',
      resizable: true,
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,productClassify.businessType))
      }
    },
    {
      title: '合同号',
      width: 100,
      align: 'center',
      dataIndex: 'contractNo',
      key: 'contractNo',
      resizable: true,
      ...createSorter('contractNo')
    },
    {
      title: '进/出货单号',
      width: 120,
      align: 'center',
      dataIndex: 'purchaseOrderNo',
      key: 'purchaseOrderNo',
      resizable: true,
      ...createSorter('purchaseOrderNo')
    },
    {
      title: '币种',
      width: 100,
      align: 'center',
      dataIndex: 'curr',
      key: 'curr',
      resizable: true,
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,[],'CURR'))
      }
    },
    {
      title: '外币金额',
      width: 100,
      align: 'center',
      dataIndex: 'originalAmount',
      key: 'originalAmount',
      resizable: true,
      customRender: ({ text }) => {
        return formatNumber(text);
      }
    },
    {
      title: '合计',
      width: 100,
      align: 'center',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      resizable: true,
      customRender: ({ text }) => {
        return formatNumber(text);
      }
    },
    {
      title: '发送用友',
      width: 100,
      align: 'center',
      dataIndex: 'sendToUf',
      key: 'sendToUf',
      resizable: true,
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,productClassify.isNot))
      }
    },
    {
      title: '单据状态',
      width: 100,
      align: 'center',
      dataIndex: 'status',
      key: 'status',
      resizable: true,
      customRender: ({ text }) => {
        const tagColor = text === '2' ? 'error' : 'success';
        return h(Tag, { color: tagColor }, cmbShowRender(text, productClassify.data_status))
      }
    },
    {
      title: '制单人',
      width: 100,
      align: 'center',
      dataIndex: 'createrUserName',
      key: 'createrUserName',
      resizable: true,
    },
    {
      title: '制单日期',
      width: 135,
      align: 'center',
      dataIndex: 'createrTime',
      key: 'createrTime',
      resizable: true,
    },
    {
      title: '确认时间',
      width: 135,
      align: 'center',
      dataIndex: 'confirmTime',
      key: 'confirmTime',
      resizable: true,
    },

  ])

  return{
    columnsConfig,
    excelColumnsConfig,
    totalColumns
  }
}


