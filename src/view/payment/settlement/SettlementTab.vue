<template>
  <section class="cs-action cs-action-tab">
    <div class="cs-tab">
      <a-tabs class="sticky-header"  v-model:activeKey="tabName" size="small" :tabBarStyle="tabBarStyle" >
        <a-tab-pane key="headTab" tab="货款结算" >
          <SettlementHeadEdit  ref="headTab"  :edit-config="editConfig"  @onEditBack="editBack"></SettlementHeadEdit>
        </a-tab-pane>
        <a-tab-pane v-if="showBody" key="file" tab="归档附件" @onEditBack="editBack" >
          <SettlementAttach :head-id="headId"  :operation-status="editConfig.editStatus" :edit-config="editConfig"></SettlementAttach>
        </a-tab-pane>
        <template #rightExtra>
            <div class="cs-tab-icon" @click="editBack">
              <GlobalIcon type="close-circle" style="color:#000"/>
            </div>
        </template>
      </a-tabs>
    </div>

  </section>
</template>

<script setup>

import {onMounted, reactive, ref, watch} from "vue";
import {editStatus} from "@/view/common/constant";
import SettlementHeadEdit from "./SettlementHeadEdit.vue";
import SettlementAttach from "@/view/payment/settlement/SettlementAttach.vue";

defineOptions({
  name:'NotifyTab'
})

const emit = defineEmits(['onEditBack'])

/* 定义editConfig 用于向子组件传递 */
const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});


/* 自定义样式 */
const tabBarStyle = {
  background:'#fff',
  position:'sticky',
  top:'0',
  zIndex:'100',
}

/* 激活Tab key */
const tabName = ref('headTab');

/* 总tab信息 */
const tabs = reactive({
  headTab:true,
  shipFrom:true,
})

/* 表头headId */
const headId = ref('')


/* 是否显示子模块 tab */
const showBody = ref(false)



/* 返回tab界面 */
const editBack = (val) => {
  if (val.editStatus === editStatus.EDIT){
    showBody.value = val.showBody
    headId.value = val.editData.sid
    props.editConfig.editStatus = val.editStatus
    props.editConfig.editData = val.editData
  }else {
    if (val) {
      emit('onEditBack', val)
    }
  }
}


/* 初始化操作 */
onMounted(()=>{
  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    // headId.value = props.editConfig.editData.sid
    showBody.value = true;
  } else if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    headId.value = props.editConfig.editData.sid
    showBody.value = true;
  }else if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    headId.value = props.editConfig.editData.sid
    showBody.value = true
  }
})



/* 监控tabName变化 */
watch(tabName, (value) => {
  for (let t in tabs) {
    tabs[t] = false
  }
  tabs[value] = true
})

</script>

<style lang="less" scoped>

</style>
