<template>
  <section  >

    <a-card size="small" title="货款结算" class="cs-card-form">
      <div class="cs-form">
        <a-form ref="formRef" labelAlign="right" :label-col="{ style: { width: '140px' } }" :rules="rules"
                :model="formData"   class=" grid-container">

          <a-form-item name="businessType" :label="'业务类型'" class="grid-item" :colon="false">
            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.businessType" id="businessType">
              <a-select-option v-for="item in productClassify.businessType"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>

          <a-form-item name="purchaseOrderNo" :label="'进货单号'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.purchaseOrderNo"/>
          </a-form-item>

          <a-form-item name="contractNo" :label="'合同号'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.contractNo"/>
          </a-form-item>

          <a-form-item name="customer" :label="'客户'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.customer"/>
          </a-form-item>

          <a-form-item name="payer" :label="'付款单位'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.payer"/>
          </a-form-item>

          <a-form-item name="foreignInvoiceNo" :label="'外商发票号'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.foreignInvoiceNo"/>
          </a-form-item>

          <a-form-item name="curr" :label="'币种'" class="grid-item" :colon="false">
            <a-select
              v-model:value="formData.curr"
              disabled
              style="width: 100%"
              size="small"
              placeholder="Please select"
              :options="currList"
            ></a-select>
          </a-form-item>

          <a-form-item name="exchangeRate" :label="'汇率'" class="grid-item" :colon="false">
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.exchangeRate" style="width: 100%" @pressEnter="calculateRmbAmount"/>
          </a-form-item>

          <a-form-item name="businessDate" :label="'业务日期'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="showDisable"
              v-model:value="formData.businessDate"
              id="createrTime"
              valueFormat="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
            />
          </a-form-item>

          <a-form-item name="originalAmount" :label="'原货币价'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" :value="formattedValues.originalAmount.value"/>
          </a-form-item>

          <a-form-item name="rmbAmount" :label="'人民币金额'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" :value="formattedValues.rmbAmount.value" style="width: 100%"/>
          </a-form-item>

          <a-form-item name="portTransferAmount" :label="'口岸调拨总价'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" :value="formattedValues.portTransferAmount.value"/>
          </a-form-item>

          <a-form-item name="agencyFee" :label="'代理费(不含税金额)'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" :value="formattedValues.agencyFee.value"/>
          </a-form-item>

          <a-form-item name="agencyFeeTax" :label="'代理费税额'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" :value="formattedValues.agencyFeeTax.value"/>
          </a-form-item>

          <a-form-item name="totalAmount" :label="'合计值'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" :value="formattedValues.totalAmount.value"/>
          </a-form-item>

          <a-form-item name="agencyFeeRate" :label="'代理费率'" class="grid-item" :colon="false">
            <a-input-number :disabled="showDisable" size="small" v-model:value="formData.agencyFeeRate" style="width: 100%" @pressEnter="onAgencyFeeRateChange"/>
          </a-form-item>

          <a-form-item name="agencyFeeTotal" :label="'代理费(价税合计)'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" :value="formattedValues.agencyFeeTotal.value"/>
          </a-form-item>

          <a-form-item name="productCategory" :label="'商品类别'" class="grid-item" :colon="false">
            <cs-select disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.productCategory" id="productCategory">
              <a-select-option v-for="item in merchandiseCategoriesMap"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>

          <a-form-item name="qty" :label="'数量'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" :value="formattedValues.qty.value"/>
          </a-form-item>

          <a-form-item name="sendToUf" :label="'发送用友'" class="grid-item"  :colon="false">
            <cs-select :disabled="showDisable"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.sendToUf" id="sendToUf">
              <a-select-option v-for="item in productClassify.isNot"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>

          <a-form-item name="remark" :label="'备注'" class="grid-item" :colon="false">
            <a-input :disabled="showDisable" size="small" v-model:value="formData.remark"/>
          </a-form-item>

          <a-form-item name="status" :label="'单据状态'" class="grid-item"  :colon="false">
            <cs-select :disabled="true"  optionFilterProp="label" option-label-prop="key"  allow-clear  show-search v-model:value="formData.status" id="status">
              <a-select-option v-for="item in productClassify.state"  :key="item.value + ' ' +item.label  " :value="item.value" :label=" item.value + item.label">
                {{item.value}} {{item.label }}
              </a-select-option>
            </cs-select>
          </a-form-item>

          <a-form-item name="createrUserName" :label="'制单人'" class="grid-item" :colon="false">
            <a-input :disabled="true" size="small" v-model:value="formData.createrUserName"/>
          </a-form-item>

          <a-form-item name="createrTime" :label="'制单日期'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="true"
              v-model:value="formData.createrTime"
              id="createrTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
            />
          </a-form-item>

          <a-form-item name="confirmTime" :label="'确认时间'" class="grid-item" :colon="false">
            <a-date-picker
              :disabled="true"
              v-model:value="formData.confirmTime"
              id="confirmTime"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              :locale="locale"
              size="small"
              style="width: 100%"
              placeholder=""
            />
          </a-form-item>

          <div class="cs-submit-btn merge-3">
            <a-button size="small" type="primary" @click="handlerSave" class="cs-margin-right"
                      v-show="props.editConfig.editStatus !== editStatus.SHOW ">保存
            </a-button>

            <a-button size="small" type="primary" @click="handlerConfirm" class="cs-margin-right"
                      v-show="props.editConfig.editStatus === editStatus.EDIT">确认
            </a-button>

            <a-button size="small" class="cs-margin-right cs-warning" @click="onBack(true)">返回
            </a-button>
          </div>
        </a-form>
      </div>
    </a-card>
<!--    <a-card size="small" v-if="props.editConfig.editStatus != 'add'"  class="cs-card-form">-->
<!--      <SettlementHeadList-->
<!--        ref="detailTableRef"-->
<!--        :disabled="showDisable"-->
<!--        :head-id="formData.sid"-->
<!--        :edit-config="props.editConfig"-->
<!--        @onHeadback="headBack"-->
<!--      />-->
<!--    </a-card>-->

<!--    <div>-->
<!--      &lt;!&ndash; 使用原生 <dialog> 元素 &ndash;&gt;-->
<!--      <cs-modal :visible="open" :title="'新增明细'" style="width: 80%" :footer="false" :closable="false">-->
<!--        <template #customContent>-->
<!--          <NotifyDetailTab v-if="modelView" :editConfig="props.editConfig" @onEditBack="handleCancel" @onHeadback="headBack" />-->
<!--        </template>-->
<!--      </cs-modal>-->
<!--    </div>-->

  </section>
</template>

<script setup>
import {editStatus, productClassify} from '@/view/common/constant'
import {message, Modal} from "ant-design-vue";
import {onMounted, reactive, ref, nextTick, createVNode, computed} from "vue";
import CsSelect from "@/components/select/CsSelect.vue";
import {usePCode} from "@/view/common/usePCode";
import {getMerchantCodeValueClient} from "@/api/bi/bi_client_info";
import {
  updateSettlementList,
  getDocNoNotifyHead,
  getUserInfo,
  confirmSettlement, getRateHead
} from "@/api/payment/payment_info";
const { getPCode } = usePCode()
import ycCsApi from "@/api/ycCsApi";
import SettlementHeadList from "@/view/payment/settlement/SettlementHeadList.vue";
import {updateContract} from "@/api/importedCigarettes/contract/contractApi";
import CsModal from "@/components/modal/cs-modal.vue";
// import NotifyDetailTab from "@/view/payment/notify/DetailTab/NotifyDetailTab.vue";
import {useCommon} from "@/view/common/useCommon";
import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
import {confirmPlan} from "@/api/importedCigarettes/plan/planApi";
import {now} from "ant-design-vue/es/_util/hooks/_vueuse/is";

const props = defineProps({
  editConfig: {
    type: Object,
    default: () => {
    }
  }
});

const {
  editConfig,
  // show,
  page,
  showSearch,
  getList,
  headSearch,
  // handleEditByRow,
  // handleViewByRow,
  operationEdit,
  onPageChange,
  handleShowSearch,
  handlerSearch,
  dataSourceList,
  tableLoading,
  getTableScroll,
  exportLoading,
  ajaxUrl,
  doExport,
  handlerRefresh,
  gridData

} = useCommon()

// 定义子组件 emit事件，用于子组件向父组件传递数据
const emit = defineEmits(['onEditBack','onHeadback']);
const onBack = (val) => {
  emit('onEditBack', val);
};
const merchandiseCategoriesMap = ref([])
// 是否禁用
const showDisable = ref(false)

// 格式化数字的辅助函数
const formatNumber = (value) => {
  if (value === null || value === undefined || value === '') {
    return '0';
  }
  // 将值转换为数字
  const num = parseFloat(value);
  if (isNaN(num)) {
    return '0';
  }
  // 使用 toLocaleString 添加千位分隔符
  return num.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

// 表单数据
const formData = reactive({
  sid: '',
  businessType: '1', // 业务类型，默认为国营贸易进口卷烟
  purchaseOrderNo: '', // 进货单号
  contractNo: '', // 合同号
  customer: '', // 客户
  payer: '中国烟草上海进出口有限责任公司', // 付款单位，默认固定值
  foreignInvoiceNo: '', // 外商发票号
  curr: 'CNY', // 币种
  exchangeRate: 0, // 汇率
  businessDate: '', // 业务日期
  originalAmount: 0, // 原货币价
  rmbAmount: 0, // 人民币金额
  portTransferAmount: 0, // 口岸调拨总价
  agencyFee: 0, // 代理费(不含税金额)
  agencyFeeTax: 0, // 代理费税额
  totalAmount: 0, // 合计值
  agencyFeeRate: 4, // 代理费率，默认为4
  agencyFeeTotal: 0, // 代理费(价税合计)
  productCategory: '', // 商品类别
  qty: 0, // 数量
  sendToUf: '0', // 发送用友，默认为0是
  remark: '', // 备注
  status: '0', // 单据状态
  confirmTime: '', // 确认时间
  createrUserName: '', // 制单人
  createrTime: '', // 制单日期
})

// 格式化显示的计算属性
const formattedValues = {
  originalAmount: computed(() => formatNumber(formData.originalAmount)),
  rmbAmount: computed(() => formatNumber(formData.rmbAmount)),
  portTransferAmount: computed(() => formatNumber(formData.portTransferAmount)),
  agencyFee: computed(() => formatNumber(formData.agencyFee)),
  agencyFeeTax: computed(() => formatNumber(formData.agencyFeeTax)),
  totalAmount: computed(() => formatNumber(formData.totalAmount)),
  agencyFeeTotal: computed(() => formatNumber(formData.agencyFeeTotal)),
  qty: computed(() => formatNumber(formData.qty))
}

// 校验规则
const rules = {
  businessType: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  purchaseOrderNo: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  contractNo: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  customer: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  payer: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  foreignInvoiceNo: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  curr: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  exchangeRate: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  businessDate: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  originalAmount: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  rmbAmount: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  portTransferAmount: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  agencyFee: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  agencyFeeTax: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  totalAmount: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  agencyFeeRate: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  agencyFeeTotal: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  productCategory: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  qty: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  status: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  createrUserName: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
  createrTime: [
    {required: true, message: '不能为空', trigger: 'blur'},
  ],
}
const currList = ref([])
const rateList = ref([])
const pCode = ref('')
const detailTableRef = ref();
const open = ref(false)
const modelView = ref(false);
const tableData = ref([]);
const editableKeys = ref([]);
//基础资料-客商信息
const buyerOptions = reactive([])

const handleCancel = async () => {
  modelView.value = false;
  open.value = false;
  // 等待 DOM 更新完成
  await nextTick();
  if (detailTableRef.value) {
    detailTableRef.value.getList();
  }
};

const headBack = async (val) =>{
  formData.payAmt = val.payAmt
  formData.payAmtRmb = val.payAmtRmb
}


const getBuyerOptions = async () => {
  try {
    const params = {}
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.bizMerchant.list}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      res.data.forEach(item => {
        buyerOptions.push({
          value: item.merchantCode,
          label: item.merchantNameCn
        });
      });
    } else {
      message.error(res.message || '获取客商数据失败');
    }
  } catch (error) {
    message.error('获取客商数据失败');
  }
}



// 计算 RMB 金额
const calculateRmbAmount = async () => {
  // 确保数值有效性
  if (typeof formData.originalAmount === 'number' && typeof formData.exchangeRate === 'number') {
    formData.rmbAmount = formData.originalAmount * formData.exchangeRate;
    // 保留两位小数
    formData.rmbAmount = Number(formData.rmbAmount.toFixed(2));

    // 计算代理费(不含税金额)
    if (typeof formData.portTransferAmount === 'number' && typeof formData.agencyFeeRate === 'number') {
      formData.agencyFee = formData.portTransferAmount * (formData.agencyFeeRate / 100);
      formData.agencyFee = Number(formData.agencyFee.toFixed(2));

      // 计算代理费税额
      formData.agencyFeeTax = formData.agencyFee * 0.06;
      formData.agencyFeeTax = Number(formData.agencyFeeTax.toFixed(2));

      // 计算代理费(价税合计)
      formData.agencyFeeTotal = formData.agencyFee + formData.agencyFeeTax;
      formData.agencyFeeTotal = Number(formData.agencyFeeTotal.toFixed(2));

      // 计算合计值
      formData.totalAmount = formData.rmbAmount + formData.agencyFeeTotal;
      formData.totalAmount = Number(formData.totalAmount.toFixed(2));
    }
  }
};

// 代理费率变化时的计算函数
const onAgencyFeeRateChange = () => {
  // 确保口岸调拨总价有效
  if (typeof formData.portTransferAmount === 'number' && typeof formData.agencyFeeRate === 'number') {
    // 计算代理费(不含税金额)
    formData.agencyFee = formData.portTransferAmount * formData.agencyFeeRate;
    formData.agencyFee = Number(formData.agencyFee.toFixed(2));

    // 计算代理费税额
    formData.agencyFeeTax = formData.agencyFee * 0.06;
    formData.agencyFeeTax = Number(formData.agencyFeeTax.toFixed(2));

    // 计算代理费(价税合计)
    formData.agencyFeeTotal = formData.agencyFee + formData.agencyFeeTax;
    formData.agencyFeeTotal = Number(formData.agencyFeeTotal.toFixed(2));

    // 计算合计值
    formData.totalAmount = formData.rmbAmount + formData.agencyFeeTotal;
    formData.totalAmount = Number(formData.totalAmount.toFixed(2));
  }
};

// 初始化操作
onMounted(() => {


  getPCode().then(res=>{
    pCode.value = res;
    currList.value = Object.entries(pCode.value.CURR).map(([value, label]) => ({
      label: `${value} ${label}`,
      value
    }));
  })

  getBuyerOptions();

  if (props.editConfig && props.editConfig.editStatus === editStatus.ADD) {
    showDisable.value = false

    Object.assign(formData, {});
    const now = new Date();

// 年-月-日
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从 0 开始，需加1
    const day = String(now.getDate()).padStart(2, '0');

// 时:分:秒
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');

// 组合成目标格式
    formData.bizDate =  `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    formData.createrTime =  `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    getDocNo()
    getUpdateUser()
    getRate()


  }
  // 初始化数据
  if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = false
  }
  if (props.editConfig && props.editConfig.editStatus === editStatus.SHOW) {
    Object.assign(formData, props.editConfig.editData);
    showDisable.value = true
  }

  getMerchantCodeValueClient().then((res)=>{
    if (res.code === 200){
      //商品类别
      if (typeof(res.data.merchandiseCategories) !== "undefined"){
        res.data.merchandiseCategories.map(item => {
          merchandiseCategoriesMap.value.push({
            label: item.value,
            value: item.label
          })
        })
      }
    }
  })
});




// vue3中使用ref,需要先定义，然后在模板中使用。因为在模板渲染之前，DOM 元素还不存在。
const formRef = ref(null);

// 确认

/* 确认事件 */
const handlerConfirm = () => {

  // 检查选中数据的状态
  if (formData.status === '1') {
    message.warning('该数据已经确认，无需重复操作');
    return;
  }


  // 弹出确认框
  Modal.confirm({
    title: '提醒',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: '是否确认所选项？',
    onOk() {

      // 这里需要调用确认API
      const sid = formData.sid;
      confirmSettlement(sid).then(res => {
        if (res.code === 200) {
          formData.status = '1'
          formData.confirmTime=res.data.confirmTime
          // 更新编辑状态为只读，这将隐藏保存和确认按钮
          props.editConfig.editStatus = editStatus.SHOW
          // 设置表单为禁用状态
          showDisable.value = true
          message.success("确认成功！")
        }else {
          // message.error(res.message || '确认失败')
        }
      }).finally(() => {

      })
    },
    onCancel() {
      // 取消操作
    },
  });
}


// 保存
const handlerSave = async () => {

  formRef.value
    .validate()
    .then(() => {
      if (props.editConfig && props.editConfig.editStatus === editStatus.ADD){
        calculateRmbAmount()
        onAgencyFeeRateChange()
        updateSettlementList(formData).then((res)=>{
          if (res.code === 200){
            message.success('新增成功!')
            formData.sid = res.data.sid
            props.editConfig.editData.sid = res.data.sid
            props.editConfig.editStatus = editStatus.EDIT
            // 保存成功后刷新明细表格数据
            if (formRef.value) {
              formRef.value.getList();
            }
          }else {
            message.error(res.message)
          }
        })
      }else if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT){
        calculateRmbAmount()
        onAgencyFeeRateChange()
        updateSettlementList(formData.sid,formData).then((res)=>{
          if (res.code === 200){
            message.success('修改成功!')
            // 保存成功后刷新明细表格数据
            // if (formRef.value) {
            //   formRef.value.getList();
            // }
            formData.createrUserName = res.data.updateUserName
            formData.createrTime = res.data.updateTime

          }else {
            message.error(res.message)
          }
        })
      }
    })
    .catch(error => {
    })
};

const handlerSaveClose = async () => {
  formRef.value
    .validate()
    .then(() => {
      if (props.editConfig && props.editConfig.editStatus === editStatus.ADD){
        calculateRmbAmount()
        onAgencyFeeRateChange()
        updateSettlementList(formData).then((res)=>{
          if (res.code === 200){
            message.success('新增成功!')
            onBack(true)
          }else {
            message.error(res.message)
          }
        })
      }else if (props.editConfig && props.editConfig.editStatus === editStatus.EDIT){
        calculateRmbAmount()
        onAgencyFeeRateChange()
        updateSettlementList(formData.sid,formData).then((res)=>{
          if (res.code === 200){
            message.success('修改成功!')
            onBack(true)
          }else {
            message.error(res.message)
          }
        })
      }
    })
    .catch(error => {
    })
}

const getDocNo = () => {
  getDocNoNotifyHead().then((res)=>{
    if (res.data !=null){
      formData.docNo =  res.data
    }
  })
}
const getRate = () => {
  getRateHead(formData.curr).then((res)=>{
    if (res.data !=null){
      formData.rate =  Number(Number(res.data).toFixed(6))
      calculateRmbAmount()
    }
  })

}
const getUpdateUser = () => {
  getUserInfo().then((res)=>{
    if (res.data !=null){
      formData.updateUserName =  res.data.loginName
    }
  })
}

const handlerAddList = ()=>{
  modelView.value = true;
  open.value = true;
}



</script>

<style lang="less" scoped>
/* 统一禁用状态下的输入框字体颜色 */
:deep(.ant-input[disabled]),
:deep(.ant-input-number-disabled),
:deep(.ant-select-disabled .ant-select-selection-item),
:deep(.ant-picker-input > input[disabled]) {
  color: rgba(0, 0, 0, 0.85) !important; /* 使用较深的黑色 */
}

/* 确保数字输入框和普通输入框在禁用状态下颜色一致 */
:deep(.ant-input-number-disabled .ant-input-number-input) {
  color: rgba(0, 0, 0, 0.85) !important;
}

</style>



