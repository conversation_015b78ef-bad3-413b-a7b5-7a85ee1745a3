import {baseColumns, createDateSorter, createNumberSorter, createSorter} from "@/view/common/baseColumns";
import {useColumnsRender} from "@/view/common/useColumnsRender";
import {h, reactive, ref} from "vue";
import {productClassify} from "@/view/common/constant";
import {Tag} from "ant-design-vue";
const {baseColumnsExport, baseColumnsShow} = baseColumns()
const { cmbShowRender } = useColumnsRender()
// 格式化数字为千分位分隔的工具函数
const formatNumber = (value) => {
  if (value === undefined || value === null || value === '') {
    return '';
  }

  // 转换为数字并检查有效性
  const number = Number(value);
  if (isNaN(number)) {
    return '';
  }

  // 配置 NumberFormat 选项
  return new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: 2,  // 至少两位小数
    maximumFractionDigits: 10
  }).format(number);
};

export function getColumns() {

  const commColumns = reactive([
    'bizType',
    'payee',
    'contractNo',
    'orderNumber',
    'payAmt',
    'curr',
    'cfmTime',
    'updateTime',
    'updateUserName',
    'docStatus',
    'sendUfida',
    'prepayFlag',
  ])

// 导出字段设置`
  const excelColumnsConfig = ref([
    ...baseColumnsExport,
    ...commColumns
  ])

  // table表格字段设置
  const columnsConfig = ref([
    ...baseColumnsShow,
    ...commColumns
  ])

  // table表格字段设置
  const totalColumns = ref([
    {
      width: 80,
      minWidth:80,
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      resizable: true,
      align: 'center',
      fixed: 'left',
    },
    {
      title: '业务类型',
      width: 150,
      align: 'center',
      dataIndex: 'bizType',
      key: 'bizType',
      resizable: true,
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,productClassify.businessType))
      }
    },
    {
      title: '收款方',
      width: 180,
      align: 'center',
      dataIndex: 'payee',
      key: 'payee',
      resizable: true,
    },
    {
      title: '合同号',
      width: 180,
      align: 'center',
      dataIndex: 'contractNo',
      key: 'contractNo',
      resizable: true,
      ...createSorter('contractNo')
    },
    {
      title: '进/出货单号',
      width: 180,
      align: 'center',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      resizable: true,
      ...createSorter('orderNumber')
    },
    {
      title: '付款金额',
      width: 180,
      align: 'center',
      dataIndex: 'payAmt',
      key: 'payAmt',
      resizable: true,
      ...createNumberSorter('payAmt'),
      customRender: ({ text }) => {
        return formatNumber(text);
      }
    },
    {
      title: '币种',
      width: 180,
      align: 'center',
      dataIndex: 'curr',
      key: 'curr',
      resizable: true,
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,[],'CURR'))
      }
    },
    {
      title: '预付标志',
      width: 180,
      align: 'center',
      dataIndex: 'prepayFlag',
      key: 'prepayFlag',
      resizable: true,
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,productClassify.isNot))
      }
    },
    {
      title: '发送用友',
      width: 180,
      align: 'center',
      dataIndex: 'sendUfida',
      key: 'sendUfida',
      resizable: true,
      customRender: ({ text }) => {
        return h(<div></div>, cmbShowRender(text,productClassify.isNot))
      }
    },
    {
      title: '单据状态',
      width: 180,
      align: 'center',
      dataIndex: 'docStatus',
      key: 'docStatus',
      resizable: true,
      customRender: ({ text }) => {
        const tagColor = text === '2' ? 'error' : 'success';
        return h(Tag, { color: tagColor }, cmbShowRender(text, productClassify.data_status))
      }
    },
    {
      title: '制单人',
      width: 180,
      align: 'center',
      dataIndex: 'updateUserName',
      key: 'updateUserName',
      resizable: true,
    },
    {
      title: '制单日期',
      width: 180,
      align: 'center',
      dataIndex: 'updateTime',
      key: 'updateTime',
      resizable: true,
    },
    {
      title: '确认时间',
      width: 180,
      align: 'center',
      dataIndex: 'cfmTime',
      key: 'cfmTime',
      resizable: true,
      ...createDateSorter('cfmTime')
    },

  ])

  return{
    columnsConfig,
    excelColumnsConfig,
    totalColumns
  }
}


