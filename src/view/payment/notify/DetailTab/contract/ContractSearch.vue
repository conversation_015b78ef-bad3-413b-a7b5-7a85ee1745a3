<template>
  <a-form  layout="inline"  label-align="right"  :label-col="{ style: { width: '100px' } }" :model="searchParam"   class="cs-form  grid-container" >

<!--    合同号-->
    <a-form-item name="contractNo"   :label="'合同号'" class="grid-item"  :colon="false">
      <a-input  size="small" v-model:value="searchParam.contractNo" />
    </a-form-item>
<!--    订单号-->
<!--    <a-form-item name="orderNo"   :label="'订单号'" class="grid-item"  :colon="false">-->
<!--      <a-input  size="small" v-model:value="searchParam.orderNo" />-->
<!--    </a-form-item>-->
  </a-form>
</template>

<script setup>
import {inject, onMounted, reactive} from 'vue'
import {productClassify} from "@/view/common/constant";
import CsSelect from "@/components/select/CsSelect.vue";
import {useCommon} from "@/view/common/useCommon";

defineOptions({
  name: 'ContractSearch'
})

const {
  editConfig,

} = useCommon()

const props = defineProps({
  headId: {
    type: Object,
    default: () => {
    }
  }
});
const searchParam = reactive({
  contractNo:'',
  headId:props.headId
})
/* 定义重置方法(注意前后顺序) */
const resetSearch = () => {
  Object.keys(searchParam).forEach(key => {
    searchParam[key] = '';
  });
}
defineExpose({searchParam,resetSearch});
onMounted(() => {

});





</script>
