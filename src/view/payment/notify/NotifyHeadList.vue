<template>
  <section  class="dc-section">
    <div class="cs-action"  v-show="show">
      <!-- 查询列表区域 -->
      <div class="cs-search">
        <a-card :bordered="false">
          <bread-crumb>
            <div ref="area_head">
              <div class="search-btn">
                <a-button size="small" type="primary" class="cs-margin-right cs-refresh" @click="handlerRefresh" v-show="showSearch">
                  <template #icon>
                    <GlobalIcon type="redo" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" class="cs-margin-right" @click="handlerSearch">
                  {{localeContent('m.common.button.query')}}
                  <template #icon>
                    <GlobalIcon type="search" style="color:#fff"/>
                  </template>
                </a-button>
                <a-button size="small" type="primary" danger class="cs-margin-right cs-warning" @click="handleShowSearch">
                  <template #icon>
                    <GlobalIcon v-show="!showSearch" type="down" style="color:#fff"/>
                    <GlobalIcon v-show="showSearch" type="up" style="color:#fff"/>
                  </template>
                </a-button>
              </div>
            </div>
          </bread-crumb>
          <div class="separateLine"></div>
          <div ref="area_search">
            <div v-show="showSearch">
              <NotifyHeadSearch ref="headSearch" />
            </div>
          </div>
        </a-card>
      </div>
      <!-- 操作按钮区域 -->
      <div class="cs-action-btn">
        <div class="cs-action-btn-item" v-has="['yc-cs:notify:add']">
          <a-button size="small" @click="handlerAdd" >
            <template #icon>
              <GlobalIcon type="plus" style="color:green"/>
            </template>
            {{localeContent('m.common.button.add')}}
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:notify:edit']">
          <a-button  size="small"  @click="handlerEdit">
            <template #icon>
              <GlobalIcon type="form" style="color:orange"/>
            </template>
            {{localeContent('m.common.button.update')}}
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:notify:delete']">
          <a-button  size="small" :loading="deleteLoading" @click="handlerDelete">
            <template #icon>
              <GlobalIcon type="delete" style="color:red"/>
            </template>
            {{localeContent('m.common.button.delete')}}
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:notify:export']">
          <a-button  size="small" :loading="exportLoading" @click="handlerExport">
            <template #icon>
              <GlobalIcon type="folder-open" style="color:orange"/>
            </template>
            {{localeContent('m.common.button.export')}}
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:notify:confirm']">
          <a-button  size="small" :loading="confirmLoading" @click="handlerConfirm">
            <template #icon>
              <GlobalIcon type="check" style="color:orange"/>
            </template>
            确认
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:notify:copy']">
          <a-button  size="small" :loading="copyLoading" @click="handlerCopy">
            <template #icon>
              <GlobalIcon type="snippets" style="color:blue"/>
            </template>
            {{localeContent('m.common.button.copy')}}
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:notify:void']">
          <a-button  size="small" :loading="voidLoading" @click="handlerInvalid">
            <template #icon>
              <GlobalIcon type="close-square" style="color:red"/>
            </template>
            作废
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:notify:back']">
          <a-button  size="small" :loading="backLoading" @click="handlerBack">
            <template #icon>
              <GlobalIcon type="swap-left" style="color:brown"/>
            </template>
            退单
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:notify:redFlush']">
          <a-button  size="small" :loading="redFlushLoading" @click="handlerRedFlush">
            <template #icon>
              <GlobalIcon type="up" style="color:red"/>
            </template>
            红冲
          </a-button>
        </div>
        <div class="cs-action-btn-item" v-has="['yc-cs:notify:print']">
<!--          <a-button  size="small" :loading="printLoading" @click="handlerPrint">-->
<!--            <template #icon>-->
<!--              <GlobalIcon type="folder-open" style="color:green"/>-->
<!--            </template>-->
<!--            打印付款通知-->
<!--          </a-button>-->
          <a-dropdown>
            <template #overlay>

              <a-menu >
                <a-menu-item key="1" @click="handlerPrint('pdf')">打印付款通知(PDF)</a-menu-item>
                <a-menu-item key="2" @click="handlerPrint('excel')">打印付款通知(EXCEL)</a-menu-item>
              </a-menu>
            </template>
            <a-button type="ghost">
              打印付款通知
              <GlobalIcon class="btn-icon" type="folder-open"  style="color:green"/>
              <DownOutlined />
            </a-button>
          </a-dropdown>
        </div>
        <!--          <div class="cs-action-btn-item"  v-has="['yc-cs:notify:import']">-->
        <!--            <a-button  size="small"  @click="handlerImport">-->
        <!--              <template #icon>-->
        <!--                <GlobalIcon type="file-excel" style="color:deepskyblue"/>-->
        <!--              </template>-->
        <!--              {{localeContent('m.common.button.import')}}-->
        <!--            </a-button>-->
        <!--          </div>-->



        <div class="cs-action-btn-settings">
          <!-- 自定义显示组件 -->
          <CsTableColSettings
            :resId="tableKey"
            :tableKey="tableKey+'-client_code'"
            :initSettingColumns="originalColumns"
            :showColumnSettings="true"
            @customColumnChange="customColumnChange"
          >
          </CsTableColSettings>
        </div>





      </div>

      <!-- 表格区域 -->
      <div  v-if="showColumns && showColumns.length > 0">
        <s-table
          :animate-rows="false"
          ref="tableRef"
          class="cs-action-item"
          size="small"
          :scroll="{ y: tableHeight,x:400 }"
          bordered
          column-drag
          :pagination="false"
          :columns="showColumns.length > 0 ?showColumns:totalColumns"
          :data-source="dataSourceList"
          :row-selection="{  selectedRowKeys: gridData.selectedRowKeys, onChange: onSelectChange }"
          :loading="tableLoading"
          row-key="sid"
          :custom-row="customRow"
        >
          <!-- 操作 -->
          <template #bodyCell="{ column,record }">

            <template v-if="['payee'].includes(column.dataIndex)">
              <div>
                <span>
                      {{ formatBuyerOptions(record.payee) }}
                </span>
              </div>
            </template>


            <template v-if="column.key === 'operation'">
              <div class="operation-container">
                <a-button
                  size="small"
                  type="link"
                  @click="handleEditByRow(record)"
                  :style="operationEdit('edit')"
                >
                  <template #icon>
                    <GlobalIcon type="form" style="color:#e93f41"/>
                  </template>
                </a-button>
                <a-button
                  size="small"
                  type="link"
                  @click="handleViewByRow(record)"
                  :style="operationEdit('view')"
                >
                  <template #icon>
                    <GlobalIcon type="search" style="color:#1677ff"/>
                  </template>
                </a-button>

              </div>
            </template>
          </template>
        </s-table>
      </div>
      <!-- 分页 -->
      <div class=cs-pagination           v-if="showColumns && showColumns.length > 0">
        <div class="count-number">
          <span>共 {{ page.total }} 条</span>
        </div>
        <a-pagination size="small" v-model:current="page.current" show-size-changer  :page-size="page.pageSize" :total="page.total"  @change="onPageChange">
          <template #buildOptionText="props">
            <span >{{ props.value }}条/页</span>
          </template>
        </a-pagination>

      </div>
    </div>

    <!-- 新增 编辑数据 -->
    <div v-if="!show">
      <NotifyTab :editConfig="editConfig" @onEditBack="handlerOnBack" />
    </div>


    <!-- 导入数据 -->
    <!--    <ImportIndex :importShow="importShow" :importConfig="importConfig"   @onImportSuccess="importSuccess"></ImportIndex>-->


  </section>


</template>

<script setup>
/* 使用自定义 Hook 函数 */
import {useCommon} from '@/view/common/useCommon'
import {createVNode, onMounted, provide, reactive, ref, watch,computed} from "vue";
import NotifyHeadSearch from "@/view/payment/notify/NotifyHeadSearch";
import {getColumns} from "@/view/payment/notify/NotifyHeadColumns";
import NotifyTab from "@/view/payment/notify/NotifyTab";
import {message, Modal} from "ant-design-vue";
import BreadCrumb from "@/components/breadcrumb/BreadCrumb.vue";
import ExclamationCircleOutlined from "@ant-design/icons-vue/lib/icons/ExclamationCircleOutlined";
import {
  backContract,
  confirmContract,
  copyContract,
  deleteNotifyHead,
  invalidContract, printNotify, redFlushContract
} from "@/api/payment/payment_info";
const { totalColumns } = getColumns()
import {ImportIndex} from 'yao-import'
import {localeContent} from "../../utils/commonUtil";
import { useImport } from "@/view/common/useImport";
import ycCsApi from "@/api/ycCsApi";
import CsTableColSettings from "@/components/settings/CsTableColSettings.vue";
import {useRoute} from "vue-router";
import {editStatus} from "@/view/common/constant";
import {deepClone} from "@/view/utils/common";
const { importConfig } = useImport()


/* 引入通用方法 */
const {
  editConfig,
  show,
  page,
  showSearch,
  headSearch,
  handleViewByRow,
  operationEdit,
  onPageChange,
  handleShowSearch,
  handlerSearch,
  dataSourceList,
  tableLoading,
  getTableScroll,
  exportLoading,
  getList,
  ajaxUrl,
  doExport,
  handlerRefresh,
  gridData

} = useCommon()



defineOptions({
  name: 'NotifyHeadList',
});



const importShow = ref(false)


const buyerOptions =  ref([])


const getBuyerOptions = async () => {
  try {
    const params = {}
    const res = await window.majesty.httpUtil.postAction(
      `${ycCsApi.bizMerchant.list}`, params
    );
    if (res.code === 200) {
      // 将接口返回的数据添加到数组中
      // 清空并更新数据（避免重复）
      buyerOptions.value = res.data.map(item => ({
        value: item.merchantCode,
        label: `${item.merchantCode} ${item.merchantNameCn}` // 增强可读性
      }))
    } else {
      message.error(res.message || '获取客商数据失败');
    }
  } catch (error) {
    message.error('获取客商数据失败');
  }
}
// 使用computed优化查找性能
const buyerMap = computed(() =>
  new Map(buyerOptions.value.map(opt => [opt.value, opt.label]))
)

// 格式化显示（带fallback处理）
const formatBuyerOptions = (code) => {
  return buyerMap.value.get(code) || code
}
onMounted(fn => {


  ajaxUrl.selectAllPage = ycCsApi.payment.notifyHead.list
  ajaxUrl.exportUrl = ycCsApi.payment.notifyHead.export

  tableHeight.value = getTableScroll(100,'');

  getList()

  initCustomColumn()
  getBuyerOptions()


})

const tableHeight = ref('')


/* 双击行进入编辑页面 */
const handleRowDblclick = (record) => {
  handleEditByRow(record)
};
// 自定义行属性
const customRow = (record) => {
  return {
    onDblclick: () => {
      handleRowDblclick(record);
    }, style: {cursor: 'pointer'}
  };
};

/* 选中事件(这里的事件，顺序不能乱，必须key,后面点跟实体，不然回显出问题。) */
const onSelectChange = (selectedRowKeys, rowSelectData) => {
  gridData.selectedData = rowSelectData;
  gridData.selectedRowKeys = selectedRowKeys;
};


/* 按钮loading */
const deleteLoading = ref(false)
const confirmLoading = ref(false)
const copyLoading = ref(false)
const backLoading = ref(false)
const voidLoading = ref(false)
const printLoading = ref(false)


/* 返回事件 */
const handlerOnBack = (flag) => {
  show.value = !show.value;
  if (flag){
    getList()
  }
}

/* 新增数据 */
const handlerAdd = ()=>{
  editConfig.value.editStatus = editStatus.ADD
  editConfig.value.editData =  {}
  show.value = !show.value;
}


/* 编辑数据 */
const handlerEdit = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1){
    message.warning('只能选择一条数据')
    return
  }
  if (gridData.selectedData[0].docStatus !== '0'){
    editConfig.value.editStatus = editStatus.SHOW
    editConfig.value.editData =  gridData.selectedData[0]
  }else {
    editConfig.value.editStatus = editStatus.EDIT
    editConfig.value.editData =  gridData.selectedData[0]
  }



  show.value =!show.value;
}


function handleEditByRow(row) {
  // 在这里添加处理编辑行的逻辑
  show.value = !show.value
  if (row.docStatus !== '0'){
    editConfig.value.editStatus = editStatus.SHOW
    editConfig.value.editData =  row
  }else {
    editConfig.value.editStatus = editStatus.EDIT
    editConfig.value.editData =  row
  }
}

/* 确认事件 */
const handlerConfirm = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1){
    message.warning('只能选择一条数据')
    return
  }
  // 弹出确认框
  Modal.confirm({
    title: '提醒',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: '确认执行此操作吗？',
    onOk() {
      confirmLoading.value = true
      // 这里需要调用确认API
      const params = {
        sid : gridData.selectedRowKeys[0]
      }
      confirmContract(params).then(res => {
        if (res.code === 200) {
          message.success("确认成功！")
          getList()
        }else {
          message.error(res.message)
        }
      }).finally(() => {
        confirmLoading.value = false
      })
    },
    onCancel() {
      // 取消操作
    },
  });
}
/* 复制事件 */
const handlerCopy = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1){
    message.warning('只能选择一条数据')
    return
  }
  // 弹出确认框
  Modal.confirm({
    title: '提醒',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '确认',
    cancelText: '取消',
    content: '是否复制所选项？',
    onOk() {
      copyLoading.value = true
      // 这里需要调用确认API
      const params = {
        sid : gridData.selectedRowKeys[0]
      }
      copyContract(params).then(res => {
        if (res.code === 200) {
          message.success("复制成功！")
          getList()
        }else {
          message.error(res.message)
        }
      }).finally(() => {
        copyLoading.value = false
      })
    },
    onCancel() {
      // 取消操作
    },
  });
}


/* 作废事件 */
const handlerInvalid = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1){
    message.warning('只能选择一条数据')
    return
  }
  // 弹出确认框
  Modal.confirm({
    title: '提醒',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '作废',
    cancelText: '取消',
    content: '确认作废所选项吗？',
    onOk() {
      voidLoading.value = true
      // 这里需要调用作废API
      const params = {
        sid : gridData.selectedRowKeys[0]
      }
      invalidContract(params).then(res => {
        if (res.code === 200) {
          message.success("作废成功！")
          getList()
        } else {
          message.error(res.message)
        }
      }).finally(() => {
        voidLoading.value = false
      })

    },
    onCancel() {
      // 取消操作
    },
  });
}
/* 退单事件 */
const handlerBack = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1){
    message.warning('只能选择一条数据')
    return
  }
  // 弹出确认框
  Modal.confirm({
    title: '提醒',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '退单',
    cancelText: '取消',
    content: '是否退单所选项？',
    onOk() {
      backLoading.value = true
      // 这里需要调用作废API
      const params = {
        sid : gridData.selectedRowKeys[0]
      }
      backContract(params).then(res => {
        if (res.code === 200) {
          message.success("退单成功！")
          getList()
        }else {
          message.error(res.message)
        }
      }).finally(() => {
        backLoading.value = false
      })

    },
    onCancel() {
      // 取消操作
    },
  });
}

const redFlushLoading = ref(false)
/* 红冲事件 */
const handlerRedFlush = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1){
    message.warning('只能选择一条数据')
    return
  }
  // 弹出确认框
  Modal.confirm({
    title: '提醒',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '红冲',
    cancelText: '取消',
    content: '是否将该票数据置为红冲？',
    onOk() {
      redFlushLoading.value = true
      // 这里需要调用作废API
      const params = {
        sid : gridData.selectedRowKeys[0]
      }
      redFlushContract(params).then(res => {
        if (res.code === 200) {
          message.success("红冲成功！")
          getList()
        }else {
          message.error(res.message)
        }
      }).finally(() => {
        redFlushLoading.value = false
      })

    },
    onCancel() {
      // 取消操作
    },
  });
}

/* 打印付款通知 */
const handlerPrint = (type) => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1){
    message.warning('只能选择一条数据')
    return
  }

  printLoading.value = true
  const params = {
    sid: gridData.selectedRowKeys[0]
  }
  window.majesty.httpUtil.downloadFile(
    `${ycCsApi.payment.notifyHead.print}/${type}`, null,params,'post',null
  ).then(res => {
  }).catch(() => {
  }).finally(() => {
    printLoading.value = false
  })

}


/* 删除数据 */
const handlerDelete = () => {
  if (gridData.selectedRowKeys.length <= 0){
    message.warning('请选择一条数据')
    return
  }
  if (gridData.selectedRowKeys.length > 1){
    message.warning('只能选择一条数据')
    return
  }
  for (let selectedDatum of gridData.selectedData) {
    if(selectedDatum.docStatus !== '0'){
      message.warning('仅编制状态数据允许删除！')
      return
    }
  }
  // 弹出确认框
  Modal.confirm({
    title: '提醒?',
    icon: createVNode(ExclamationCircleOutlined),
    okText: '删除',
    cancelText: '取消',
    content: '确认删除所选项吗？',
    onOk() {
      deleteLoading.value = true
      deleteNotifyHead(gridData.selectedRowKeys).then(res => {
        if (res.code === 200) {
          message.success("删除成功！")
          getList()
        }else {
          message.error(res.message)
        }
      }).finally(() => {
        deleteLoading.value = false
      })
    },
    onCancel() {

    },
  });

}


/* 打开导入 */
// const handlerImport = ()=>{
//   importShow.value = !importShow.value
//   // 参数外部重置 可以选择在onMounted里面重置 或者 打开时重置
//   importConfig.taskCode = 'base_client_import'
// }


/* 导入成功后事件 */
// const importSuccess = ()=>{
//   importShow.value =!importShow.value
//   getList()
// }


/* 导出事件 */
const handlerExport = () =>{
  const now = new Date()
  const year = now.getFullYear()
  const month = String(now.getMonth() + 1).padStart(2, '0')
  const day = String(now.getDate()).padStart(2, '0')
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  const seconds = String(now.getSeconds()).padStart(2, '0')
  const timestamp = `${year}${month}${day}${hours}${minutes}${seconds}`
  doExport( `付款通知表头${timestamp}.xlsx`,totalColumns)
}



/* 自定义设置 */
/* 显示列数据 */
const showColumns =  ref([])

/* 唯一键 */
const tableKey = ref('')

tableKey.value = window.$vueApp ? window.majesty.router.currentRoute.value.path : useRoute().path
const originalColumns = ref()




/* 自定义显示列初始化操作 */
const initCustomColumn = () => {
  // 这里是拷贝是属于
  let tempColumns = deepClone(totalColumns.value)
  let dealColumns = []
  // 使用map遍历会丢失customRender方法，所以使用forEach
  tempColumns.map((item) => {
    let newObj = Object.assign({}, item);
    newObj["visible"] = true;
    // 需要将customRender 方法追加到新对象中
    if (item.customRender) {
      newObj["customRender"] = item.customRender;
    }
    dealColumns.push(newObj);
  });
  //原始列信息
  originalColumns.value = dealColumns;
}



/* 选中visible为true的数据进行显示 */
const customColumnChange = (settingColumns)  => {
  totalColumns.value = settingColumns.filter((item) => item.visible === true);
  showColumns.value = [...totalColumns.value]
  //  深拷贝之前实现 丢失了方法，所以修改了utils的deepClone方法，这里不要了
  // showColumns.value.map((item) => {
  //   let temp = totalColumns.value.find((tempItem) => tempItem.key === item.key);
  //   if (temp && temp.customRender) {
  //     item.customRender = temp.customRender;
  //   }
  // });
}





/* 监控 dataSourceList */
watch(dataSourceList, (newValue, oldValue) => {
  showColumns.value = [...totalColumns.value];
  // 将showColumns的数据属性 和 初始属性进行比对，如果初始属性存在customRender 方法，追加到showColumns中
},{deep:true})






</script>

<style lang="less" scoped>


</style>
