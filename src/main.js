// import appStore from './store'
// import appRouter from './router'
//
// window.majesty && window.majesty.util.registerApp('example', {appRouter, appStore});

// 导入样式
import '@/common.less';





import STable from '@surely-vue/table';
import '@surely-vue/table/dist/index.less';
import { setLicenseKey } from '@surely-vue/table';
setLicenseKey('3f5885673e7e749e33097498e7554ea5T1JERVI6MTAwMDg3LEVYUElSWT0xNzYxOTU1MjAwMDAwLERPTUFJTj1zdXJlbHkuY29vbCxVTFRJTUFURT0xLEtFWVZFUlNJT049MQ==');
import  Vue, {createApp} from 'vue'
import App from './App.vue'
import appRouter from './router'
import * as VueRouter from 'vue-router'
import Antd from 'ant-design-vue';
import 'ant-design-vue/dist/reset.css';
import pcode from './utils/XdoPcode'
import { axios } from '@/api/request'
import store from '@/store'
import vSelect from 'vue-select'
import 'vue-select/dist/vue-select.css'
import { GlobalIcon } from './components/icon/index'


import {
  getAction,
  deleteAction,
  putAction,
  postAction,
  httpAction,
  downloadFile,
  uploadAction,
} from '@/api/manage'
import {localeContent} from "@/view/utils/commonUtil";
import i18n from "@/assets/lang";


const router = VueRouter.createRouter({
  history: VueRouter.createWebHistory(process.env.BASE_URL),
  scrollBehavior: () => ({
    top: 0,
  }),
  routes: [
    ...appRouter
  ]
})



const app = createApp(App)
  .use(STable)
  .use(store)
  .use(i18n)
  .use(Antd).use(router).component('v-select', vSelect);

router.isReady().then(() => {
  app.mount('#app');
});

// 全局注册图标组件
app.component('GlobalIcon', GlobalIcon)

window.majesty = Object.assign(window.majesty || {}, {
  Vue,
  router,
  store,
  httpUtil: {
    getAction: getAction,
    deleteAction: deleteAction,
    putAction: putAction,
    postAction: postAction,
    httpAction: httpAction,
    downloadFile: downloadFile,
    uploadAction: uploadAction
  }
})

pcode.setupApi(axios)
pcode.initDict()

// window.$vueApp = app
app.mixin({
  data() {
    return {
      pcode: pcode.codeEnum,
    }
  },
  methods: {
    xdoi18nGet(key, bizType) {
      return localeContent(key)
    },
    pcodeGet(type, key) {
      return pcode.get(type, key)
    },
    pcodeRemote(type, key) {
      return pcode.remote(type, key)
    },
    pcodeList(type) {
      const promise = pcode.getList(type)
      return promise
    },
    pcodeGetByFormat(type, key, format) {
      if (!key) {
        return ''
      }
      if (!format) {
        return key
      }
      const value = pcode.get(type, key)

      if (value && value.indexOf(key) === -1) {
        return format
          .replace(/\$\{key\}/g, !key ? '' : key)
          .replace(/\$\{value\}/g, !value ? '' : value)
      }
      return key
    },
  },
})

