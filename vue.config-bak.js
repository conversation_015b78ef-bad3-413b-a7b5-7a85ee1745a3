const namespace = 'example'

module.exports = {
  runtimeCompiler: true,
  lintOnSave: undefined,
  productionSourceMap: false,
  // css: {
  //   extract: false
  // },
  configureWebpack: config => {
    config.entry = {
      [namespace]: ["./src/main.js"]
    }
    return {
      // externals: { vue: "Vue" },
      output: {
        library: namespace,
        filename: process.env.NODE_ENV === 'production'
          ? `biz/${namespace}/js/[id].[contenthash:4].js`
          : '[id].js',
        chunkFilename: `biz/${namespace}/js/${namespace}.vendors.[contenthash:4].js`,
      },
      optimization: {
        runtimeChunk: false, // 依赖处理与bundle合并
        splitChunks: false
      }
    }
  },
  devServer: {
    port: 8077,
    proxy: {
      '/api/pcode':{
        target: 'http://192.168.10.134:32080/pcode',
        changeOrigin: true,
        pathRewrite: { "^/api/pcode": "pcode" },
      },
    }
  },
  // css.extract: false：
  // 这是核心配置，设置为 false 后，Vue CLI 会将所有 CSS 内联到 JS 文件中，而不是提取为单独的 CSS 文件。
  // 默认情况下，css.extract 在生产环境中为 true，会将 CSS 提取为单独的文件。
  // optimization.splitChunks: false：
  // 禁用代码分割，确保所有代码（包括 CSS）被打包到主 JS 文件中。
  // optimization.runtimeChunk: false：
  // 禁用运行时 chunk，确保运行时逻辑与主 JS 文件合并。
  css: {
    loaderOptions: {
      less: {
        lessOptions: {
          modifyVars: {
            /* less 变量覆盖，用于自定义 ant design 主题 */
            //"primary-color": "#2F54EB",
            "primary-color":"#1890FF",
            "link-color": "#1890FF",
            "border-radius-base": "2px"
          },
          javascriptEnabled: true
        }
      }
    }
  }
}

