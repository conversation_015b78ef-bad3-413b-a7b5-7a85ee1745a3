# external-system-demo

外部系统示例,浮云通过动态引入外部系统编译后的js来显示页面

## 安装依赖
```
npm install
```

## 本地模式开发注意事项
1. 修改vue.config.js文件,注释掉这一行: `//externals: { vue: "Vue" }`,
2. 修改main.js文件,将最上面的三行注释
```
// import appStore from './store'
// import appRouter from './router'
//
// window.majesty && window.majesty.util.registerApp('example', {appRouter, appStore});
```
3. 启动,并在浏览器中通过路由页面浏览页面
```
npm run serve
```
## 打包挂载注意事项
1. 修改vue.config.js文件,放开这一行注释: `externals: { vue: "Vue" }`,
2. 修改main.js文件,将最上面的三行注释,放开,其余全部注释
3. 打包`npm run build`
